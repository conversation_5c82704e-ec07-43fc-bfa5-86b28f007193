// nolint
package utils

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/mysql"
)

// TestCheckInstanceIdIsNamespace 测试CheckInstanceIdIsNamespace方法
func TestCheckInstanceIdIsNamespace(t *testing.T) {
	tests := []struct {
		name         string
		instanceId   string
		setupMock    func(mock sqlmock.Sqlmock)
		expectedBool bool
		expectedErr  bool
		description  string
	}{
		{
			name:       "TestCheckInstanceIdIsNamespace_GivenExistingInstanceId_WhenCheckIsNamespace_ThenReturnTrue",
			instanceId: "test-namespace-1",
			setupMock: func(mock sqlmock.Sqlmock) {
				// Mock返回存在的记录
				rows := sqlmock.NewRows([]string{
					"f_account_id", "f_project_id", "f_bce_account", "f_multicluster_enable",
					"f_multiwrite_enable", "f_cprom_disable", "f_multiwrite_url", "f_multiread_url",
					"f_new_instance_id", "f_namespace", "f_default_expire_time", "f_delete_time",
					"f_create_time", "f_update_time",
				}).AddRow(
					1, 1001, "test-account", 1, 1, 1,
					"http://write.test.com", "http://read.test.com",
					"instance-123", "test-namespace-1", "30d",
					time.Now(), time.Now(), time.Now(),
				)
				mock.ExpectQuery("SELECT (.+) FROM `t_account_manage`").
					WithArgs("test-namespace-1").
					WillReturnRows(rows)
			},
			expectedBool: true,
			expectedErr:  false,
			description:  "实例ID存在于namespace中时返回true",
		},
		{
			name:       "TestCheckInstanceIdIsNamespace_GivenNonExistingInstanceId_WhenCheckIsNamespace_ThenReturnFalse",
			instanceId: "non-existing-namespace",
			setupMock: func(mock sqlmock.Sqlmock) {
				// Mock返回空记录
				rows := sqlmock.NewRows([]string{
					"f_account_id", "f_project_id", "f_bce_account", "f_multicluster_enable",
					"f_multiwrite_enable", "f_cprom_disable", "f_multiwrite_url", "f_multiread_url",
					"f_new_instance_id", "f_namespace", "f_default_expire_time", "f_delete_time",
					"f_create_time", "f_update_time",
				})
				mock.ExpectQuery("SELECT (.+) FROM `t_account_manage`").
					WithArgs("non-existing-namespace").
					WillReturnRows(rows)
			},
			expectedBool: false,
			expectedErr:  false,
			description:  "实例ID不存在于namespace中时返回false",
		},
		{
			name:       "TestCheckInstanceIdIsNamespace_GivenMultipleRecords_WhenCheckIsNamespace_ThenReturnTrue",
			instanceId: "multi-namespace",
			setupMock: func(mock sqlmock.Sqlmock) {
				// Mock返回多条记录
				rows := sqlmock.NewRows([]string{
					"f_account_id", "f_project_id", "f_bce_account", "f_multicluster_enable",
					"f_multiwrite_enable", "f_cprom_disable", "f_multiwrite_url", "f_multiread_url",
					"f_new_instance_id", "f_namespace", "f_default_expire_time", "f_delete_time",
					"f_create_time", "f_update_time",
				}).AddRow(
					1, 1001, "test-account-1", 1, 1, 1,
					"http://write.test1.com", "http://read.test1.com",
					"instance-123", "multi-namespace", "30d",
					time.Now(), time.Now(), time.Now(),
				).AddRow(
					2, 1002, "test-account-2", 1, 1, 1,
					"http://write.test2.com", "http://read.test2.com",
					"instance-456", "multi-namespace", "30d",
					time.Now(), time.Now(), time.Now(),
				)
				mock.ExpectQuery("SELECT (.+) FROM `t_account_manage`").
					WithArgs("multi-namespace").
					WillReturnRows(rows)
			},
			expectedBool: true,
			expectedErr:  false,
			description:  "多条记录匹配时返回true",
		},
		{
			name:       "TestCheckInstanceIdIsNamespace_GivenDatabaseError_WhenCheckIsNamespace_ThenReturnError",
			instanceId: "error-namespace",
			setupMock: func(mock sqlmock.Sqlmock) {
				// Mock返回数据库错误
				mock.ExpectQuery("SELECT (.+) FROM `t_account_manage`").
					WithArgs("error-namespace").
					WillReturnError(errors.New("database connection failed"))
			},
			expectedBool: false,
			expectedErr:  true,
			description:  "数据库查询失败时返回错误",
		},
		{
			name:       "TestCheckInstanceIdIsNamespace_GivenEmptyInstanceId_WhenCheckIsNamespace_ThenReturnFalse",
			instanceId: "",
			setupMock: func(mock sqlmock.Sqlmock) {
				// Mock返回空记录
				rows := sqlmock.NewRows([]string{
					"f_account_id", "f_project_id", "f_bce_account", "f_multicluster_enable",
					"f_multiwrite_enable", "f_cprom_disable", "f_multiwrite_url", "f_multiread_url",
					"f_new_instance_id", "f_namespace", "f_default_expire_time", "f_delete_time",
					"f_create_time", "f_update_time",
				})
				mock.ExpectQuery("SELECT (.+) FROM `t_account_manage`").
					WithArgs("").
					WillReturnRows(rows)
			},
			expectedBool: false,
			expectedErr:  false,
			description:  "空实例ID时返回false",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建mock数据库
			db, mock, err := sqlmock.New()
			if err != nil {
				t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
			}
			defer db.Close()

			// 设置mysql连接
			originalConn := mysql.Conn
			mysql.Conn, err = gorm.Open("mysql", db)
			if err != nil {
				t.Fatalf("an error '%s' was not expected when opening gorm connection", err)
			}
			defer func() {
				mysql.Conn = originalConn
			}()

			// 设置mock期望
			tt.setupMock(mock)

			// 执行测试
			ctx := context.Background()
			result, err := CheckInstanceIdIsNamespace(ctx, tt.instanceId)

			// 验证结果
			if tt.expectedErr {
				assert.Error(t, err, tt.description)
			} else {
				assert.NoError(t, err, tt.description)
				assert.Equal(t, tt.expectedBool, result, tt.description)
			}

			// 验证所有mock期望都被满足
			if err := mock.ExpectationsWereMet(); err != nil {
				t.Errorf("mock expectations were not met: %s", err)
			}
		})
	}
}
