// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/10/29 19:30:00, by <EMAIL>, create
*/
/*
定义 CCE Types 和 LogicBCC Type 转换
未直接使用 LogicBCC Type 是因为 BCC OpenAPI 类型和 LogicBCC 类型不一致, CCE 以 OpenAPI 为准
*/

package ccetypes

import (
	"context"
	"fmt"
	"strings"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bccimage"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/eip"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/internalvpc"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/logicalbcc"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/logicbcc"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/logicimage"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/logger"
)

// TransOpenBCCImageTypeToLogicBCC - bccimage.ImageType 转成 logicimage.ImageType
func TransOpenBCCImageTypeToLogicBCC(ctx context.Context,
	imageType bccimage.ImageType) (logicimage.ImageType, error) {
	switch imageType {
	case bccimage.ImageTypeSystem:
		return logicimage.ImageTypeSystem, nil
	case bccimage.ImageTypeCustom:
		return logicimage.ImageTypeCustom, nil
	case bccimage.ImageTypeSharing:
		return logicimage.ImageTypeSharing, nil
	case bccimage.ImageTypeGPUSystem:
		return logicimage.ImageTypeGPUSystem, nil
	case bccimage.ImageTypeGPUCustom:
		return logicimage.ImageTypeGPUCustom, nil
	case bccimage.ImageTypeBBCSystem:
		return logicimage.ImageTypeBBCSystem, nil
	case bccimage.ImageTypeBBCCustom:
		return logicimage.ImageTypeBBCCustom, nil
	default:
		return "", fmt.Errorf("Unsupported CCE ImageType: %s", imageType)
	}
}

// TransLogicBCCImageTypeToOpenBCC - logicimage.ImageType 转成 bccimage.ImageType
func TransLogicBCCImageTypeToOpenBCC(ctx context.Context,
	imageType logicimage.ImageType) (bccimage.ImageType, error) {
	switch imageType {
	case logicimage.ImageTypeSystem:
		return bccimage.ImageTypeSystem, nil
	case logicimage.ImageTypeCustom:
		return bccimage.ImageTypeCustom, nil
	case logicimage.ImageTypeSharing:
		return bccimage.ImageTypeSharing, nil
	case logicimage.ImageTypeGPUSystem:
		return bccimage.ImageTypeGPUSystem, nil
	case logicimage.ImageTypeGPUCustom:
		return bccimage.ImageTypeGPUCustom, nil
	case logicimage.ImageTypeBBCSystem:
		return bccimage.ImageTypeBBCSystem, nil
	case logicimage.ImageTypeBBCCustom:
		return bccimage.ImageTypeBBCCustom, nil
	default:
		return "", fmt.Errorf("Unsupported LogicImage ImageType: %s", imageType)
	}
}

// TransLogicBCCStorageTypeToOpenBCC - logicbcc.StorageType 转成 bcc.StorageType
func TransLogicBCCStorageTypeToOpenBCC(ctx context.Context, storageType logicbcc.StorageType) (bcc.StorageType, error) {
	switch storageType {
	case logicbcc.StorageTypeSTD1:
		return bcc.StorageTypeSTD1, nil
	case logicbcc.StorageTypeHP1:
		return bcc.StorageTypeHP1, nil
	case logicbcc.StorageTypeCloudHP1:
		return bcc.StorageTypeCloudHP1, nil
	case logicbcc.StorageTypeHDD:
		return bcc.StorageTypeHDD, nil
	case logicbcc.StorageTypeLocal:
		return bcc.StorageTypeLocal, nil
	case logicbcc.StorageTypeDCCSATA:
		return bcc.StorageTypeDCCSATA, nil
	case logicbcc.StorageTypeDCCSSD:
		return bcc.StorageTypeDCCSSD, nil
	default:
		return "", fmt.Errorf("Unsupported LogicBCC storageType: %s", storageType)
	}
}

// TransOpenBCCStorageTypeToLogicBCC - bcc.StorageType 转成 logicbcc.StorageType
func TransOpenBCCStorageTypeToLogicBCC(ctx context.Context, storageType bcc.StorageType) (logicbcc.StorageType, error) {
	switch storageType {
	case bcc.StorageTypeSTD1:
		return logicbcc.StorageTypeSTD1, nil
	case bcc.StorageTypeHP1:
		return logicbcc.StorageTypeHP1, nil
	case bcc.StorageTypeCloudHP1:
		return logicbcc.StorageTypeCloudHP1, nil
	case bcc.StorageTypeHDD:
		return logicbcc.StorageTypeHDD, nil
	case bcc.StorageTypeLocal:
		return logicbcc.StorageTypeLocal, nil
	case bcc.StorageTypeDCCSATA:
		return logicbcc.StorageTypeDCCSATA, nil
	case bcc.StorageTypeDCCSSD:
		return logicbcc.StorageTypeDCCSSD, nil
	case bcc.StorageTypeEnhancedSSD:
		return logicbcc.StorageTypeEnhancedSSD, nil
	default:
		return "", fmt.Errorf("Unsupported OpenBCC StorageType: %s", storageType)
	}
}

// TransLogicBCCStatusToLogicalBCCStatus - logicbcc.Status 转成 logicalbcc.Status
func TransLogicBCCStatusToLogicalBCCStatus(ctx context.Context, status logicbcc.ServerStatus) (logicalbcc.ServerStatus, error) {
	switch strings.ToUpper(string(status)) {
	case string(logicbcc.ServerStatusActive):
		return logicalbcc.ServerStatusActive, nil
	case string(logicbcc.ServerStatusBuild):
		return logicalbcc.ServerStatusBuild, nil
	case string(logicbcc.ServerStatusRebuild):
		return logicalbcc.ServerStatusRebuild, nil
	case string(logicbcc.ServerStatusDeleted):
		return logicalbcc.ServerStatusDeleted, nil
	case string(logicbcc.ServerStatusSnapshot):
		return logicalbcc.ServerStatusSnapshot, nil
	case string(logicbcc.ServerStatusDeleteSnapshot):
		return logicalbcc.ServerStatusDeleteSnapshot, nil
	case string(logicbcc.ServerStatusVolumeResize):
		return logicalbcc.ServerStatusVolumeResize, nil
	case string(logicbcc.ServerStatusError):
		return logicalbcc.ServerStatusError, nil
	case string(logicbcc.ServerStatusExpired):
		return logicalbcc.ServerStatusExpired, nil
	case string(logicbcc.ServerStatusReboot):
		return logicalbcc.ServerStatusReboot, nil
	case string(logicbcc.ServerStatusRecharge):
		return logicalbcc.ServerStatusRecharge, nil
	case string(logicbcc.ServerStatusShutoff):
		return logicalbcc.ServerStatusShutoff, nil
	case string(logicbcc.ServerStatusStopped):
		return logicalbcc.ServerStatusStopped, nil
	case string(logicbcc.ServerStatusUnknown):
		return logicalbcc.ServerStatusUnknown, nil
	default:
		return "", fmt.Errorf("Unsupported LogicBCC ServerStatus: %s", status)
	}
}

// TransLogicalBCCStatusToLogicBCCStatus - logicalbcc.Status 转成 logicbcc.Status
func TransLogicalBCCStatusToLogicBCCStatus(ctx context.Context, status logicalbcc.ServerStatus) (logicbcc.ServerStatus, error) {
	// 接口返回大小写不一致，统一转成大写作匹配
	switch strings.ToUpper(string(status)) {
	case string(logicalbcc.ServerStatusActive):
		return logicbcc.ServerStatusActive, nil
	case string(logicalbcc.ServerStatusBuild):
		return logicbcc.ServerStatusBuild, nil
	case string(logicalbcc.ServerStatusRebuild):
		return logicbcc.ServerStatusRebuild, nil
	case string(logicalbcc.ServerStatusDeleted):
		return logicbcc.ServerStatusDeleted, nil
	case string(logicalbcc.ServerStatusSnapshot):
		return logicbcc.ServerStatusSnapshot, nil
	case string(logicalbcc.ServerStatusDeleteSnapshot):
		return logicbcc.ServerStatusDeleteSnapshot, nil
	case string(logicalbcc.ServerStatusVolumeResize):
		return logicbcc.ServerStatusVolumeResize, nil
	case string(logicalbcc.ServerStatusError):
		return logicbcc.ServerStatusError, nil
	case string(logicalbcc.ServerStatusExpired):
		return logicbcc.ServerStatusExpired, nil
	case string(logicalbcc.ServerStatusReboot):
		return logicbcc.ServerStatusReboot, nil
	case string(logicalbcc.ServerStatusRecharge):
		return logicbcc.ServerStatusRecharge, nil
	case string(logicalbcc.ServerStatusShutoff):
		return logicbcc.ServerStatusShutoff, nil
	case string(logicalbcc.ServerStatusStopped):
		return logicbcc.ServerStatusStopped, nil
	case string(logicalbcc.ServerStatusUnknown):
		return logicbcc.ServerStatusUnknown, nil
	default:
		return "", fmt.Errorf("Unsupported LogicalBCC ServerStatus: %s", status)
	}
}

// TransOpenBCCInstanceTypeToLogicBCC - bcc.InstanceType 转成 logicbcc.InstanceType
func TransOpenBCCInstanceTypeToLogicBCC(ctx context.Context, instanceType bcc.InstanceType) (logicbcc.InstanceType, error) {
	switch instanceType {
	case bcc.InstanceTypeN1:
		return logicbcc.InstanceTypeN1, nil
	case bcc.InstanceTypeN2:
		return logicbcc.InstanceTypeN2, nil
	case bcc.InstanceTypeN3:
		return logicbcc.InstanceTypeN3, nil
	case bcc.InstanceTypeN4:
		return logicbcc.InstanceTypeN4, nil
	case bcc.InstanceTypeN5:
		return logicbcc.InstanceTypeN5, nil
	case bcc.InstanceTypeC1:
		return logicbcc.InstanceTypeC1, nil
	case bcc.InstanceTypeC2:
		return logicbcc.InstanceTypeC2, nil
	case bcc.InstanceTypeS1:
		return logicbcc.InstanceTypeS1, nil
	case bcc.InstanceTypeG1:
		return logicbcc.InstanceTypeG1, nil
	case bcc.InstanceTypeF1:
		return logicbcc.InstanceTypeF1, nil
	case bcc.InstanceTypeDCC:
		return logicbcc.InstanceTypeDCC, nil
	case bcc.InstanceTypeBBC:
		return logicbcc.InstanceTypeBBC, nil
	case bcc.InstanceTypeBBCGPU:
		return logicbcc.InstanceTypeBBCGPU, nil
	default:
		logger.Warnf(ctx, "unsupported OpenBCC InstanceType %s, just pass through", instanceType)

		return logicbcc.InstanceType(instanceType), nil
	}
}

// TransLogicBCCInstanceTypeToOpenBCC - logicbcc.InstanceType 转成 bcc.InstanceType
func TransLogicBCCInstanceTypeToOpenBCC(ctx context.Context, instanceType logicbcc.InstanceType) (bcc.InstanceType, error) {
	switch instanceType {
	case logicbcc.InstanceTypeN1:
		return bcc.InstanceTypeN1, nil
	case logicbcc.InstanceTypeN2:
		return bcc.InstanceTypeN2, nil
	case logicbcc.InstanceTypeN3:
		return bcc.InstanceTypeN3, nil
	case logicbcc.InstanceTypeN4:
		return bcc.InstanceTypeN4, nil
	case logicbcc.InstanceTypeN5:
		return bcc.InstanceTypeN5, nil
	case logicbcc.InstanceTypeC1:
		return bcc.InstanceTypeC1, nil
	case logicbcc.InstanceTypeC2:
		return bcc.InstanceTypeC2, nil
	case logicbcc.InstanceTypeS1:
		return bcc.InstanceTypeS1, nil
	case logicbcc.InstanceTypeG1:
		return bcc.InstanceTypeG1, nil
	case logicbcc.InstanceTypeF1:
		return bcc.InstanceTypeF1, nil
	case logicbcc.InstanceTypeDCC:
		return bcc.InstanceTypeDCC, nil
	case logicbcc.InstanceTypeBBC:
		return bcc.InstanceTypeBBC, nil
	case logicbcc.InstanceTypeBBCGPU:
		return bcc.InstanceTypeBBCGPU, nil
	default:
		logger.Errorf(ctx, "unsupported LogicBCC InstanceType: %s", instanceType)
		return "", nil
	}
}

// TransLogicBCCGPUTypeToOpenBCC - logicbcc.GPUType 转成 bcc.GPUType
func TransLogicBCCGPUTypeToOpenBCC(ctx context.Context, gpuType logicbcc.GPUType) (bcc.GPUType, error) {
	switch gpuType {
	case logicbcc.GPUTypeV100_16:
		return bcc.GPUTypeV100_16, nil
	case logicbcc.GPUTypeV100_32:
		return bcc.GPUTypeV100_32, nil
	case logicbcc.GPUTypeP40:
		return bcc.GPUTypeP40, nil
	case logicbcc.GPUTypeP4:
		return bcc.GPUTypeP4, nil
	case logicbcc.GPUTypeK40:
		return bcc.GPUTypeK40, nil
	default:
		return "", fmt.Errorf("unsupported LogicBCC GPUType: %s", gpuType)
	}
}

// TransOpenBCCGPUTypeToLogicBCC - bcc.GPUType 转成 logicbcc.GPUType
func TransOpenBCCGPUTypeToLogicBCC(ctx context.Context, gpuType bcc.GPUType) (logicbcc.GPUType, error) {
	switch gpuType {
	case bcc.GPUTypeV100_16:
		return logicbcc.GPUTypeV100_16, nil
	case bcc.GPUTypeV100_32:
		return logicbcc.GPUTypeV100_32, nil
	case bcc.GPUTypeP40:
		return logicbcc.GPUTypeP40, nil
	case bcc.GPUTypeP4:
		return logicbcc.GPUTypeP4, nil
	case bcc.GPUTypeK40:
		return logicbcc.GPUTypeK40, nil
	default:
		logger.Infof(ctx, "GPUType %s not supported, just pass it to logicbcc", gpuType)

		return logicbcc.GPUType(gpuType), nil
	}
}

// TransOpenBCCPaymentTimingToLogicBCC - 将 bcc.PaymentTiming 转成 logicbcc.ProductType
func TransOpenBCCPaymentTimingToLogicBCC(ctx context.Context, paymentTiming bcc.PaymentTiming) (logicbcc.ProductType, error) {
	switch paymentTiming {
	case bcc.PaymentTimingPrepaid:
		return logicbcc.ProductTypePrepay, nil
	case bcc.PaymentTimingPostpaid:
		return logicbcc.ProductTypePostpay, nil
	default:
		return "", fmt.Errorf("unsupported OpenBCC PaymentTiming: %s", paymentTiming)
	}
}

// TransLogicBCCPaymentTimingToOpenBCC - 将 logicbcc.ProductType 转成 bcc.PaymentTiming
func TransLogicBCCPaymentTimingToOpenBCC(ctx context.Context, paymentTiming logicbcc.ProductType) (bcc.PaymentTiming, error) {
	switch paymentTiming {
	case logicbcc.ProductTypePrepay:
		return bcc.PaymentTimingPrepaid, nil
	case logicbcc.ProductTypePostpay:
		return bcc.PaymentTimingPostpaid, nil
	default:
		return "", fmt.Errorf("unsupported LogicBCC PaymentTiming: %s", paymentTiming)
	}
}

// TransOpenEIPBillingMethodToLogicBCC - 将 eip.BillingMethod 转成 logicbcc.ProductType
func TransOpenEIPBillingMethodToLogicBCC(ctx context.Context, billingMethod eip.BillingMethod) (logicbcc.EIPBillingMethod, error) {
	switch billingMethod {
	case eip.BillingMethodByBandwidth:
		return logicbcc.EIPBillingMethodBandwidth, nil
	case eip.BillingMethodByTraffic:
		return logicbcc.EIPBillingMethodNetTraffic, nil
	case eip.PeakBandwidthPercent95A:
		return logicbcc.PeakBandwidthPercent95A, nil
	default:
		return "", fmt.Errorf("unsupported OpenEIP BillingMethod: %s", billingMethod)
	}
}

func TransAllTypeToOpenBCCEIPType(ctx context.Context, chargeType string) string {
	switch chargeType {
	case string(eip.BillingMethodByBandwidth):
		return string(bcc.InternetChargeBandwidth)
	case string(eip.BillingMethodByTraffic):
		return string(bcc.InternetChargeTraffic)
	case string(bcc.InternetChargeBandwidth):
		return string(bcc.InternetChargeBandwidth)
	case string(bcc.InternetChargeTraffic):
		return string(bcc.InternetChargeTraffic)
	default:
		logger.Errorf(ctx, "unexpected eip chargeType: %s", chargeType)
		return chargeType
	}
}

// TransZoneNameToAvailableZone - 将 zoneName 转成 availableZone
func TransZoneNameToAvailableZone(ctx context.Context, zoneName string) (internalvpc.AvailableZone, error) {
	arr := strings.Split(zoneName, "-")
	if len(arr) != 3 {
		return "", fmt.Errorf("unrecognized zoneName format: %s", zoneName)
	}

	zone := arr[2]
	if zone == "a" {
		return internalvpc.ZoneA, nil
	}
	if zone == "b" {
		return internalvpc.ZoneB, nil
	}
	if zone == "c" {
		return internalvpc.ZoneC, nil
	}
	if zone == "d" {
		return internalvpc.ZoneD, nil
	}
	if zone == "e" {
		return internalvpc.ZoneE, nil
	}
	if zone == "f" {
		return internalvpc.ZoneF, nil
	}

	return "", fmt.Errorf("unrecognized zoneName: %s", zoneName)
}

// TransAvailableZoneToZoneName - 将 availableZone 转成 zoneName
func TransAvailableZoneToZoneName(ctx context.Context, country, region, availableZone string) string {
	az := internalvpc.AvailableZone(availableZone)
	zonePrefix := country + "-" + region + "-"
	switch az {
	case internalvpc.ZoneA:
		return zonePrefix + "a"
	case internalvpc.ZoneB:
		return zonePrefix + "b"
	case internalvpc.ZoneC:
		return zonePrefix + "c"
	case internalvpc.ZoneD:
		return zonePrefix + "d"
	case internalvpc.ZoneE:
		return zonePrefix + "e"
	case internalvpc.ZoneF:
		return zonePrefix + "f"
	default:
		logger.Errorf(ctx, "unknow available zone: %s", availableZone)
		return availableZone
	}
}
