// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/10/29 19:30:00, by <EMAIL>, create
*/
/*
定义 CCE Types 和 LogicBCC Type 转换
未直接使用 LogicBCC Type 是因为 BCC OpenAPI 类型和 LogicBCC 类型不一致, CCE 以 OpenAPI 为准
*/

package ccetypes

import (
	"context"
	"reflect"
	"testing"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bccimage"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/eip"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/logicalbcc"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/logicbcc"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/logicimage"
)

func TestTransOpenBCCInstanceTypeToLogicBCC(t *testing.T) {
	type args struct {
		ctx          context.Context
		instanceType bcc.InstanceType
	}
	tests := []struct {
		name    string
		args    args
		want    logicbcc.InstanceType
		wantStr string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "InstanceTypeN1",
			args: args{
				ctx:          context.TODO(),
				instanceType: bcc.InstanceTypeN1,
			},
			want:    logicbcc.InstanceTypeN1,
			wantStr: "0",
			wantErr: false,
		},
		{
			name: "InstanceTypeN2",
			args: args{
				ctx:          context.TODO(),
				instanceType: bcc.InstanceTypeN2,
			},
			want:    logicbcc.InstanceTypeN2,
			wantStr: "7",
			wantErr: false,
		},
		{
			name: "InstanceTypeN3",
			args: args{
				ctx:          context.TODO(),
				instanceType: bcc.InstanceTypeN3,
			},
			want:    logicbcc.InstanceTypeN3,
			wantStr: "10",
			wantErr: false,
		},
		{
			name: "InstanceTypeN4",
			args: args{
				ctx:          context.TODO(),
				instanceType: bcc.InstanceTypeN4,
			},
			want:    logicbcc.InstanceTypeN4,
			wantStr: "12",
			wantErr: false,
		},
		{
			name: "InstanceTypeN5",
			args: args{
				ctx:          context.TODO(),
				instanceType: bcc.InstanceTypeN5,
			},
			want:    logicbcc.InstanceTypeN5,
			wantStr: "13",
			wantErr: false,
		},
		{
			name: "InstanceTypeC1",
			args: args{
				ctx:          context.TODO(),
				instanceType: bcc.InstanceTypeC1,
			},
			want:    logicbcc.InstanceTypeC1,
			wantStr: "4",
			wantErr: false,
		},
		{
			name: "InstanceTypeC2",
			args: args{
				ctx:          context.TODO(),
				instanceType: bcc.InstanceTypeC2,
			},
			want:    logicbcc.InstanceTypeC2,
			wantStr: "11",
			wantErr: false,
		},
		{
			name: "InstanceTypeS1",
			args: args{
				ctx:          context.TODO(),
				instanceType: bcc.InstanceTypeS1,
			},
			want:    logicbcc.InstanceTypeS1,
			wantStr: "5",
			wantErr: false,
		},
		{
			name: "InstanceTypeG1",
			args: args{
				ctx:          context.TODO(),
				instanceType: bcc.InstanceTypeG1,
			},
			want:    logicbcc.InstanceTypeG1,
			wantStr: "9",
			wantErr: false,
		},
		{
			name: "InstanceTypeF1",
			args: args{
				ctx:          context.TODO(),
				instanceType: bcc.InstanceTypeF1,
			},
			want:    logicbcc.InstanceTypeF1,
			wantStr: "8",
			wantErr: false,
		},
		{
			name: "InstanceTypeDCC",
			args: args{
				ctx:          context.TODO(),
				instanceType: bcc.InstanceTypeDCC,
			},
			want:    logicbcc.InstanceTypeDCC,
			wantStr: "1",
			wantErr: false,
		},
		{
			name: "InstanceTypeBBC",
			args: args{
				ctx:          context.TODO(),
				instanceType: bcc.InstanceTypeBBC,
			},
			want:    logicbcc.InstanceTypeBBC,
			wantStr: "2",
			wantErr: false,
		},
		{
			name: "InstanceTypeBBCGPU",
			args: args{
				ctx:          context.TODO(),
				instanceType: bcc.InstanceTypeBBCGPU,
			},
			want:    logicbcc.InstanceTypeBBCGPU,
			wantStr: "3",
			wantErr: false,
		},
		{
			name: "不支持的类型",
			args: args{
				ctx:          context.TODO(),
				instanceType: "22",
			},
			want:    logicbcc.InstanceType("22"),
			wantStr: "22",
			wantErr: false,
		},
	}

	if len(tests) != len(SupportedOpenBCCInstanceType)/2+1 {
		t.Errorf("TransOpenBCCInstanceTypeToLogicBCC: len(tests) != len(SupportedInstanceType)+1")
		return
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := TransOpenBCCInstanceTypeToLogicBCC(tt.args.ctx, tt.args.instanceType)
			if err != nil {
				if tt.wantErr == true {
					return
				}

				t.Errorf("TransOpenBCCInstanceTypeToLogicBCC() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr == true {
				t.Errorf("TransOpenBCCInstanceTypeToLogicBCC() failed, wantErr=true got=false")
				return
			}

			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("TransOpenBCCInstanceTypeToLogicBCC() = %v, want %v", got, tt.want)
			}

			if !reflect.DeepEqual(string(got), tt.wantStr) {
				t.Errorf("TransOpenBCCInstanceTypeToLogicBCC() = %v, want %v", got, tt.wantStr)
			}
		})
	}
}

func TestTransLogicBCCInstanceTypeToOpenBCC(t *testing.T) {
	type args struct {
		ctx          context.Context
		instanceType logicbcc.InstanceType
	}
	tests := []struct {
		name    string
		args    args
		want    bcc.InstanceType
		wantStr string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "InstanceTypeN1",
			args: args{
				ctx:          context.TODO(),
				instanceType: logicbcc.InstanceTypeN1,
			},
			want:    bcc.InstanceTypeN1,
			wantStr: "N1",
			wantErr: false,
		},
		{
			name: "InstanceTypeN2",
			args: args{
				ctx:          context.TODO(),
				instanceType: logicbcc.InstanceTypeN2,
			},
			want:    bcc.InstanceTypeN2,
			wantStr: "N2",
			wantErr: false,
		},
		{
			name: "InstanceTypeN3",
			args: args{
				ctx:          context.TODO(),
				instanceType: logicbcc.InstanceTypeN3,
			},
			want:    bcc.InstanceTypeN3,
			wantStr: "N3",
			wantErr: false,
		},
		{
			name: "InstanceTypeN4",
			args: args{
				ctx:          context.TODO(),
				instanceType: logicbcc.InstanceTypeN4,
			},
			want:    bcc.InstanceTypeN4,
			wantStr: "N4",
			wantErr: false,
		},
		{
			name: "InstanceTypeN5",
			args: args{
				ctx:          context.TODO(),
				instanceType: logicbcc.InstanceTypeN5,
			},
			want:    bcc.InstanceTypeN5,
			wantStr: "N5",
			wantErr: false,
		},
		{
			name: "InstanceTypeC1",
			args: args{
				ctx:          context.TODO(),
				instanceType: logicbcc.InstanceTypeC1,
			},
			want:    bcc.InstanceTypeC1,
			wantStr: "C1",
			wantErr: false,
		},
		{
			name: "InstanceTypeC2",
			args: args{
				ctx:          context.TODO(),
				instanceType: logicbcc.InstanceTypeC2,
			},
			want:    bcc.InstanceTypeC2,
			wantStr: "C2",
			wantErr: false,
		},
		{
			name: "InstanceTypeS1",
			args: args{
				ctx:          context.TODO(),
				instanceType: logicbcc.InstanceTypeS1,
			},
			want:    bcc.InstanceTypeS1,
			wantStr: "S1",
			wantErr: false,
		},
		{
			name: "InstanceTypeG1",
			args: args{
				ctx:          context.TODO(),
				instanceType: logicbcc.InstanceTypeG1,
			},
			want:    bcc.InstanceTypeG1,
			wantStr: "G1",
			wantErr: false,
		},
		{
			name: "InstanceTypeF1",
			args: args{
				ctx:          context.TODO(),
				instanceType: logicbcc.InstanceTypeF1,
			},
			want:    bcc.InstanceTypeF1,
			wantStr: "F1",
			wantErr: false,
		},
		{
			name: "InstanceTypeDCC",
			args: args{
				ctx:          context.TODO(),
				instanceType: logicbcc.InstanceTypeDCC,
			},
			want:    bcc.InstanceTypeDCC,
			wantStr: "DCC",
			wantErr: false,
		},
		{
			name: "InstanceTypeBBC",
			args: args{
				ctx:          context.TODO(),
				instanceType: logicbcc.InstanceTypeBBC,
			},
			want:    bcc.InstanceTypeBBC,
			wantStr: "BBC",
			wantErr: false,
		},
		{
			name: "InstanceTypeBBCGPU",
			args: args{
				ctx:          context.TODO(),
				instanceType: logicbcc.InstanceTypeBBCGPU,
			},
			want:    bcc.InstanceTypeBBCGPU,
			wantStr: "BBC_GPU",
			wantErr: false,
		},
		{
			name: "不支持类型",
			args: args{
				ctx:          context.TODO(),
				instanceType: "",
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := TransLogicBCCInstanceTypeToOpenBCC(tt.args.ctx, tt.args.instanceType)
			if err != nil {
				if tt.wantErr == true {
					return
				}

				t.Errorf("TransLogicBCCInstanceTypeToOpenBCC() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr == true {
				t.Errorf("TransLogicBCCInstanceTypeToOpenBCC() failed, wantErr=true got=false")
				return
			}

			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("TransLogicBCCInstanceTypeToOpenBCC() = %v, want %v", got, tt.want)
			}

			if !reflect.DeepEqual(string(got), tt.wantStr) {
				t.Errorf("TransLogicBCCInstanceTypeToOpenBCC() = %v, want %v", got, tt.wantStr)
			}
		})
	}
}

func TestTransOpenBCCImageTypeToLogicBCC(t *testing.T) {
	type args struct {
		ctx       context.Context
		imageType bccimage.ImageType
	}
	tests := []struct {
		name    string
		args    args
		want    logicimage.ImageType
		wantStr string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "ImageTypeSystem",
			args: args{
				ctx:       context.TODO(),
				imageType: bccimage.ImageTypeSystem,
			},
			want:    logicimage.ImageTypeSystem,
			wantStr: "common",
			wantErr: false,
		},
		{
			name: "ImageTypeCustom",
			args: args{
				ctx:       context.TODO(),
				imageType: bccimage.ImageTypeCustom,
			},
			want:    logicimage.ImageTypeCustom,
			wantStr: "custom",
			wantErr: false,
		},
		{
			name: "ImageTypeSharing",
			args: args{
				ctx:       context.TODO(),
				imageType: bccimage.ImageTypeSharing,
			},
			want:    logicimage.ImageTypeSharing,
			wantStr: "sharing",
			wantErr: false,
		},
		{
			name: "ImageTypeGPUSystem",
			args: args{
				ctx:       context.TODO(),
				imageType: bccimage.ImageTypeGPUSystem,
			},
			want:    logicimage.ImageTypeGPUSystem,
			wantStr: "gpuBccImage",
			wantErr: false,
		},
		{
			name: "ImageTypeGPUCustom",
			args: args{
				ctx:       context.TODO(),
				imageType: bccimage.ImageTypeGPUCustom,
			},
			want:    logicimage.ImageTypeGPUCustom,
			wantStr: "gpuBccCustom",
			wantErr: false,
		},
		{
			name: "ImageTypeBBCSystem",
			args: args{
				ctx:       context.TODO(),
				imageType: bccimage.ImageTypeBBCSystem,
			},
			want:    logicimage.ImageTypeBBCSystem,
			wantStr: "bbcCommon",
			wantErr: false,
		},
		{
			name: "ImageTypeBBCCustom",
			args: args{
				ctx:       context.TODO(),
				imageType: bccimage.ImageTypeBBCCustom,
			},
			want:    logicimage.ImageTypeBBCCustom,
			wantStr: "bbcCustom",
			wantErr: false,
		},
		{
			name: "未支持类型",
			args: args{
				ctx:       context.TODO(),
				imageType: "",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := TransOpenBCCImageTypeToLogicBCC(tt.args.ctx, tt.args.imageType)
			if err != nil {
				if tt.wantErr == true {
					return
				}

				t.Errorf("TransOpenBCCImageTypeToLogicBCC() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr == true {
				t.Errorf("TransOpenBCCImageTypeToLogicBCC() failed, wantErr=true got=false")
				return
			}

			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("TransOpenBCCImageTypeToLogicBCC() = %v, want %v", got, tt.want)
			}

			if !reflect.DeepEqual(string(got), tt.wantStr) {
				t.Errorf("TransOpenBCCImageTypeToLogicBCC() = %v, want %v", got, tt.wantStr)
			}
		})
	}
}

func TestTransLogicBCCImageTypeToOpenBCC(t *testing.T) {
	type args struct {
		ctx       context.Context
		imageType logicimage.ImageType
	}

	tests := []struct {
		name    string
		args    args
		want    bccimage.ImageType
		wantStr string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "ImageTypeSystem",
			args: args{
				ctx:       context.TODO(),
				imageType: logicimage.ImageTypeSystem,
			},
			want:    bccimage.ImageTypeSystem,
			wantStr: "System",
			wantErr: false,
		},
		{
			name: "ImageTypeCustom",
			args: args{
				ctx:       context.TODO(),
				imageType: logicimage.ImageTypeCustom,
			},
			want:    bccimage.ImageTypeCustom,
			wantStr: "Custom",
			wantErr: false,
		},
		{
			name: "ImageTypeSharing",
			args: args{
				ctx:       context.TODO(),
				imageType: logicimage.ImageTypeSharing,
			},
			want:    bccimage.ImageTypeSharing,
			wantStr: "Sharing",
			wantErr: false,
		},
		{
			name: "ImageTypeGPUSystem",
			args: args{
				ctx:       context.TODO(),
				imageType: logicimage.ImageTypeGPUSystem,
			},
			want:    bccimage.ImageTypeGPUSystem,
			wantStr: "GpuBccSystem",
			wantErr: false,
		},
		{
			name: "ImageTypeGPUCustom",
			args: args{
				ctx:       context.TODO(),
				imageType: logicimage.ImageTypeGPUCustom,
			},
			want:    bccimage.ImageTypeGPUCustom,
			wantStr: "GpuBccCustom",
			wantErr: false,
		},
		{
			name: "ImageTypeBBCSystem",
			args: args{
				ctx:       context.TODO(),
				imageType: logicimage.ImageTypeBBCSystem,
			},
			want:    bccimage.ImageTypeBBCSystem,
			wantStr: "BbcSystem",
			wantErr: false,
		},
		{
			name: "ImageTypeBBCCustom",
			args: args{
				ctx:       context.TODO(),
				imageType: logicimage.ImageTypeBBCCustom,
			},
			want:    bccimage.ImageTypeBBCCustom,
			wantStr: "BbcCustom",
			wantErr: false,
		},
		{
			name: "未支持类型",
			args: args{
				ctx:       context.TODO(),
				imageType: "",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := TransLogicBCCImageTypeToOpenBCC(tt.args.ctx, tt.args.imageType)
			if err != nil {
				if tt.wantErr == true {
					return
				}

				t.Errorf("TransLogicBCCImageTypeToOpenBCC() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr == true {
				t.Errorf("TransLogicBCCImageTypeToOpenBCC() failed, wantErr=true got=false")
				return
			}

			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("TransLogicBCCImageTypeToOpenBCC() = %v, want %v", got, tt.want)
			}

			if !reflect.DeepEqual(string(got), tt.wantStr) {
				t.Errorf("TransLogicBCCImageTypeToOpenBCC() = %v, want %v", got, tt.wantStr)
			}
		})
	}
}

func TestTransLogicBCCGPUTypeToOpenBCC(t *testing.T) {
	type args struct {
		ctx     context.Context
		gpuType logicbcc.GPUType
	}
	tests := []struct {
		name    string
		args    args
		want    bcc.GPUType
		wantStr string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "GPUTypeV100_16",
			args: args{
				ctx:     context.TODO(),
				gpuType: logicbcc.GPUTypeV100_16,
			},
			want:    bcc.GPUTypeV100_16,
			wantStr: "V100-16",
			wantErr: false,
		},
		{
			name: "GPUTypeV100_32",
			args: args{
				ctx:     context.TODO(),
				gpuType: logicbcc.GPUTypeV100_32,
			},
			want:    bcc.GPUTypeV100_32,
			wantStr: "V100-32",
			wantErr: false,
		}, {
			name: "GPUTypeP40",
			args: args{
				ctx:     context.TODO(),
				gpuType: logicbcc.GPUTypeP40,
			},
			want:    bcc.GPUTypeP40,
			wantStr: "P40",
			wantErr: false,
		}, {
			name: "GPUTypeP4",
			args: args{
				ctx:     context.TODO(),
				gpuType: logicbcc.GPUTypeP4,
			},
			want:    bcc.GPUTypeP4,
			wantStr: "P4",
			wantErr: false,
		}, {
			name: "GPUTypeK40",
			args: args{
				ctx:     context.TODO(),
				gpuType: logicbcc.GPUTypeK40,
			},
			want:    bcc.GPUTypeK40,
			wantStr: "K40",
			wantErr: false,
		}, {
			name: "不支持的 GPU 类型",
			args: args{
				ctx:     context.TODO(),
				gpuType: "",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := TransLogicBCCGPUTypeToOpenBCC(tt.args.ctx, tt.args.gpuType)
			if err != nil {
				if tt.wantErr == true {
					return
				}

				t.Errorf("TransLogicBCCGPUTypeToOpenBCC() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr == true {
				t.Errorf("TransLogicBCCGPUTypeToOpenBCC() failed, wantErr=true got=false")
				return
			}

			if got != tt.want {
				t.Errorf("TransLogicBCCGPUTypeToOpenBCC() = %v, want %v", got, tt.want)
			}

			if !reflect.DeepEqual(string(got), tt.wantStr) {
				t.Errorf("TransLogicBCCGPUTypeToOpenBCC() = %v, want %v", got, tt.wantStr)
			}
		})
	}
}

func TestTransOpenBCCGPUTypeToLogicBCC(t *testing.T) {
	type args struct {
		ctx     context.Context
		gpuType bcc.GPUType
	}
	tests := []struct {
		name    string
		args    args
		want    logicbcc.GPUType
		wantStr string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "GPUTypeV100_16",
			args: args{
				ctx:     context.TODO(),
				gpuType: bcc.GPUTypeV100_16,
			},
			want:    logicbcc.GPUTypeV100_16,
			wantStr: "nTeslaV100-16",
			wantErr: false,
		},
		{
			name: "GPUTypeV100_32",
			args: args{
				ctx:     context.TODO(),
				gpuType: bcc.GPUTypeV100_32,
			},
			want:    logicbcc.GPUTypeV100_32,
			wantStr: "nTeslaV100-32",
			wantErr: false,
		}, {
			name: "GPUTypeP40",
			args: args{
				ctx:     context.TODO(),
				gpuType: bcc.GPUTypeP40,
			},
			want:    logicbcc.GPUTypeP40,
			wantStr: "nTeslaP40",
			wantErr: false,
		}, {
			name: "GPUTypeP4",
			args: args{
				ctx:     context.TODO(),
				gpuType: bcc.GPUTypeP4,
			},
			want:    logicbcc.GPUTypeP4,
			wantStr: "nTeslaP4",
			wantErr: false,
		}, {
			name: "GPUTypeK40",
			args: args{
				ctx:     context.TODO(),
				gpuType: bcc.GPUTypeK40,
			},
			want:    logicbcc.GPUTypeK40,
			wantStr: "nTeslaK40",
			wantErr: false,
		}, {
			name: "不支持的 GPU 类型",
			args: args{
				ctx:     context.TODO(),
				gpuType: "chenhuan",
			},
			want:    logicbcc.GPUType("chenhuan"),
			wantStr: "chenhuan",
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := TransOpenBCCGPUTypeToLogicBCC(tt.args.ctx, tt.args.gpuType)
			if err != nil {
				if tt.wantErr == true {
					return
				}

				t.Errorf("TransOpenBCCGPUTypeToLogicBCC() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr == true {
				t.Errorf("TransOpenBCCGPUTypeToLogicBCC() failed, wantErr=true got=false")
				return
			}

			if got != tt.want {
				t.Errorf("TransOpenBCCGPUTypeToLogicBCC() = %v, want %v", got, tt.want)
			}

			if !reflect.DeepEqual(string(got), tt.wantStr) {
				t.Errorf("TransOpenBCCGPUTypeToLogicBCC() = %v, want %v", got, tt.wantStr)
			}
		})
	}
}

func TestTransLogicBCCStorageTypeToOpenBCC(t *testing.T) {
	type args struct {
		ctx         context.Context
		storageType logicbcc.StorageType
	}
	tests := []struct {
		name    string
		args    args
		want    bcc.StorageType
		wantStr string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "StorageTypeSTD1",
			args: args{
				ctx:         context.TODO(),
				storageType: logicbcc.StorageTypeSTD1,
			},
			want:    bcc.StorageTypeSTD1,
			wantStr: "std1",
			wantErr: false,
		},
		{
			name: "StorageTypeHP1",
			args: args{
				ctx:         context.TODO(),
				storageType: logicbcc.StorageTypeHP1,
			},
			want:    bcc.StorageTypeHP1,
			wantStr: "hp1",
			wantErr: false,
		}, {
			name: "StorageTypeCloudHP1",
			args: args{
				ctx:         context.TODO(),
				storageType: logicbcc.StorageTypeCloudHP1,
			},
			want:    bcc.StorageTypeCloudHP1,
			wantStr: "cloud_hp1",
			wantErr: false,
		}, {
			name: "StorageTypeHDD",
			args: args{
				ctx:         context.TODO(),
				storageType: logicbcc.StorageTypeHDD,
			},
			want:    bcc.StorageTypeHDD,
			wantStr: "hdd",
			wantErr: false,
		}, {
			name: "StorageTypeLocal",
			args: args{
				ctx:         context.TODO(),
				storageType: logicbcc.StorageTypeLocal,
			},
			want:    bcc.StorageTypeLocal,
			wantStr: "local",
			wantErr: false,
		}, {
			name: "StorageTypeDCCSATA",
			args: args{
				ctx:         context.TODO(),
				storageType: logicbcc.StorageTypeDCCSATA,
			},
			want:    bcc.StorageTypeDCCSATA,
			wantStr: "sata",
			wantErr: false,
		}, {
			name: "StorageTypeDCCSSD",
			args: args{
				ctx:         context.TODO(),
				storageType: logicbcc.StorageTypeDCCSSD,
			},
			want:    bcc.StorageTypeDCCSSD,
			wantStr: "ssd",
			wantErr: false,
		}, {
			name: "未支持类型",
			args: args{
				ctx:         context.TODO(),
				storageType: "",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := TransLogicBCCStorageTypeToOpenBCC(tt.args.ctx, tt.args.storageType)
			if err != nil {
				if tt.wantErr == true {
					return
				}

				t.Errorf("TransLogicBCCStorageTypeToOpenBCC() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr == true {
				t.Errorf("TransLogicBCCStorageTypeToOpenBCC() failed, wantErr=true got=false")
				return
			}

			if got != tt.want {
				t.Errorf("TransLogicBCCStorageTypeToOpenBCC() = %v, want %v", got, tt.want)
			}

			if !reflect.DeepEqual(string(got), tt.wantStr) {
				t.Errorf("TransLogicBCCStorageTypeToOpenBCC() = %v, want %v", got, tt.wantStr)
			}
		})
	}
}

func TestTransOpenBCCStorageTypeToLogicBCC(t *testing.T) {
	type args struct {
		ctx         context.Context
		storageType bcc.StorageType
	}
	tests := []struct {
		name    string
		args    args
		want    logicbcc.StorageType
		wantStr string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "StorageTypeSTD1",
			args: args{
				ctx:         context.TODO(),
				storageType: bcc.StorageTypeSTD1,
			},
			want:    logicbcc.StorageTypeSTD1,
			wantStr: "sata",
			wantErr: false,
		},
		{
			name: "StorageTypeHP1",
			args: args{
				ctx:         context.TODO(),
				storageType: bcc.StorageTypeHP1,
			},
			want:    logicbcc.StorageTypeHP1,
			wantStr: "ssd",
			wantErr: false,
		},
		{
			name: "StorageTypeCloudHP1",
			args: args{
				ctx:         context.TODO(),
				storageType: bcc.StorageTypeCloudHP1,
			},
			want:    logicbcc.StorageTypeCloudHP1,
			wantStr: "premium_ssd",
			wantErr: false,
		},
		{
			name: "StorageTypeHDD",
			args: args{
				ctx:         context.TODO(),
				storageType: bcc.StorageTypeHDD,
			},
			want:    logicbcc.StorageTypeHDD,
			wantStr: "hdd",
			wantErr: false,
		},
		{
			name: "StorageTypeLocal",
			args: args{
				ctx:         context.TODO(),
				storageType: bcc.StorageTypeLocal,
			},
			want:    logicbcc.StorageTypeLocal,
			wantStr: "local",
			wantErr: false,
		},
		{
			name: "StorageTypeDCCSATA",
			args: args{
				ctx:         context.TODO(),
				storageType: bcc.StorageTypeDCCSATA,
			},
			want:    logicbcc.StorageTypeDCCSATA,
			wantStr: "SATA",
			wantErr: false,
		},
		{
			name: "StorageTypeDCCSSD",
			args: args{
				ctx:         context.TODO(),
				storageType: bcc.StorageTypeDCCSSD,
			},
			want:    logicbcc.StorageTypeDCCSSD,
			wantStr: "SSD",
			wantErr: false,
		},
		{
			name: "未支持类型",
			args: args{
				ctx:         context.TODO(),
				storageType: "",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := TransOpenBCCStorageTypeToLogicBCC(tt.args.ctx, tt.args.storageType)
			if err != nil {
				if tt.wantErr == true {
					return
				}

				t.Errorf("TransOpenBCCStorageTypeToLogicBCC() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr == true {
				t.Errorf("TransOpenBCCStorageTypeToLogicBCC() failed, wantErr=true got=false")
				return
			}

			if got != tt.want {
				t.Errorf("TransOpenBCCStorageTypeToLogicBCC() = %v, want %v", got, tt.want)
			}

			if !reflect.DeepEqual(string(got), tt.wantStr) {
				t.Errorf("TransOpenBCCStorageTypeToLogicBCC() = %v, want %v", got, tt.wantStr)
			}
		})
	}
}

func TestTransLogicBCCStatusToLogicalBCCStatus(t *testing.T) {
	type args struct {
		ctx    context.Context
		status logicbcc.ServerStatus
	}
	tests := []struct {
		name    string
		args    args
		want    logicalbcc.ServerStatus
		wantStr string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "ServerStatusActive",
			args: args{
				ctx:    context.TODO(),
				status: logicbcc.ServerStatusActive,
			},
			want:    logicalbcc.ServerStatusActive,
			wantStr: "ACTIVE",
			wantErr: false,
		},
		{
			name: "ServerStatusBuild",
			args: args{
				ctx:    context.TODO(),
				status: logicbcc.ServerStatusBuild,
			},
			want:    logicalbcc.ServerStatusBuild,
			wantStr: "BUILD",
			wantErr: false,
		},
		{
			name: "ServerStatusRebuild",
			args: args{
				ctx:    context.TODO(),
				status: logicbcc.ServerStatusRebuild,
			},
			want:    logicalbcc.ServerStatusRebuild,
			wantStr: "REBUILD",
			wantErr: false,
		}, {
			name: "ServerStatusDeleted",
			args: args{
				ctx:    context.TODO(),
				status: logicbcc.ServerStatusDeleted,
			},
			want:    logicalbcc.ServerStatusDeleted,
			wantStr: "DELETED",
			wantErr: false,
		}, {
			name: "ServerStatusSnapshot",
			args: args{
				ctx:    context.TODO(),
				status: logicbcc.ServerStatusSnapshot,
			},
			want:    logicalbcc.ServerStatusSnapshot,
			wantStr: "SNAPSHOT",
			wantErr: false,
		}, {
			name: "ServerStatusDeleteSnapshot",
			args: args{
				ctx:    context.TODO(),
				status: logicbcc.ServerStatusDeleteSnapshot,
			},
			want:    logicalbcc.ServerStatusDeleteSnapshot,
			wantStr: "DELETE_SNAPSHOT",
			wantErr: false,
		}, {
			name: "ServerStatusVolumeResize",
			args: args{
				ctx:    context.TODO(),
				status: logicbcc.ServerStatusVolumeResize,
			},
			want:    logicalbcc.ServerStatusVolumeResize,
			wantStr: "VOLUME_RESIZE",
			wantErr: false,
		}, {
			name: "ServerStatusError",
			args: args{
				ctx:    context.TODO(),
				status: logicbcc.ServerStatusError,
			},
			want:    logicalbcc.ServerStatusError,
			wantStr: "ERROR",
			wantErr: false,
		}, {
			name: "ServerStatusExpired",
			args: args{
				ctx:    context.TODO(),
				status: logicbcc.ServerStatusExpired,
			},
			want:    logicalbcc.ServerStatusExpired,
			wantStr: "EXPIRED",
			wantErr: false,
		}, {
			name: "ServerStatusReboot",
			args: args{
				ctx:    context.TODO(),
				status: logicbcc.ServerStatusReboot,
			},
			want:    logicalbcc.ServerStatusReboot,
			wantStr: "REBOOT",
			wantErr: false,
		}, {
			name: "ServerStatusRecharge",
			args: args{
				ctx:    context.TODO(),
				status: logicbcc.ServerStatusRecharge,
			},
			want:    logicalbcc.ServerStatusRecharge,
			wantStr: "RECHARGE",
			wantErr: false,
		},
		{
			name: "ServerStatusShutoff",
			args: args{
				ctx:    context.TODO(),
				status: logicbcc.ServerStatusShutoff,
			},
			want:    logicalbcc.ServerStatusShutoff,
			wantStr: "SHUTOFF",
			wantErr: false,
		}, {
			name: "ServerStatusStopped",
			args: args{
				ctx:    context.TODO(),
				status: logicbcc.ServerStatusStopped,
			},
			want:    logicalbcc.ServerStatusStopped,
			wantStr: "STOPPED",
			wantErr: false,
		}, {
			name: "ServerStatusUnknown",
			args: args{
				ctx:    context.TODO(),
				status: logicbcc.ServerStatusUnknown,
			},
			want:    logicalbcc.ServerStatusUnknown,
			wantStr: "UNKNOWN",
			wantErr: false,
		},
		{
			name: "不支持的 Status",
			args: args{
				ctx:    context.TODO(),
				status: "chenhuan",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := TransLogicBCCStatusToLogicalBCCStatus(tt.args.ctx, tt.args.status)
			if err != nil {
				if tt.wantErr == true {
					return
				}

				t.Errorf("TransLogicBCCStatusToLogicalBCCStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr == true {
				t.Errorf("TransLogicBCCStatusToLogicalBCCStatus() failed, wantErr=true got=false")
				return
			}

			if got != tt.want {
				t.Errorf("TransLogicBCCStatusToLogicalBCCStatus() = %v, want %v", got, tt.want)
			}

			if !reflect.DeepEqual(string(got), tt.wantStr) {
				t.Errorf("TransLogicBCCStatusToLogicalBCCStatus() = %v, wantStr %v", got, tt.wantStr)
			}
		})
	}
}

func TestTransLogicalBCCStatusToLogicBCCStatus(t *testing.T) {
	type args struct {
		ctx    context.Context
		status logicalbcc.ServerStatus
	}
	tests := []struct {
		name    string
		args    args
		want    logicbcc.ServerStatus
		wantStr string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "ServerStatusActive",
			args: args{
				ctx:    context.TODO(),
				status: logicalbcc.ServerStatusActive,
			},
			want:    logicbcc.ServerStatusActive,
			wantStr: "ACTIVE",
			wantErr: false,
		},
		{
			name: "ServerStatusBuild",
			args: args{
				ctx:    context.TODO(),
				status: logicalbcc.ServerStatusBuild,
			},
			want:    logicbcc.ServerStatusBuild,
			wantStr: "BUILD",
			wantErr: false,
		},
		{
			name: "ServerStatusRebuild",
			args: args{
				ctx:    context.TODO(),
				status: logicalbcc.ServerStatusRebuild,
			},
			want:    logicbcc.ServerStatusRebuild,
			wantStr: "REBUILD",
			wantErr: false,
		}, {
			name: "ServerStatusDeleted",
			args: args{
				ctx:    context.TODO(),
				status: logicalbcc.ServerStatusDeleted,
			},
			want:    logicbcc.ServerStatusDeleted,
			wantStr: "DELETED",
			wantErr: false,
		}, {
			name: "ServerStatusSnapshot",
			args: args{
				ctx:    context.TODO(),
				status: logicalbcc.ServerStatusSnapshot,
			},
			want:    logicbcc.ServerStatusSnapshot,
			wantStr: "SNAPSHOT",
			wantErr: false,
		}, {
			name: "ServerStatusDeleteSnapshot",
			args: args{
				ctx:    context.TODO(),
				status: logicalbcc.ServerStatusDeleteSnapshot,
			},
			want:    logicbcc.ServerStatusDeleteSnapshot,
			wantStr: "DELETE_SNAPSHOT",
			wantErr: false,
		}, {
			name: "ServerStatusVolumeResize",
			args: args{
				ctx:    context.TODO(),
				status: logicalbcc.ServerStatusVolumeResize,
			},
			want:    logicbcc.ServerStatusVolumeResize,
			wantStr: "VOLUME_RESIZE",
			wantErr: false,
		}, {
			name: "ServerStatusError",
			args: args{
				ctx:    context.TODO(),
				status: logicalbcc.ServerStatusError,
			},
			want:    logicbcc.ServerStatusError,
			wantStr: "ERROR",
			wantErr: false,
		}, {
			name: "ServerStatusExpired",
			args: args{
				ctx:    context.TODO(),
				status: logicalbcc.ServerStatusExpired,
			},
			want:    logicbcc.ServerStatusExpired,
			wantStr: "EXPIRED",
			wantErr: false,
		}, {
			name: "ServerStatusReboot",
			args: args{
				ctx:    context.TODO(),
				status: logicalbcc.ServerStatusReboot,
			},
			want:    logicbcc.ServerStatusReboot,
			wantStr: "REBOOT",
			wantErr: false,
		}, {
			name: "ServerStatusRecharge",
			args: args{
				ctx:    context.TODO(),
				status: logicalbcc.ServerStatusRecharge,
			},
			want:    logicbcc.ServerStatusRecharge,
			wantStr: "RECHARGE",
			wantErr: false,
		},
		{
			name: "ServerStatusShutoff",
			args: args{
				ctx:    context.TODO(),
				status: logicalbcc.ServerStatusShutoff,
			},
			want:    logicbcc.ServerStatusShutoff,
			wantStr: "SHUTOFF",
			wantErr: false,
		}, {
			name: "ServerStatusStopped",
			args: args{
				ctx:    context.TODO(),
				status: logicalbcc.ServerStatusStopped,
			},
			want:    logicbcc.ServerStatusStopped,
			wantStr: "STOPPED",
			wantErr: false,
		}, {
			name: "ServerStatusUnknown",
			args: args{
				ctx:    context.TODO(),
				status: logicalbcc.ServerStatusUnknown,
			},
			want:    logicbcc.ServerStatusUnknown,
			wantStr: "UNKNOWN",
			wantErr: false,
		},
		{
			name: "不支持的 Status",
			args: args{
				ctx:    context.TODO(),
				status: "chenhuan",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := TransLogicalBCCStatusToLogicBCCStatus(tt.args.ctx, tt.args.status)
			if err != nil {
				if tt.wantErr == true {
					return
				}

				t.Errorf("TransLogicalBCCStatusToLogicBCCStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr == true {
				t.Errorf("TransLogicalBCCStatusToLogicBCCStatus() failed, wantErr=true got=false")
				return
			}

			if got != tt.want {
				t.Errorf("TransLogicalBCCStatusToLogicBCCStatus() = %v, want %v", got, tt.want)
			}

			if !reflect.DeepEqual(string(got), tt.wantStr) {
				t.Errorf("TransLogicalBCCStatusToLogicBCCStatus() = %v, wantStr %v", got, tt.wantStr)
			}
		})
	}
}

func TestTransOpenBCCPaymentTimingToLogicBCC(t *testing.T) {
	type args struct {
		ctx           context.Context
		paymentTiming bcc.PaymentTiming
	}
	tests := []struct {
		name    string
		args    args
		want    logicbcc.ProductType
		wantErr bool
	}{
		{
			name: "Prepaid",
			args: args{
				ctx:           context.TODO(),
				paymentTiming: "Prepaid",
			},
			want:    "prepay",
			wantErr: false,
		},
		{
			name: "Postpaid",
			args: args{
				ctx:           context.TODO(),
				paymentTiming: "Postpaid",
			},
			want:    "postpay",
			wantErr: false,
		},
		{
			name: "invalid_pay",
			args: args{
				ctx:           context.TODO(),
				paymentTiming: "invalid_pay",
			},
			want:    "",
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := TransOpenBCCPaymentTimingToLogicBCC(tt.args.ctx, tt.args.paymentTiming)
			if (err != nil) != tt.wantErr {
				t.Errorf("TransOpenBCCPaymentTimingToLogicBCC() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("TransOpenBCCPaymentTimingToLogicBCC() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestTransLogicBCCPaymentTimingToOpenBCC(t *testing.T) {
	type args struct {
		ctx           context.Context
		paymentTiming logicbcc.ProductType
	}
	tests := []struct {
		name    string
		args    args
		want    bcc.PaymentTiming
		wantErr bool
	}{
		{
			name: "prepay",
			args: args{
				ctx:           context.TODO(),
				paymentTiming: "prepay",
			},
			want:    "Prepaid",
			wantErr: false,
		},
		{
			name: "postpay",
			args: args{
				ctx:           context.TODO(),
				paymentTiming: "postpay",
			},
			want:    "Postpaid",
			wantErr: false,
		},
		{
			name: "invalid_pay",
			args: args{
				ctx:           context.TODO(),
				paymentTiming: "invalid_pay",
			},
			want:    "",
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := TransLogicBCCPaymentTimingToOpenBCC(tt.args.ctx, tt.args.paymentTiming)
			if (err != nil) != tt.wantErr {
				t.Errorf("TransLogicBCCPaymentTimingToOpenBCC() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("TransLogicBCCPaymentTimingToOpenBCC() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestTransOpenEIPBillingMethodToLogicBCC(t *testing.T) {
	type args struct {
		ctx           context.Context
		billingMethod eip.BillingMethod
	}
	tests := []struct {
		name    string
		args    args
		want    logicbcc.EIPBillingMethod
		wantErr bool
	}{
		{
			name: "ByBandwidth",
			args: args{
				ctx:           context.TODO(),
				billingMethod: "ByBandwidth",
			},
			want:    "bandwidth",
			wantErr: false,
		},
		{
			name: "ByTraffic",
			args: args{
				ctx:           context.TODO(),
				billingMethod: "ByTraffic",
			},
			want:    "netraffic",
			wantErr: false,
		},
		{
			name: "invalid_type",
			args: args{
				ctx:           context.TODO(),
				billingMethod: "invalid_type",
			},
			want:    "",
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := TransOpenEIPBillingMethodToLogicBCC(tt.args.ctx, tt.args.billingMethod)
			if (err != nil) != tt.wantErr {
				t.Errorf("TransOpenEIPBillingMethodToLogicBCC() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("TransOpenEIPBillingMethodToLogicBCC() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestTransAvailableZoneToZoneName(t *testing.T) {
	type args struct {
		ctx           context.Context
		country       string
		region        string
		availableZone string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			args: args{
				ctx:           context.TODO(),
				country:       "cn",
				region:        "bj",
				availableZone: "zoneE",
			},
			want: "cn-bj-e",
		},
		{
			name: "unknow zone",
			args: args{
				ctx:           context.TODO(),
				country:       "cn",
				region:        "bj",
				availableZone: "zoneG",
			},
			want: "zoneG",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := TransAvailableZoneToZoneName(tt.args.ctx, tt.args.country, tt.args.region, tt.args.availableZone); got != tt.want {
				t.Errorf("TransAvailableZoneToZoneName() = %v, want %v", got, tt.want)
			}
		})
	}
}
