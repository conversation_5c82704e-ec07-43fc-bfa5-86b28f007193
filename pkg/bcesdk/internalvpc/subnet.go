package internalvpc

import (
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
)

func (c *Client) GetSubnetByID(ctx context.Context, shortID string, option *bce.SignOption) (*Subnet, error) {
	if shortID == "" {
		return nil, fmt.Errorf("shortID cannot be empty")
	}
	params := map[string]string{
		"id": shortID,
	}

	url := fmt.Sprintf("v1/api/logical/network/subnet")
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	respContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result SubnetResponse
	err = json.Unmarshal(respContent, &result)
	if err != nil {
		return nil, err
	}

	return result.Subnet, nil
}
