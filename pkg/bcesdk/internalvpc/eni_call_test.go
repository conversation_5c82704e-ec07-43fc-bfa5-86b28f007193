package internalvpc

import (
	"context"
	"encoding/json"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
	"testing"
	"time"
)

func testGetENIs(t *testing.T) {
	accessKeyID := "e0c68be8495540f280b3e9ef03ec25d2"
	secretAccessKey := "b599d5f4f30e41bcb0e1482c9de65bd6"
	region := "gz"

	eniclient := NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      region,
		Endpoint:    Endpoints[region],
	})
	eniclient.SetDebug(true)

	req := &GetENIsRequest{
		PageNo:   1,
		PageSize: 10000,
		VPCUUID:  "314f0fae-87ea-4e80-87d7-199b95eef5f3",
	}

	resp, err := eniclient.GetENIs(context.TODO(), req, NewSignOption())
	if err != nil {
		t.Errorf("GetENIs failed: %v", err)
	}

	if str, err := json.Marshal(resp); err == nil {
		t.Logf("GetENIs success: %v", string(str))
	}
}

func testDetachENI(t *testing.T) {
	accessKeyID := "e0c68be8495540f280b3e9ef03ec25d2"
	secretAccessKey := "b599d5f4f30e41bcb0e1482c9de65bd6"
	region := "gz"

	eniclient := NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      region,
		Endpoint:    Endpoints[region],
	})
	eniclient.SetDebug(true)

	err := eniclient.DetachENI(context.TODO(), "eni-gps1mnafhbjq", "27b5f101-c7bc-4f1a-9f26-7c0a2b4c47f0", NewSignOption())
	if err != nil {
		t.Errorf("DeleteENI failed: %v", err)
	}
}

func testDeleteENI(t *testing.T) {
	accessKeyID := "e0c68be8495540f280b3e9ef03ec25d2"
	secretAccessKey := "b599d5f4f30e41bcb0e1482c9de65bd6"
	region := "gz"

	eniclient := NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      region,
		Endpoint:    Endpoints[region],
	})
	eniclient.SetDebug(true)

	err := eniclient.DeleteENI(context.TODO(), "eni-gps1mnafhbjq", NewSignOption())
	if err != nil {
		t.Errorf("DeleteENI failed: %v", err)
	}
}
