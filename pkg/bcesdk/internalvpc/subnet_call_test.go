package internalvpc

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
)

func testGetSubnetByID(t *testing.T) {
	region := "gz"
	accessKeyID := "e0c68be8495540f280b3e9ef03ec25d2"
	secretAccessKey := "b599d5f4f30e41bcb0e1482c9de65bd6"

	vpcclient := NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      region,
		Endpoint:    Endpoints[region],
	})
	vpcclient.SetDebug(true)

	response, err := vpcclient.GetSubnetByID(context.Background(), "sbn-mnbvhnuupv1u", NewSignOption())
	if err != nil {
		t.<PERSON><PERSON><PERSON>("GetSubnetByID failed: %v", err)
	}
	if responseByte, err := json.Marshal(response); err == nil {
		t.Logf("GetSubnetByID succeeded: %s", string(responseByte))
	}
}
