package internalvpc

//// FakeClient for AppBLB fake client
//type FakeClient struct {
//	LoadBalancerMap map[string]*BlbInstance
//}
//
//// NewFakeClient for logical BLB fake client
//func NewFakeClient() *FakeClient {
//	return &FakeClient{
//		// Key = blb_id
//		LoadBalancerMap: map[string]*BlbInstance{},
//	}
//}
//
//func (f *FakeClient) CreateAppLoadBalancer(ctx context.Context, args *CreateAppLoadBalancerArgs, option *bce.SignOption) (*CreateAppLoadBalancerResponse, error) {
//	if args == nil {
//		return nil, fmt.Errorf("CreateAppLoadBalancer faile: args is nil")
//	}
//
//	blb := &AppLoadBalancer{
//		Name:   args.Name,
//		Status: BLBStatusAvailable,
//	}
//
//	for {
//		blbID := util.GenerateBCEShortID("lb")
//		if _, ok := f.AppLoadBalancerMap[blbID]; !ok {
//			blb.BLBID = blbID
//			f.AppLoadBalancerMap[blbID] = blb
//
//			break
//		}
//	}
//
//	return &CreateAppLoadBalancerResponse{
//		Name:  blb.Name,
//		BLBID: blb.BLBID,
//	}, nil
//}
