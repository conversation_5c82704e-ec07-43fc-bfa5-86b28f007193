package internalvpc

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
)

func (c *Client) MapByLongID(ctx context.Context, longIDs []string, serviceType int, option *bce.SignOption) (map[string]string, error) {
	params := map[string]string{}

	var request LongIDRequest
	request.Type = serviceType
	request.LongIDs = longIDs
	postContent, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("v1/api/logical/network/id/long")
	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	respContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var resultMap IdMapperResponse
	err = json.Unmarshal(respContent, &resultMap)
	if err != nil {
		return nil, err
	}

	return resultMap.Map, err
}

func (c *Client) MapByShortID(ctx context.Context, shortIDs []string, option *bce.SignOption) (map[string]string, error) {
	params := map[string]string{}

	var request ShortIDRequest
	request.ShortIDs = shortIDs
	postContent, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("v1/api/logical/network/id/short")
	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	respContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var resultMap IdMapperResponse
	err = json.Unmarshal(respContent, &resultMap)
	if err != nil {
		return nil, err
	}

	return resultMap.Map, nil
}
