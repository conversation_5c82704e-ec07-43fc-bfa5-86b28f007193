// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/03/28 13:11:00, by <EMAIL>, create
*/
/*
实现 CCE V2 SDK Cluster 相关方法
*/

package ccev2

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
	ccetypes "icode.baidu.com/baidu/cprom/cloud-stack/pkg/types"
	"strconv"
)

// CreateCluster - 创建集群
// PARAMS:
//   - ctx: The context to trace request
//   - args: *ClusterConfig 创建集群配置
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	string: ClusterID 集群 ID
//	error: nil if succeed, error if fail
func (c *Client) CreateCluster(ctx context.Context, args *CreateClusterRequest, option *bce.SignOption) (*CreateClusterResponse, error) {
	if args == nil {
		return nil, fmt.Errorf("args is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("cluster", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r CreateClusterResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// UpdateCluster - 更新集群
func (c *Client) UpdateCluster(ctx context.Context, clusterID string, args *ccetypes.ClusterSpec,
	option *bce.SignOption) (*UpdateClusterResponse, error) {
	return nil, nil
}

// DeleteCluster - 删除集群
// PARAMS:
//   - ctx: The context to trace request
//   - ClusterID: 集群 ID
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) DeleteCluster(ctx context.Context, clusterID string, deleteOptions *DeleteOptions, option *bce.SignOption) (*DeleteClusterResponse, error) {
	if clusterID == "" {
		return nil, fmt.Errorf("clusterID is empty")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	if deleteOptions != nil {
		params["moveOut"] = fmt.Sprintf("%v", deleteOptions.MoveOut)
		params["deleteResource"] = fmt.Sprintf("%v", deleteOptions.DeleteResource)
		params["deleteCDSSnapshot"] = fmt.Sprintf("%v", deleteOptions.DeleteCDSSnapshot)
	}

	url := fmt.Sprintf("cluster/%s", clusterID)
	req, err := bce.NewRequest("DELETE", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r DeleteClusterResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// GetCluster - 查询集群
//
// PARAMS:
//   - ctx: The context to trace request
//   - ClusterID: 集群 ID
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) GetCluster(ctx context.Context, clusterID string, option *bce.SignOption) (*GetClusterResponse, error) {
	if clusterID == "" {
		return nil, fmt.Errorf("clusterID is empty")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	url := fmt.Sprintf("cluster/%s", clusterID)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r GetClusterResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// GetEtcdCert - 查询集群ETCD凭证
//
// PARAMS:
//  - ctx: The context to trace request
//  - ClusterID: 集群 ID
//  - option: *bce.SignOption 代签名方法
//
// RETURNS:
//   error: nil if succeed, error if fail
func (c *Client) GetEtcdCert(ctx context.Context, clusterID string, option *bce.SignOption) (*GetEtcdCertsResponse, error) {
	if clusterID == "" {
		return nil, fmt.Errorf("clusterID is empty")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	url := fmt.Sprintf("certs/%s/etcd", clusterID)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r GetEtcdCertsResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// ListClusters - 集群列表
func (c *Client) ListClusters(ctx context.Context, keywordType ClusterKeywordType, keyword string,
	orderBy ClusterOrderBy, order Order, pageNum, pageSize int, option *bce.SignOption) (*ListClustersResponse, error) {

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
		"keywordType": string(keywordType),
		"keyword":     keyword,
		"orderBy":     string(orderBy),
		"order":       string(order),
		"pageNo":      strconv.Itoa(pageNum),
		"pageSize":    strconv.Itoa(pageSize),
	}

	url := fmt.Sprintf("clusters")
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r ListClustersResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// GetClusterOfBBE - 查询 BBE 集群, 仅提供 BBE 使用
//
// PARAMS:
//   - ctx: The context to trace request
//   - ClusterID: 集群 ID
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) GetClusterOfBBE(ctx context.Context, clusterID string, option *bce.SignOption) (*GetClusterOfBBEResponse, error) {
	if clusterID == "" {
		return nil, fmt.Errorf("clusterID is empty")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	url := fmt.Sprintf("cluster/bbe/%s", clusterID)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r GetClusterOfBBEResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// CreateAutoScalerConfig - 初始化集群自动扩缩容配置
//
// PARAMS:
//   - ctx: The context to trace request
//   - ClusterID: 集群 ID
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) CreateAutoScalerConfig(ctx context.Context, clusterID string, option *bce.SignOption) (*CommonResponse, error) {
	if clusterID == "" {
		return nil, fmt.Errorf("clusterID is empty")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	url := fmt.Sprintf("autoscaler/%s", clusterID)
	req, err := bce.NewRequest("POST", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r CommonResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// ResetClusterRetryCount - 重置 Cluster 及关联 Instance 的 RetryCount 为 0
func (c *Client) ResetClusterRetryCount(ctx context.Context, clusterID string, option *bce.SignOption) error {
	if clusterID == "" {
		return fmt.Errorf("clusterID is empty")
	}

	url := fmt.Sprintf("cluster/%s/retry", clusterID)
	req, err := bce.NewRequest("PUT", c.GetURL(url, nil), nil)
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}
