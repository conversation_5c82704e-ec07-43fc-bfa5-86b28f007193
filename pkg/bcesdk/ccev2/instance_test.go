// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/04/06 13:08:00, by <EMAIL>, create
*/
/*
实现 CCE V2 SDK Instance 相关测试方法
*/

package ccev2

import (
	"context"
	"fmt"
	utils2 "icode.baidu.com/baidu/cprom/cloud-stack/cprom-common/utils"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bccimage"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/retrypolicy"
	ccetypes "icode.baidu.com/baidu/cprom/cloud-stack/pkg/types"
)

func testCreateBCCInstances(t *testing.T) {
	args := []*InstanceSet{
		{
			InstanceSpec: ccetypes.InstanceSpec{
				InstanceType: bcc.InstanceTypeN3,
				VPCConfig: ccetypes.VPCConfig{
					VPCSubnetID:     "sbn-xq42pjzwmdmg",
					SecurityGroupID: "g-wd9iaxq9z3xs",
				},
				InstanceResource: ccetypes.InstanceResource{
					RootDiskSize: 100,
					CPU:          1,
					MEM:          4,
				},
				AdminPassword: "jhkj4%w@oip",
				InstanceOS: ccetypes.InstanceOS{
					ImageType: bccimage.ImageTypeSystem,
					OSType:    bccimage.OSTypeLinux,
					OSName:    bccimage.OSNameCentOS,
					OSVersion: "7.5",
					OSArch:    "x86_64 (64bit)",
				},
				InstanceChargingType: bcc.PaymentTimingPostpaid,
			},
			Count: 1,
		},
	}

	// 创建节点
	resp, err := client.CreateInstances(context.TODO(), "cce-olfmqhbg", args, nil)
	if err != nil {
		t.Errorf("CreateInstances failed: %v", err)
		return
	}

	t.Errorf("CreateInstances success: %s", utils2.ToJSON(resp))
}

func testCreateExistedBCCInstances(t *testing.T) {
	rebuild := true
	args := []*InstanceSet{
		{
			InstanceSpec: ccetypes.InstanceSpec{
				Existed: true,
				ExistedOption: ccetypes.ExistedOption{
					ExistedInstanceID: "i-qm0sl0Wp",
					Rebuild:           &(rebuild),
				},
				DeleteOption: &ccetypes.DeleteOption{
					MoveOut: true,
				},
				MachineType:   ccetypes.MachineTypeBCC,
				AdminPassword: "jhkj4%w@oip",
				InstanceOS: ccetypes.InstanceOS{
					ImageType: bccimage.ImageTypeSystem,
					ImageName: "7.3 x86_64 (64bit)",
					OSType:    bccimage.OSTypeLinux,
					OSName:    bccimage.OSNameCentOS,
					OSVersion: "7.3",
					OSArch:    "x86_64 (64bit)",
				},
			},
		},
	}

	// 创建节点
	resp, err := client.CreateInstances(context.TODO(), "cce-xy2kmy91", args, nil)
	if err != nil {
		t.Errorf("CreateInstances failed: %v", err)
		return
	}

	t.Errorf("CreateInstances success: %s", utils2.ToJSON(resp))
}

func testDeleteExistedBCCInstances(t *testing.T) {
	// 删除节点
	_, err := client.DeleteInstances(context.TODO(), "cce-mmaltwf5", &DeleteInstancesRequest{
		// CCEInstanceIDs: []string{
		// 	"cce-mmaltwf5-l9g6p4wn",
		// },
		InstanceIDs: []string{
			"i-h5uM6bEm",
		},
	}, nil)

	if err != nil {
		t.Errorf("CreateInstances failed: %v", err)
		return
	}
}

func testGetInstance(t *testing.T) {
	resp, err := client.GetInstance(context.TODO(), "cce-049knl8d", "i-jUK6izeR", nil)
	if err != nil {
		t.Errorf("GetInstance failed: %v", err)
		return
	}

	t.Errorf(utils2.ToJSON(resp))
}

func testDeleteInstances(t *testing.T) {
	if _, err := client.DeleteInstances(context.TODO(), "cce-dlj0vmu6", &DeleteInstancesRequest{
		InstanceIDs: []string{
			"",
			"cce-ghrrg878-u5tydm5h",
		},
	}, nil); err != nil {
		t.Errorf("DeleteInstances failed: %v", err)
		return
	}
}

func testListInstancesByPage(t *testing.T) {
	resp, err := client.ListInstancesByPage(context.TODO(), "cce-06w1u9nk", &ListInstancesByPageParams{
		// EnableInternalFields:    true,
		EnableUpgradeNodeFields: true,
		IPList:                  "***********,***********",
	}, nil)
	if err != nil {
		t.Errorf("TestListInstancesByPage failed: %v", err)
		return
	}

	t.Errorf(utils2.ToJSON(resp))
}

func testClient_ListInstancesByInstanceGroupID(t *testing.T) {
	config := bce.Config{
		Credentials: bce.NewCredentials("6aff65797f724d92a01b2630ad0b80d6", "62938cc789964b86b61e7d818d736560"),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      "gztest",
		RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
	}

	clusterID := "testClusterID"
	instanceGroupID := "testInstanceGroupID"

	mockResp := ListInstancesByInstanceGroupIDResponse{
		CommonResponse: CommonResponse{
			RequestID: "RequestID",
		},
		Page: ListInstancesByInstanceGroupIDPage{
			PageNo:     1,
			PageSize:   10,
			TotalCount: 1,
			List: []*Instance{
				{
					Spec: &InstanceSpec{
						CCEInstanceID:        "xxxx",
						InstanceName:         "",
						RuntimeType:          "",
						RuntimeVersion:       "",
						ClusterID:            "",
						ClusterRole:          "",
						UserID:               "",
						InstanceGroupID:      "",
						InstanceGroupName:    "",
						MachineType:          "",
						InstanceType:         "",
						BBCOption:            nil,
						VPCConfig:            VPCConfig{},
						InstanceResource:     ccetypes.InstanceResource{},
						ImageID:              "",
						InstanceOS:           ccetypes.InstanceOS{},
						NeedEIP:              false,
						EIPOption:            nil,
						SSHKeyID:             "",
						InstanceChargingType: "",
						Tags:                 nil,
						Labels:               nil,
						Taints:               nil,
					},
					Status: &InstanceStatus{
						Machine:       Machine{},
						InstancePhase: "",
						MachineStatus: "",
					},
					CreatedAt: time.Now(),
				},
			},
		},
	}

	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "GET" || r.URL.Path != "/cluster/"+clusterID+"/instancegroup/"+instanceGroupID+"/instances" {
			w.WriteHeader(404)
			return
		}
		fmt.Fprintln(w, utils2.ToJSON(mockResp))
	}))
	defer ts.Close()

	config.Endpoint = ts.URL

	client := NewClient(&config)
	client.SetDebug(true)

	resp, err := client.ListInstancesByInstanceGroupID(context.Background(), clusterID, instanceGroupID, 0, 0, nil)
	if err != nil {
		t.Errorf("list instanceGroups failed: %v", err)
	}
	if !cmp.Equal(resp, &mockResp) {
		t.Errorf("diff: %s", cmp.Diff(resp, &mockResp))
	}
}

func testClient_UpdateInstance(t *testing.T) {
	config := bce.Config{
		Credentials: bce.NewCredentials("6aff65797f724d92a01b2630ad0b80d6", "62938cc789964b86b61e7d818d736560"),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      "gztest",
		RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
	}

	clusterID := "testClusterID"
	instanceID := "testInstanceID"

	mockResp := UpdateInstancesResponse{
		Instance:  &Instance{},
		RequestID: "",
	}

	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "PUT" || r.URL.Path != "/cluster/"+clusterID+"/instance/"+instanceID {
			w.WriteHeader(404)
			return
		}
		fmt.Fprintln(w, utils2.ToJSON(mockResp))
	}))
	defer ts.Close()

	config.Endpoint = ts.URL

	client := NewClient(&config)
	client.SetDebug(true)

	resp, err := client.UpdateInstance(context.Background(), clusterID, instanceID, &ccetypes.InstanceSpec{}, nil)
	if err != nil {
		t.Errorf("list instanceGroups failed: %v", err)
	}
	if !cmp.Equal(resp, &mockResp) {
		t.Errorf("diff: %s", cmp.Diff(resp, &mockResp))
	}
}

func testResetInstanceRetryCount(t *testing.T) {
	ctx := context.TODO()

	client := NewClient(&gzTestLililiConfig)
	client.SetDebug(true)

	if err := client.ResetInstanceRetryCount(ctx, "cce-4i5ekc0a", "cce-4i5ekc0a-6oa2bgu6", nil); err != nil {
		t.Errorf("ResetInstanceRetryCount failed: %s", err)
	}
}
