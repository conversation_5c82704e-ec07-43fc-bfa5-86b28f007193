// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/04/15 13:11:00, by ch<PERSON><PERSON><PERSON>@baidu.com, create
*/
/*
实现 CCE V2 SDK Cluster 相关方法
*/

package ccev2

import (
	"context"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
)

func (c *Client) GetClusterQuota(ctx context.Context, option *bce.SignOption) (*GetQuotaResponse, error) {
	return nil, nil
}

func (c *Client) GetClusterNodeQuota(ctx context.Context, clusterID string, option *bce.SignOption) (*GetQuotaResponse, error) {
	return nil, nil
}
