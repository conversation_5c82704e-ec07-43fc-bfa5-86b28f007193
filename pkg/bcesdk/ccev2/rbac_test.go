// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2022/02/23 10:37:00, by <EMAIL>, create
*/
/*
实现 CCE V2 SDK Instance 相关方法
*/

package ccev2

import (
	"context"
	utils2 "icode.baidu.com/baidu/cprom/cloud-stack/cprom-common/utils"
	"testing"
)

func testClient_CreateRBACKubeConfig(t *testing.T) {
	args := &KubeConfigRequest{
		ClusterID:      "cce-06w1u9nk",
		Temp:           true,
		ExpireHours:    1,
		KubeConfigType: "vpc",
	}

	resp, err := client.CreateRBACKubeConfig(context.TODO(), args, nil)
	if err != nil {
		t.Errorf("CreateRBACKubeConfig failed: %v", err)
		return
	}

	t.<PERSON>rrorf("CreateRBACKubeConfig success: %s", utils2.ToJSON(resp))
}
