/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  instancegroup
 * @Version: 1.0.0
 * @Date: 2020/7/9 4:16 下午
 */
package ccev2

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
	ccetypes "icode.baidu.com/baidu/cprom/cloud-stack/pkg/types"
)

type InstanceGroupListOption struct {
	pageNo   int
	pageSize int
}

// CreateInstanceGroup - 创建节点组
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: 集群id
//   - request: 创建节点组配置
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	*CreateInstanceGroupResponse: 创建节点组结果
//	error: nil if succeed, error if fail
func (c *Client) CreateInstanceGroup(ctx context.Context, clusterID string, request *CreateInstanceGroupRequest, option *bce.SignOption) (*CreateInstanceGroupResponse, error) {
	if clusterID == "" {
		return nil, fmt.Errorf("empty clusterID")
	}
	if request == nil {
		return nil, fmt.Errorf("request is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL(fmt.Sprintf("cluster/%s/instancegroup", clusterID), params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r CreateInstanceGroupResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// ListInstanceGroups - 获取节点组列表
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: 集群id
//   - listOption: *InstanceGroupListOption
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	*ListInstanceGroupResponse: 节点组列表结果
//	error: nil if succeed, error if fail
func (c *Client) ListInstanceGroups(ctx context.Context, clusterID string, listOption *InstanceGroupListOption, option *bce.SignOption) (*ListInstanceGroupResponse, error) {
	if clusterID == "" {
		return nil, fmt.Errorf("empty clusterID")
	}

	if listOption != nil && (listOption.pageNo <= 0 || listOption.pageSize <= 0) {
		return nil, fmt.Errorf("invlaid pageNo or pageSize")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	if listOption != nil {
		params["pageNo"] = strconv.Itoa(listOption.pageNo)
		params["pageSize"] = strconv.Itoa(listOption.pageSize)
	}

	req, err := bce.NewRequest("GET", c.GetURL(fmt.Sprintf("cluster/%s/instancegroups", clusterID), params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r ListInstanceGroupResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// GetInstanceGroup - 获取节点组详情
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: 集群id
//   - instanceGroupID: 节点组ID
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	*GetInstanceGroupResponse: 节点组详情结果
//	error: nil if succeed, error if fail
func (c *Client) GetInstanceGroup(ctx context.Context, clusterID, instanceGroupID string, option *bce.SignOption) (*GetInstanceGroupResponse, error) {
	if clusterID == "" {
		return nil, fmt.Errorf("empty clusterID")
	}

	if instanceGroupID == "" {
		return nil, fmt.Errorf("empty instanceGroupID")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	req, err := bce.NewRequest("GET", c.GetURL(fmt.Sprintf("cluster/%s/instancegroup/%s", clusterID, instanceGroupID), params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r GetInstanceGroupResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// UpdateInstanceGroupReplicas - 更新节点组节点副本数
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: 集群id
//   - instanceGroupID: 节点组ID
//   - request: *UpdateInstanceGroupReplicasRequest
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	*UpdateInstanceGroupReplicasResponse: 更新节点组结果
//	error: nil if succeed, error if fail
func (c *Client) UpdateInstanceGroupReplicas(ctx context.Context, clusterID, instanceGroupID string,
	request *UpdateInstanceGroupReplicasRequest, option *bce.SignOption) (*UpdateInstanceGroupReplicasResponse, error) {

	if clusterID == "" {
		return nil, fmt.Errorf("empty clusterID")
	}

	if instanceGroupID == "" {
		return nil, fmt.Errorf("empty instanceGroupID")
	}

	if request == nil {
		return nil, fmt.Errorf("nil UpdateInstanceGroupReplicasRequest")
	}

	if request.Replicas < 0 {
		return nil, fmt.Errorf("replicas < 0")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("PUT", c.GetURL(fmt.Sprintf("cluster/%s/instancegroup/%s/replicas",
		clusterID, instanceGroupID), params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r UpdateInstanceGroupReplicasResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// UpdateInstanceGroupClusterAutoscalerSpec - 更新节点组弹性伸缩配置
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: 集群id
//   - instanceGroupID: 节点组ID
//   - request: *ClusterAutoscalerSpec
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	*UpdateInstanceGroupClusterAutoscalerSpecResponse: 更新节点组结果
//	error: nil if succeed, error if fail
func (c *Client) UpdateInstanceGroupClusterAutoscalerSpec(ctx context.Context, clusterID, instanceGroupID string,
	request *ClusterAutoscalerSpec, option *bce.SignOption) (*UpdateInstanceGroupClusterAutoscalerSpecResponse, error) {

	if clusterID == "" {
		return nil, fmt.Errorf("empty clusterID")
	}

	if instanceGroupID == "" {
		return nil, fmt.Errorf("empty instanceGroupID")
	}

	if request == nil {
		return nil, fmt.Errorf("nil UpdateInstanceGroupReplicasRequest")
	}

	if request.Enabled {
		if request.MinReplicas < 0 || request.MaxReplicas < request.MinReplicas {
			return nil, fmt.Errorf("invalid minReplicas or maxReplicas")
		}
		if request.ScalingGroupPriority < 0 {
			return nil, fmt.Errorf("invalid scalingGroupPriority")
		}
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("PUT", c.GetURL(fmt.Sprintf("cluster/%s/instancegroup/%s/autoscaler",
		clusterID, instanceGroupID), params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r UpdateInstanceGroupClusterAutoscalerSpecResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// UpdateInstanceGroupInstanceTemplate - 更新节点组节点配置模版
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: 集群id
//   - instanceGroupID: 节点组ID
//   - request: *UpdateInstanceGroupInstanceTemplateRequest
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	*UpdateInstanceGroupInstanceTemplateResponse: 更新节点组结果
//	error: nil if succeed, error if fail
func (c *Client) UpdateInstanceGroupInstanceTemplate(ctx context.Context, clusterID, instanceGroupID string,
	request *UpdateInstanceGroupInstanceTemplateRequest, option *bce.SignOption) (*UpdateInstanceGroupInstanceTemplateResponse, error) {

	if clusterID == "" {
		return nil, fmt.Errorf("empty clusterID")
	}

	if instanceGroupID == "" {
		return nil, fmt.Errorf("empty instanceGroupID")
	}

	if request == nil {
		return nil, fmt.Errorf("nil UpdateInstanceGroupInstanceTemplateRequest")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	putContent, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("PUT", c.GetURL(fmt.Sprintf("cluster/%s/instancegroup/%s/instancetemplate",
		clusterID, instanceGroupID), params), bytes.NewBuffer(putContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r UpdateInstanceGroupInstanceTemplateResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// UpdateInstanceGroupPausedStatus - 更新节点组暂停发单状态
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: 集群id
//   - instanceGroupID: 节点组ID
//   - request: PauseDetail
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	*UpdateInstanceGroupPausedStatusResponse: 更新节点组结果
//	error: nil if succeed, error if fail
func (c *Client) UpdateInstanceGroupPausedStatus(ctx context.Context, clusterID, instanceGroupID string,
	request PauseDetail, option *bce.SignOption) (*UpdateInstanceGroupPausedStatusResponse, error) {

	if clusterID == "" {
		return nil, fmt.Errorf("empty clusterID")
	}

	if instanceGroupID == "" {
		return nil, fmt.Errorf("empty instanceGroupID")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	putContent, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("PUT", c.GetURL(fmt.Sprintf("cluster/%s/instancegroup/%s/pause",
		clusterID, instanceGroupID), params), bytes.NewBuffer(putContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r UpdateInstanceGroupPausedStatusResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// DeleteInstanceGroup - 删除节点组
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: 集群id
//   - instanceGroupID: 节点组ID
//   - deleteInstances: 是否从集群关联删除节点组内的节点
//
// RETURNS:
//
//	*DeleteInstanceGroupResponse: 删除节点组结果
//	error: nil if succeed, error if fail
func (c *Client) DeleteInstanceGroup(ctx context.Context, clusterID, instanceGroupID string, deleteInstances bool,
	option *bce.SignOption) (*DeleteInstanceGroupResponse, error) {

	if clusterID == "" {
		return nil, fmt.Errorf("empty clusterID")
	}

	if instanceGroupID == "" {
		return nil, fmt.Errorf("empty instanceGroupID")
	}

	params := map[string]string{
		"clientToken":     c.GenerateClientToken(),
		"deleteInstances": strconv.FormatBool(deleteInstances),
	}

	req, err := bce.NewRequest("DELETE", c.GetURL(fmt.Sprintf("cluster/%s/instancegroup/%s",
		clusterID, instanceGroupID), params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r DeleteInstanceGroupResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// CreateScaleUpInstanceGroupTask - 创建节点组扩容task
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: 集群id
//   - instanceGroupID: 节点组ID
//   - targetReplicas: 扩容后目标节点副本数
//
// RETURNS:
//
//	*CreateTaskResp: 任务创建结果
//	error: nil if succeed, error if fail
func (c *Client) CreateScaleUpInstanceGroupTask(ctx context.Context, clusterID, instanceGroupID string,
	targetReplicas int, option *bce.SignOption) (*CreateTaskResp, error) {
	if clusterID == "" {
		return nil, fmt.Errorf("empty clusterID")
	}

	if instanceGroupID == "" {
		return nil, fmt.Errorf("empty instanceGroupID")
	}

	params := map[string]string{
		"clientToken":  c.GenerateClientToken(),
		"upToReplicas": fmt.Sprintf("%d", targetReplicas),
	}

	req, err := bce.NewRequest("PUT", c.GetURL(fmt.Sprintf("cluster/%s/instancegroup/%s/scaleup",
		clusterID, instanceGroupID), params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r CreateTaskResp
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// CreateScaleDownInstanceGroupTask - 创建节点组缩容task
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: 集群id
//   - instanceGroupID: 节点组ID
//   - instancesToBeRemoved: 需要从节点组移除的节点
//
// RETURNS:
//
//	*CreateTaskResp: 任务创建结果
//	error: nil if succeed, error if fail
func (c *Client) CreateScaleDownInstanceGroupTask(ctx context.Context, clusterID, instanceGroupID string,
	instancesToBeRemoved []string, option *bce.SignOption) (*CreateTaskResp, error) {
	if clusterID == "" {
		return nil, fmt.Errorf("empty clusterID")
	}

	if instanceGroupID == "" {
		return nil, fmt.Errorf("empty instanceGroupID")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	putContent, err := json.Marshal(RemoveInstancesFromInstanceGroup{InstancesToBeRemoved: instancesToBeRemoved})
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("PUT", c.GetURL(fmt.Sprintf("cluster/%s/instancegroup/%s/scaledown",
		clusterID, instanceGroupID), params), bytes.NewBuffer(putContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r CreateTaskResp
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// CreateScaleDownInstanceGroupByCleanPolicy - 节点组移出节点支持清除策略
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: 集群id
//   - instanceGroupID: 节点组ID
//   - instancesToBeRemoved: 需要从节点组移除的节点
//   - cleanPolicy: 清除策略
//
// RETURNS:
//
//	*CreateTaskResp: 任务创建结果
//	error: nil if succeed, error if fail
func (c *Client) CreateScaleDownInstanceGroupByCleanPolicy(ctx context.Context, clusterID, instanceGroupID string,
	instancesToBeRemoved []string, cleanPolicy CleanPolicy, deleteOption *ccetypes.DeleteOption,
	option *bce.SignOption) (*CreateTaskResp, error) {
	if clusterID == "" {
		return nil, fmt.Errorf("empty clusterID")
	}

	if instanceGroupID == "" {
		return nil, fmt.Errorf("empty instanceGroupID")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	putContent, err := json.Marshal(RemoveInstancesFromInstanceGroup{
		CleanPolicy:          cleanPolicy,
		InstancesToBeRemoved: instancesToBeRemoved,
		DeleteOption:         deleteOption,
	})
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("PUT", c.GetURL(fmt.Sprintf("cluster/%s/instancegroup/%s/scaledown",
		clusterID, instanceGroupID), params), bytes.NewBuffer(putContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r CreateTaskResp
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}
