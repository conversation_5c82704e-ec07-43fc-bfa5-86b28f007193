// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/11/05 14:39:00, by <EMAIL>, create
*/
/*
实现 CCE V2 SDK Plugin 相关测试方法
*/

package ccev2

import (
	"context"
	"testing"
)

func testInstallPlugin(t *testing.T) {
	err := client.InstallPlugin(context.TODO(), "cce-p7esjpl1", "cce-ingress-controller", nil)
	if err != nil {
		t.Errorf("InstallPlugin failed: %v", err)
		return
	}
}
