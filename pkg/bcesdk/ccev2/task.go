/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  task
 * @Version: 1.0.0
 * @Date: 2021/6/24 11:11 上午
 */
package ccev2

import (
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
)

// GetTask - 获取指定的Task
// PARAMS:
//   - ctx: The context to trace request
//   - taskType: Task 类型
//   - taskID: Task ID
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	*GetTaskResp: 获取 Task 结果
//	error: nil if succeed, error if fail
func (c *Client) GetTask(ctx context.Context, taskType, taskID string, option *bce.SignOption) (*GetTaskResp, error) {
	if taskType == "" {
		return nil, fmt.Errorf("empty taskType")
	}

	if taskID == "" {
		return nil, fmt.Errorf("empty taskID")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	req, err := bce.NewRequest("GET", c.GetURL(fmt.Sprintf("task/%s/%s", taskType, taskID), params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r GetTaskResp
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// ListTasks - 获取 Task 列表
// PARAMS:
//   - ctx: The context to trace request
//   - taskType: Task 类型
//   - listOption: 列表筛选参数
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	*ListTaskResp: 获取 Task 列表结果
//	error: nil if succeed, error if fail
func (c *Client) ListTasks(ctx context.Context, taskType string, listOption ListTaskOption, signOption *bce.SignOption) (*ListTaskResp, error) {
	if taskType == "" {
		return nil, fmt.Errorf("empty taskType")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	if listOption.targetID != "" {
		params["targetID"] = listOption.targetID
	}
	if listOption.pageNo > 0 && listOption.pageSize > 0 {
		params["pageNo"] = fmt.Sprintf("%d", listOption.pageNo)
		params["pageSize"] = fmt.Sprintf("%d", listOption.pageSize)
	}

	req, err := bce.NewRequest("GET", c.GetURL(fmt.Sprintf("tasks/%s", taskType), params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, signOption)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r ListTaskResp
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}
