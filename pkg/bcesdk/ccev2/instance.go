// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/04/06 12:52:00, by <EMAIL>, create
*/
/*
实现 CCE V2 SDK Instance 相关方法
*/

package ccev2

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/logger"
	ccetypes "icode.baidu.com/baidu/cprom/cloud-stack/pkg/types"
)

// CreateInstances - 新建节点
// PARAMS:
//   - ctx: The context to trace request
//   - args: []*ccetypes.InstanceGroupSpec
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	[]string: CCEInstanceID, CCE 标识 Node
//	error: nil if succeed, error if fail
func (c *Client) CreateInstances(ctx context.Context, clusterID string, args []*InstanceSet, option *bce.SignOption) (*CreateInstancesResponse, error) {
	if args == nil {
		return nil, fmt.Errorf("args is nil")
	}

	if clusterID == "" {
		return nil, fmt.Errorf("clusterID is empty")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("cluster/%s/instances", clusterID)
	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r CreateInstancesResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

func (c *Client) UpdateInstance(ctx context.Context, clusterID string, instanceID string,
	spec *ccetypes.InstanceSpec, option *bce.SignOption) (*UpdateInstancesResponse, error) {
	if clusterID == "" {
		return nil, fmt.Errorf("empty clusterID")
	}

	if instanceID == "" {
		return nil, fmt.Errorf("empty instanceID")
	}

	if spec == nil {
		return nil, fmt.Errorf("instance spec is nil")
	}

	putContent, err := json.Marshal(spec)
	if err != nil {
		return nil, err
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	url := fmt.Sprintf("cluster/%s/instance/%s", clusterID, instanceID)
	req, err := bce.NewRequest("PUT", c.GetURL(url, params), bytes.NewBuffer(putContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r UpdateInstancesResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// GetInstance - 获取 Instance 对象
// PARAMS:
//   - ctx: The context to trace request
//   - instanceID: BCC InstanceID 或 CCE InstanceID
//
// RETURNS:
//
//	GetInstanceResponse: Instance 返回
//	error: nil if succeed, error if fail
func (c *Client) GetInstance(ctx context.Context, clusterID, instanceID string,
	option *bce.SignOption) (*GetInstanceResponse, error) {
	if instanceID == "" {
		return nil, fmt.Errorf("instanceID is empty")
	}

	url := fmt.Sprintf("cluster/%s/instance/%s?includeK8SNode=true", clusterID, instanceID)
	req, err := bce.NewRequest("Get", c.GetURL(url, nil), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		logger.Errorf(ctx, "SendRequest failed: %s", err)
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		logger.Errorf(ctx, "GetBodyContent failed: %s", err)
		return nil, err
	}

	var r GetInstanceResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		logger.Errorf(ctx, "json.Unmarshal failed: %s", err)
		return nil, err
	}

	return &r, nil
}

// GetInstanceByNodeName - 根据集群中的 NodeName 获取 Instance 对象
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: CCE 集群的 ID
//   - nodeName: Kubernetes 集群中的节点名称
//
// RETURNS:
//
//	GetInstanceByNodeNameResponse: Instance 返回
//	error: nil if succeed, error if fail
func (c *Client) GetInstanceByNodeName(ctx context.Context, clusterID string, nodeName string, option *bce.SignOption) (*GetInstanceByNodeNameResponse, error) {
	if clusterID == "" {
		return nil, fmt.Errorf("clusterID is empty")
	}
	if nodeName == "" {
		return nil, fmt.Errorf("nodeName is empty")
	}

	url := fmt.Sprintf("cluster/%s/instance/node/%s", clusterID, nodeName)
	req, err := bce.NewRequest("Get", c.GetURL(url, nil), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		logger.Errorf(ctx, "SendRequest failed: %s", err)
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		logger.Errorf(ctx, "GetBodyContent failed: %s", err)
		return nil, err
	}

	var r GetInstanceByNodeNameResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		logger.Errorf(ctx, "json.Unmarshal failed: %s", err)
		return nil, err
	}

	return &r, nil
}

// DeleteInstances - 通过 CCEInstanceIDs 或 BCCInstanceIDs 删除节点
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: 集群 ID
//   - cceInstanceIDs: []string
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) DeleteInstances(ctx context.Context, clusterID string, args *DeleteInstancesRequest, option *bce.SignOption) (*DeleteInstancesResponse, error) {
	if clusterID == "" {
		return nil, fmt.Errorf("clusterID is empty")
	}

	if args == nil {
		return nil, fmt.Errorf("DeleteNodeArs is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("cluster/%s/instances", clusterID)
	req, err := bce.NewRequest("PUT", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r DeleteInstancesResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// ListInstancesByPage - 根据 keywordType 和 keyword 获取 Instance 列表
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: 集群 ID
//   - args: 获取集群节点列表参数
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) ListInstancesByPage(ctx context.Context, clusterID string,
	args *ListInstancesByPageParams, option *bce.SignOption) (*ListInstancesResponse, error) {
	if clusterID == "" {
		return nil, fmt.Errorf("clusterID is empty")
	}

	var params map[string]string
	if args != nil {
		params = map[string]string{
			"keywordType":             string(args.KeywordType),
			"keyword":                 args.Keyword,
			"orderBy":                 string(args.OrderBy),
			"order":                   string(args.Order),
			"pageNo":                  strconv.Itoa(args.PageNo),
			"pageSize":                strconv.Itoa(args.PageSize),
			"enableInternalFields":    strconv.FormatBool(args.EnableInternalFields),
			"enableUpgradeNodeFields": strconv.FormatBool(args.EnableUpgradeNodeFields),
			"clusterRole":             string(args.ClusterRole),
			"becRegion":               args.BECRegion,
			"ipList":                  args.IPList,
		}
	}

	url := fmt.Sprintf("cluster/%s/instances", clusterID)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r ListInstancesResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// ListInstancesByInstanceGroupID - 获取节点组内 Instance 列表
// PARAMS:
//   - ctx: The context to trace request
//   - clusterID: 集群 ID
//   - instanceGroupID: 节点组 ID
//   - pageNo: 页码，如果为0就不分页
//   - pageSize: 每页结果数，如果为0就不分页
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) ListInstancesByInstanceGroupID(ctx context.Context, clusterID string,
	instanceGroupID string, pageNo, pageSize int, option *bce.SignOption) (*ListInstancesByInstanceGroupIDResponse, error) {
	if clusterID == "" {
		return nil, fmt.Errorf("clusterID is empty")
	}

	if instanceGroupID == "" {
		return nil, fmt.Errorf("instanceGroupID is empty")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	if pageSize > 0 && pageNo > 0 {
		params["pageSize"] = strconv.Itoa(pageSize)
		params["pageNo"] = strconv.Itoa(pageNo)
	}

	url := fmt.Sprintf("cluster/%s/instancegroup/%s/instances", clusterID, instanceGroupID)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r ListInstancesByInstanceGroupIDResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

func (c *Client) CordonInstances(ctx context.Context, cordonReq *CordonNodesRequest, option *bce.SignOption) error {
	if cordonReq == nil {
		return fmt.Errorf("DeleteNodeArs is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(cordonReq)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("POST", c.GetURL("/nodes/cordon", params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	if err != nil {
		return err
	}

	return nil
}

// ResetInstanceRetryCount - Set Instance.Status.RetryCount to 0
func (c *Client) ResetInstanceRetryCount(ctx context.Context, clusterID, cceInstanceID string, option *bce.SignOption) error {
	if clusterID == "" {
		return fmt.Errorf("clusterID is empty")
	}

	if cceInstanceID == "" {
		return fmt.Errorf("cceInstanceID is empty")
	}

	url := fmt.Sprintf("cluster/%s/instance/%s/retry", clusterID, cceInstanceID)
	req, err := bce.NewRequest("PUT", c.GetURL(url, nil), nil)
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

// SyncInstance - 同步集群节点的机器信息
func (c *Client) SyncInstance(ctx context.Context, clusterID string, option *bce.SignOption) (*SyncInstancesResponse, error) {
	if clusterID == "" {
		return nil, fmt.Errorf("clusterID is empty")
	}

	url := fmt.Sprintf("sync/cluster/%s/instances", clusterID)
	req, err := bce.NewRequest("POST", c.GetURL(url, nil), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var syncResp SyncInstancesResponse
	err = json.Unmarshal(bodyContent, &syncResp)
	if err != nil {
		return nil, err
	}

	return &syncResp, nil
}
