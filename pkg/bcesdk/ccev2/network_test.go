package ccev2

import (
	ccetypes "icode.baidu.com/baidu/cprom/cloud-stack/pkg/types"
	"testing"
)

func TestCheckContainerNetworkCIDRRequest_Validate(t *testing.T) {
	type fields struct {
		VPCID             string
		VPCCIDR           string
		VPCCIDRIPv6       string
		ContainerCIDR     string
		ContainerCIDRIPv6 string
		ClusterIPCIDR     string
		ClusterIPCIDRIPv6 string
		MaxPodsPerNode    int
		IPVersion         ccetypes.ContainerNetworkIPType
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "vpcId is empty",
			fields: fields{
				VPCID: "",
			},
			wantErr: true,
		},
		{
			name: "invalid ip version",
			fields: fields{
				VPCID:     "xxx",
				IPVersion: ccetypes.ContainerNetworkIPType("IPV5"),
			},
			wantErr: true,
		},
		{
			name: "valid case",
			fields: fields{
				VPCID:             "xxx",
				VPCCIDR:           "10.0.0.0/8",
				VPCCIDRIPv6:       "fc00::/9",
				ContainerCIDR:     "10.0.0.0/9",
				ContainerCIDRIPv6: "fc00::/19",
				ClusterIPCIDR:     "10.0.0.0/9",
				ClusterIPCIDRIPv6: "fc00::/19",
				IPVersion:         ccetypes.ContainerNetworkIPTypeDualStack,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := &CheckContainerNetworkCIDRRequest{
				VPCID:             tt.fields.VPCID,
				VPCCIDR:           tt.fields.VPCCIDR,
				VPCCIDRIPv6:       tt.fields.VPCCIDRIPv6,
				ContainerCIDR:     tt.fields.ContainerCIDR,
				ContainerCIDRIPv6: tt.fields.ContainerCIDRIPv6,
				ClusterIPCIDR:     tt.fields.ClusterIPCIDR,
				ClusterIPCIDRIPv6: tt.fields.ClusterIPCIDRIPv6,
				MaxPodsPerNode:    tt.fields.MaxPodsPerNode,
				IPVersion:         tt.fields.IPVersion,
			}
			if err := req.Validate(); (err != nil) != tt.wantErr {
				t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestRecommendContainerNetworkCIDRRequest_Validate(t *testing.T) {
	type fields struct {
		K8SVersion                   ccetypes.K8SVersion
		IPVersion                    ccetypes.ContainerNetworkIPType
		VPCID                        string
		VPCCIDR                      string
		VPCCIDRIPv6                  string
		ClusterMaxNodeNum            int
		MaxPodsPerNode               int
		ContainerPrivateNetCIDRs     []PrivateNetString
		ContainerPrivateNetCIDRIPv6s []PrivateNetString
		ClusterMaxServiceNum         int
		ClusterIPPrivateNetCIDRs     []PrivateNetString
		ClusterIPPrivateNetCIDRIPv6s []PrivateNetString
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "invalid max pods per node",
			fields: fields{
				MaxPodsPerNode: -1,
			},
			wantErr: true,
		},
		{
			name: "invalid max node num",
			fields: fields{
				MaxPodsPerNode:    64,
				ClusterMaxNodeNum: -1,
			},
			wantErr: true,
		},
		{
			name: "invalid max node num",
			fields: fields{
				K8SVersion:                   ccetypes.K8S_1_16_8,
				IPVersion:                    ccetypes.ContainerNetworkIPTypeDualStack,
				VPCID:                        "xx",
				ClusterMaxNodeNum:            64,
				MaxPodsPerNode:               32,
				ContainerPrivateNetCIDRs:     []PrivateNetString{PrivateIPv4Net10},
				ContainerPrivateNetCIDRIPv6s: []PrivateNetString{PrivateIPv6Net},
				ClusterMaxServiceNum:         1024,
				ClusterIPPrivateNetCIDRs:     []PrivateNetString{PrivateIPv4Net10},
				ClusterIPPrivateNetCIDRIPv6s: []PrivateNetString{PrivateIPv6Net},
				VPCCIDR:                      "10.0.0.0/8",
				VPCCIDRIPv6:                  "fc00::/9",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := &RecommendContainerNetworkCIDRRequest{
				K8SVersion:                   tt.fields.K8SVersion,
				IPVersion:                    tt.fields.IPVersion,
				VPCID:                        tt.fields.VPCID,
				VPCCIDR:                      tt.fields.VPCCIDR,
				VPCCIDRIPv6:                  tt.fields.VPCCIDRIPv6,
				ClusterMaxNodeNum:            tt.fields.ClusterMaxNodeNum,
				MaxPodsPerNode:               tt.fields.MaxPodsPerNode,
				ContainerPrivateNetCIDRs:     tt.fields.ContainerPrivateNetCIDRs,
				ContainerPrivateNetCIDRIPv6s: tt.fields.ContainerPrivateNetCIDRIPv6s,
				ClusterMaxServiceNum:         tt.fields.ClusterMaxServiceNum,
				ClusterIPPrivateNetCIDRs:     tt.fields.ClusterIPPrivateNetCIDRs,
				ClusterIPPrivateNetCIDRIPv6s: tt.fields.ClusterIPPrivateNetCIDRIPv6s,
			}
			if err := req.Validate(); (err != nil) != tt.wantErr {
				t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCheckClusterIPCIDRequest_Validate(t *testing.T) {
	type fields struct {
		VPCID             string
		VPCCIDR           string
		VPCCIDRIPv6       string
		ClusterIPCIDR     string
		ClusterIPCIDRIPv6 string
		IPVersion         ccetypes.ContainerNetworkIPType
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			fields: fields{
				VPCID:             "xxx",
				VPCCIDR:           "10.0.0.0/8",
				VPCCIDRIPv6:       "fc00::/9",
				ClusterIPCIDR:     "10.0.0.0/12",
				ClusterIPCIDRIPv6: "fc00::/110",
				IPVersion:         ccetypes.ContainerNetworkIPTypeDualStack,
			},
			wantErr: false,
		},

		{
			fields: fields{
				VPCID:         "xxx",
				VPCCIDR:       "10.0.0.0/8",
				ClusterIPCIDR: "10.0.0.0/8",
				IPVersion:     ccetypes.ContainerNetworkIPTypeIPv4,
			},
			wantErr: true,
		},

		{
			fields: fields{
				VPCID:             "xxx",
				VPCCIDRIPv6:       "fc00::/9",
				ClusterIPCIDRIPv6: "fc00::/9",
				IPVersion:         ccetypes.ContainerNetworkIPTypeIPv6,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := &CheckClusterIPCIDRequest{
				VPCID:             tt.fields.VPCID,
				VPCCIDR:           tt.fields.VPCCIDR,
				VPCCIDRIPv6:       tt.fields.VPCCIDRIPv6,
				ClusterIPCIDR:     tt.fields.ClusterIPCIDR,
				ClusterIPCIDRIPv6: tt.fields.ClusterIPCIDRIPv6,
				IPVersion:         tt.fields.IPVersion,
			}
			if err := req.Validate(); (err != nil) != tt.wantErr {
				t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
