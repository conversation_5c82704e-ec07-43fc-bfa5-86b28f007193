// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/03/28 13:11:00, by <EMAIL>, create
*/
/*
实现 CCE V2 SDK Cluster 相关测试方法
*/

package ccev2

import (
	"context"
	"encoding/json"
	utils2 "icode.baidu.com/baidu/cprom/cloud-stack/cprom-common/utils"
	"testing"
	"time"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bccimage"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/retrypolicy"
	ccetypes "icode.baidu.com/baidu/cprom/cloud-stack/pkg/types"
)

var whConfig = bce.Config{
	Credentials: bce.NewCredentials("bcb4a6085db942ebb4562435d3da12b9", "98ae9eda4d184156bdf29a9075aa9a36"),
	Checksum:    true,
	Timeout:     30 * time.Second,
	Region:      "fwh",
	Endpoint:    "http://10.158.132.178:8080/api/v2",
	RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
}

var gzTestConfig = bce.Config{
	Credentials: bce.NewCredentials("e0c68be8495540f280b3e9ef03ec25d2", "b599d5f4f30e41bcb0e1482c9de65bd6"),
	Checksum:    true,
	Timeout:     30 * time.Second,
	Region:      "gztest",
	Endpoint:    "http://10.164.32.142:8793/api/cce/service/v2",
	RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
}

var gzTestLililiConfig = bce.Config{
	Credentials: bce.NewCredentials("6e249e45efa842118f92e4a68f31bb6a", "af4932b1e1de49f7a246f258ca530394"),
	Checksum:    true,
	Timeout:     30 * time.Second,
	Region:      "gztest",
	Endpoint:    "http://10.164.32.142:8793/api/cce/service/v2",
	RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
}

var bdblTestLililiConfig = bce.Config{
	Credentials: bce.NewCredentials("6e249e45efa842118f92e4a68f31bb6a", "af4932b1e1de49f7a246f258ca530394"),
	Checksum:    true,
	Timeout:     30 * time.Second,
	Region:      "bdbl",
	Endpoint:    "http://*************:8793/api/cce/service/v2",
	RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
}

var client = NewClient(&gzTestConfig)

func testCreateExistedMasterCluster(t *testing.T) {
	rebuildFlag := true
	// CreateCluster 参数
	args := &CreateClusterRequest{
		ClusterSpec: &ccetypes.ClusterSpec{
			ClusterName: "chenhuan-existed-cluster",
			Description: "集群描述",
			K8SVersion:  ccetypes.K8S_1_16_3,
			RuntimeType: ccetypes.RuntimeTypeDocker,
			VPCID:       "vpc-7k0buknui19g",
			MasterConfig: ccetypes.MasterConfig{
				MasterType:            ccetypes.MasterTypeCustom,
				ClusterBLBVPCSubnetID: "sbn-ggr9sh9kpb6z",
			},
			ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
				Mode:                 ccetypes.ContainerNetworkModeKubenet,
				LBServiceVPCSubnetID: "sbn-ggr9sh9kpb6z",
				ClusterPodCIDR:       "**********/24",
				ClusterIPServiceCIDR: "***********/24",
			},
		},
		MasterSpecs: []*InstanceSet{
			&InstanceSet{
				InstanceSpec: ccetypes.InstanceSpec{
					AdminPassword: "jhkj4%w@oip",
					Existed:       true,
					ExistedOption: ccetypes.ExistedOption{
						Rebuild:           &(rebuildFlag),
						ExistedInstanceID: "i-qJ7qdZcG",
					},
					MachineType: ccetypes.MachineTypeBCC,
					InstanceOS: ccetypes.InstanceOS{
						ImageType: bccimage.ImageTypeSystem,
						OSType:    bccimage.OSTypeLinux,
						OSName:    bccimage.OSNameCentOS,
						OSVersion: "7.5",
						OSArch:    "x86_64 (64bit)",
					},
				},
			},
			// &ccetypes.InstanceGroupSpec{
			// 	InstanceSpec: ccetypes.InstanceSpec{
			// 		AdminPassword: "jhkj4%w@oip",
			// 		Existed:       true,
			// 		ExistedOption: ccetypes.ExistedOption{
			// 			Rebuild:           &(rebuildFlag),
			// 			ExistedInstanceID: "i-h5uM6bEm",
			// 		},
			// 		InstanceOS: ccetypes.InstanceOS{
			// 			ImageType: bccimage.ImageTypeSystem,
			// 			OSType:    bccimage.OSTypeLinux,
			// 			OSName:    bccimage.OSNameCentOS,
			// 			OSVersion: "7.5",
			// 			OSArch:    "x86_64 (64bit)",
			// 		},
			// 	},
			// },
			// &ccetypes.InstanceGroupSpec{
			// 	InstanceSpec: ccetypes.InstanceSpec{
			// 		AdminPassword: "jhkj4%w@oip",
			// 		Existed:       true,
			// 		ExistedOption: ccetypes.ExistedOption{
			// 			Rebuild:           &(rebuildFlag),
			// 			ExistedInstanceID: "i-IcF2O7cW",
			// 		},
			// 		InstanceOS: ccetypes.InstanceOS{
			// 			ImageType: bccimage.ImageTypeSystem,
			// 			OSType:    bccimage.OSTypeLinux,
			// 			OSName:    bccimage.OSNameCentOS,
			// 			OSVersion: "7.5",
			// 			OSArch:    "x86_64 (64bit)",
			// 		},
			// 	},
			// },
		},
		NodeSpecs: []*InstanceSet{
			// &ccetypes.InstanceGroupSpec{
			// 	InstanceSpec: ccetypes.InstanceSpec{
			// 		AdminPassword: "jhkj4%w@oip",
			// 		Existed:       true,
			// 		ExistedOption: ccetypes.ExistedOption{
			// 			Rebuild:           &(rebuildFlag),
			// 			ExistedInstanceID: "i-2qFuTEgy",
			// 		},
			// 		InstanceOS: ccetypes.InstanceOS{
			// 			ImageType: bccimage.ImageTypeSystem,
			// 			OSType:    bccimage.OSTypeLinux,
			// 			OSName:    bccimage.OSNameCentOS,
			// 			OSVersion: "7.5",
			// 			OSArch:    "x86_64 (64bit)",
			// 		},
			// 	},
			// },
		},
	}

	// 创建集群
	resp, err := client.CreateCluster(context.TODO(), args, nil)
	if err != nil {
		t.Errorf("CreateCluster failed: %v", err)
		return
	}
	t.Errorf("CreateCluster success: %s", utils2.ToJSON(resp))
}

func testDeleteCluster(t *testing.T) {
	resp, err := client.DeleteCluster(context.TODO(), "cce-b50928u1", &DeleteOptions{
		MoveOut:           false,
		DeleteResource:    false,
		DeleteCDSSnapshot: false,
	}, nil)
	if err != nil {
		t.Errorf("DeleteCluster failed: %v", err)
		return
	}

	t.Logf(utils2.ToJSON(resp))
}

func testGetCluster(t *testing.T) {
	cluster, err := client.GetCluster(context.TODO(), "c-fiXiSWo", nil)
	if err != nil {
		t.Errorf("GetCluster failed: %v", err)
		return
	}

	t.Errorf("GetCluster success: %s", utils2.ToJSON(cluster))
}

func testGetClusterOfBBE(t *testing.T) {
	client.SetDebug(true)

	cluster, err := client.GetClusterOfBBE(context.TODO(), "cce-5u88kc8o", nil)
	if err != nil {
		t.Errorf("GetCluster failed: %v", err)
		return
	}

	t.Errorf("GetCluster success: %s", utils2.ToJSON(cluster))
}

func testResetClusterRetryCount(t *testing.T) {
	ctx := context.TODO()

	client := NewClient(&gzTestConfig)
	client.SetDebug(true)

	if err := client.ResetClusterRetryCount(ctx, "cce-w9bf1m2i", nil); err != nil {
		t.Errorf("ResetClusterRetryCount failed: %s", err)
	}

	t.Errorf("success")
}

func testClient_ListClusters(t *testing.T) {
	ctx := context.TODO()

	client := NewClient(&gzTestConfig)
	client.SetDebug(true)

	clusters, err := client.ListClusters(ctx, "", "", "", "", 1, 10000, nil)
	if err != nil {
		t.Errorf("ListClusters failed: %s", err)
	}

	t.Errorf("ListClusters succeeded: %s", utils2.ToJSON(clusters))
}

func TestClient_GetCluster(t *testing.T) {

	type fields struct {
		Client *bce.Client
	}
	type args struct {
		ctx       context.Context
		clusterID string
		option    *bce.SignOption
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
		wantNil bool
	}{
		{
			name:   "found",
			fields: fields{Client: bce.NewClient(&gzTestConfig)},
			args: args{
				ctx:       context.Background(),
				clusterID: "cce-zmyxl67c",
				option:    &bce.SignOption{},
			},
			wantNil: false,
			wantErr: false,
		},
		{
			name:   "not found",
			fields: fields{Client: bce.NewClient(&gzTestConfig)},
			args: args{
				ctx:       context.Background(),
				clusterID: "cce-zmyxl67c1",
				option:    &bce.SignOption{},
			},
			wantErr: true,
			wantNil: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				Client: tt.fields.Client,
			}
			got, err := c.GetCluster(tt.args.ctx, tt.args.clusterID, tt.args.option)
			if err != nil {
				errJson, _ := json.Marshal(err)
				t.Log(string(errJson))
			}
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCluster() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if (got == nil) != tt.wantNil {
				t.Errorf("GetCluster() got = %v, want not nil", got)
			}
		})
	}
}
