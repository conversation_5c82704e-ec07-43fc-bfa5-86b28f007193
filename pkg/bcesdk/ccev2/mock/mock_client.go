// Code generated by MockGen. DO NOT EDIT.
// Source: ./types.go

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
	ccev2 "icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/ccev2"
	ccetypes "icode.baidu.com/baidu/cprom/cloud-stack/pkg/types"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// CheckContainerNetworkCIDR mocks base method.
func (m *MockInterface) CheckContainerNetworkCIDR(ctx context.Context, args *ccev2.CheckContainerNetworkCIDRRequest, option *bce.SignOption) (*ccev2.CheckContainerNetworkCIDRResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckContainerNetworkCIDR", ctx, args, option)
	ret0, _ := ret[0].(*ccev2.CheckContainerNetworkCIDRResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckContainerNetworkCIDR indicates an expected call of CheckContainerNetworkCIDR.
func (mr *MockInterfaceMockRecorder) CheckContainerNetworkCIDR(ctx, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckContainerNetworkCIDR", reflect.TypeOf((*MockInterface)(nil).CheckContainerNetworkCIDR), ctx, args, option)
}

// CordonInstances mocks base method.
func (m *MockInterface) CordonInstances(ctx context.Context, cordonReq *ccev2.CordonNodesRequest, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CordonInstances", ctx, cordonReq, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// CordonInstances indicates an expected call of CordonInstances.
func (mr *MockInterfaceMockRecorder) CordonInstances(ctx, cordonReq, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CordonInstances", reflect.TypeOf((*MockInterface)(nil).CordonInstances), ctx, cordonReq, option)
}

// CreateCluster mocks base method.
func (m *MockInterface) CreateCluster(ctx context.Context, args *ccev2.CreateClusterRequest, option *bce.SignOption) (*ccev2.CreateClusterResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateCluster", ctx, args, option)
	ret0, _ := ret[0].(*ccev2.CreateClusterResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateCluster indicates an expected call of CreateCluster.
func (mr *MockInterfaceMockRecorder) CreateCluster(ctx, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCluster", reflect.TypeOf((*MockInterface)(nil).CreateCluster), ctx, args, option)
}

// CreateInstanceGroup mocks base method.
func (m *MockInterface) CreateInstanceGroup(ctx context.Context, clusterID string, request *ccev2.CreateInstanceGroupRequest, option *bce.SignOption) (*ccev2.CreateInstanceGroupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInstanceGroup", ctx, clusterID, request, option)
	ret0, _ := ret[0].(*ccev2.CreateInstanceGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateInstanceGroup indicates an expected call of CreateInstanceGroup.
func (mr *MockInterfaceMockRecorder) CreateInstanceGroup(ctx, clusterID, request, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInstanceGroup", reflect.TypeOf((*MockInterface)(nil).CreateInstanceGroup), ctx, clusterID, request, option)
}

// CreateInstances mocks base method.
func (m *MockInterface) CreateInstances(ctx context.Context, clusterID string, args []*ccev2.InstanceSet, option *bce.SignOption) (*ccev2.CreateInstancesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInstances", ctx, clusterID, args, option)
	ret0, _ := ret[0].(*ccev2.CreateInstancesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateInstances indicates an expected call of CreateInstances.
func (mr *MockInterfaceMockRecorder) CreateInstances(ctx, clusterID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInstances", reflect.TypeOf((*MockInterface)(nil).CreateInstances), ctx, clusterID, args, option)
}

// CreateRBACKubeConfig mocks base method.
func (m *MockInterface) CreateRBACKubeConfig(ctx context.Context, request *ccev2.KubeConfigRequest, option *bce.SignOption) (*ccev2.CreateRBACResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRBACKubeConfig", ctx, request, option)
	ret0, _ := ret[0].(*ccev2.CreateRBACResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRBACKubeConfig indicates an expected call of CreateRBACKubeConfig.
func (mr *MockInterfaceMockRecorder) CreateRBACKubeConfig(ctx, request, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRBACKubeConfig", reflect.TypeOf((*MockInterface)(nil).CreateRBACKubeConfig), ctx, request, option)
}

// CreateScaleDownInstanceGroupByCleanPolicy mocks base method.
func (m *MockInterface) CreateScaleDownInstanceGroupByCleanPolicy(ctx context.Context, clusterID, instanceGroupID string, instancesToBeRemoved []string, cleanPolicy ccev2.CleanPolicy, deleteOption *ccetypes.DeleteOption, option *bce.SignOption) (*ccev2.CreateTaskResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateScaleDownInstanceGroupByCleanPolicy", ctx, clusterID, instanceGroupID, instancesToBeRemoved, cleanPolicy, deleteOption, option)
	ret0, _ := ret[0].(*ccev2.CreateTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateScaleDownInstanceGroupByCleanPolicy indicates an expected call of CreateScaleDownInstanceGroupByCleanPolicy.
func (mr *MockInterfaceMockRecorder) CreateScaleDownInstanceGroupByCleanPolicy(ctx, clusterID, instanceGroupID, instancesToBeRemoved, cleanPolicy, deleteOption, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateScaleDownInstanceGroupByCleanPolicy", reflect.TypeOf((*MockInterface)(nil).CreateScaleDownInstanceGroupByCleanPolicy), ctx, clusterID, instanceGroupID, instancesToBeRemoved, cleanPolicy, deleteOption, option)
}

// CreateScaleDownInstanceGroupTask mocks base method.
func (m *MockInterface) CreateScaleDownInstanceGroupTask(ctx context.Context, clusterID, instanceGroupID string, instancesToBeRemoved []string, option *bce.SignOption) (*ccev2.CreateTaskResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateScaleDownInstanceGroupTask", ctx, clusterID, instanceGroupID, instancesToBeRemoved, option)
	ret0, _ := ret[0].(*ccev2.CreateTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateScaleDownInstanceGroupTask indicates an expected call of CreateScaleDownInstanceGroupTask.
func (mr *MockInterfaceMockRecorder) CreateScaleDownInstanceGroupTask(ctx, clusterID, instanceGroupID, instancesToBeRemoved, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateScaleDownInstanceGroupTask", reflect.TypeOf((*MockInterface)(nil).CreateScaleDownInstanceGroupTask), ctx, clusterID, instanceGroupID, instancesToBeRemoved, option)
}

// CreateScaleUpInstanceGroupTask mocks base method.
func (m *MockInterface) CreateScaleUpInstanceGroupTask(ctx context.Context, clusterID, instanceGroupID string, targetReplicas int, option *bce.SignOption) (*ccev2.CreateTaskResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateScaleUpInstanceGroupTask", ctx, clusterID, instanceGroupID, targetReplicas, option)
	ret0, _ := ret[0].(*ccev2.CreateTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateScaleUpInstanceGroupTask indicates an expected call of CreateScaleUpInstanceGroupTask.
func (mr *MockInterfaceMockRecorder) CreateScaleUpInstanceGroupTask(ctx, clusterID, instanceGroupID, targetReplicas, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateScaleUpInstanceGroupTask", reflect.TypeOf((*MockInterface)(nil).CreateScaleUpInstanceGroupTask), ctx, clusterID, instanceGroupID, targetReplicas, option)
}

// CreateWorkflow mocks base method.
func (m *MockInterface) CreateWorkflow(ctx context.Context, clusterID string, args *ccev2.CreateWorkflowRequest, option *bce.SignOption) (*ccev2.CreateWorkflowResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateWorkflow", ctx, clusterID, args, option)
	ret0, _ := ret[0].(*ccev2.CreateWorkflowResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateWorkflow indicates an expected call of CreateWorkflow.
func (mr *MockInterfaceMockRecorder) CreateWorkflow(ctx, clusterID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateWorkflow", reflect.TypeOf((*MockInterface)(nil).CreateWorkflow), ctx, clusterID, args, option)
}

// DeleteCluster mocks base method.
func (m *MockInterface) DeleteCluster(ctx context.Context, clusterID string, options *ccev2.DeleteOptions, option *bce.SignOption) (*ccev2.DeleteClusterResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteCluster", ctx, clusterID, options, option)
	ret0, _ := ret[0].(*ccev2.DeleteClusterResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteCluster indicates an expected call of DeleteCluster.
func (mr *MockInterfaceMockRecorder) DeleteCluster(ctx, clusterID, options, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteCluster", reflect.TypeOf((*MockInterface)(nil).DeleteCluster), ctx, clusterID, options, option)
}

// DeleteInstanceGroup mocks base method.
func (m *MockInterface) DeleteInstanceGroup(ctx context.Context, clusterID, instanceGroupID string, deleteInstances bool, option *bce.SignOption) (*ccev2.DeleteInstanceGroupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteInstanceGroup", ctx, clusterID, instanceGroupID, deleteInstances, option)
	ret0, _ := ret[0].(*ccev2.DeleteInstanceGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteInstanceGroup indicates an expected call of DeleteInstanceGroup.
func (mr *MockInterfaceMockRecorder) DeleteInstanceGroup(ctx, clusterID, instanceGroupID, deleteInstances, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteInstanceGroup", reflect.TypeOf((*MockInterface)(nil).DeleteInstanceGroup), ctx, clusterID, instanceGroupID, deleteInstances, option)
}

// DeleteInstances mocks base method.
func (m *MockInterface) DeleteInstances(ctx context.Context, clusterID string, args *ccev2.DeleteInstancesRequest, option *bce.SignOption) (*ccev2.DeleteInstancesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteInstances", ctx, clusterID, args, option)
	ret0, _ := ret[0].(*ccev2.DeleteInstancesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteInstances indicates an expected call of DeleteInstances.
func (mr *MockInterfaceMockRecorder) DeleteInstances(ctx, clusterID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteInstances", reflect.TypeOf((*MockInterface)(nil).DeleteInstances), ctx, clusterID, args, option)
}

// DeleteWorkflow mocks base method.
func (m *MockInterface) DeleteWorkflow(ctx context.Context, clusterID, workflowID string, option *bce.SignOption) (*ccev2.DeleteWorkflowResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteWorkflow", ctx, clusterID, workflowID, option)
	ret0, _ := ret[0].(*ccev2.DeleteWorkflowResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteWorkflow indicates an expected call of DeleteWorkflow.
func (mr *MockInterfaceMockRecorder) DeleteWorkflow(ctx, clusterID, workflowID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteWorkflow", reflect.TypeOf((*MockInterface)(nil).DeleteWorkflow), ctx, clusterID, workflowID, option)
}

// GetAdminKubeConfig mocks base method.
func (m *MockInterface) GetAdminKubeConfig(ctx context.Context, clusterID string, kubeConfigType ccev2.KubeConfigType, option *bce.SignOption) (*ccev2.GetKubeConfigResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAdminKubeConfig", ctx, clusterID, kubeConfigType, option)
	ret0, _ := ret[0].(*ccev2.GetKubeConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAdminKubeConfig indicates an expected call of GetAdminKubeConfig.
func (mr *MockInterfaceMockRecorder) GetAdminKubeConfig(ctx, clusterID, kubeConfigType, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAdminKubeConfig", reflect.TypeOf((*MockInterface)(nil).GetAdminKubeConfig), ctx, clusterID, kubeConfigType, option)
}

// GetBindingCpromInstances mocks base method.
func (m *MockInterface) GetBindingCpromInstances(ctx context.Context, args *ccev2.ListBindingCPromInstanceResquest, option *bce.SignOption) (*ccev2.ListBindingCPromInstanceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBindingCpromInstances", ctx, args, option)
	ret0, _ := ret[0].(*ccev2.ListBindingCPromInstanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBindingCpromInstances indicates an expected call of GetBindingCpromInstances.
func (mr *MockInterfaceMockRecorder) GetBindingCpromInstances(ctx, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBindingCpromInstances", reflect.TypeOf((*MockInterface)(nil).GetBindingCpromInstances), ctx, args, option)
}

// GetCluster mocks base method.
func (m *MockInterface) GetCluster(ctx context.Context, clusterID string, option *bce.SignOption) (*ccev2.GetClusterResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCluster", ctx, clusterID, option)
	ret0, _ := ret[0].(*ccev2.GetClusterResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCluster indicates an expected call of GetCluster.
func (mr *MockInterfaceMockRecorder) GetCluster(ctx, clusterID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCluster", reflect.TypeOf((*MockInterface)(nil).GetCluster), ctx, clusterID, option)
}

// GetClusterNodeQuota mocks base method.
func (m *MockInterface) GetClusterNodeQuota(ctx context.Context, clusterID string, option *bce.SignOption) (*ccev2.GetQuotaResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterNodeQuota", ctx, clusterID, option)
	ret0, _ := ret[0].(*ccev2.GetQuotaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterNodeQuota indicates an expected call of GetClusterNodeQuota.
func (mr *MockInterfaceMockRecorder) GetClusterNodeQuota(ctx, clusterID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterNodeQuota", reflect.TypeOf((*MockInterface)(nil).GetClusterNodeQuota), ctx, clusterID, option)
}

// GetClusterQuota mocks base method.
func (m *MockInterface) GetClusterQuota(ctx context.Context, option *bce.SignOption) (*ccev2.GetQuotaResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterQuota", ctx, option)
	ret0, _ := ret[0].(*ccev2.GetQuotaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterQuota indicates an expected call of GetClusterQuota.
func (mr *MockInterfaceMockRecorder) GetClusterQuota(ctx, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterQuota", reflect.TypeOf((*MockInterface)(nil).GetClusterQuota), ctx, option)
}

// GetEtcdCert mocks base method.
func (m *MockInterface) GetEtcdCert(ctx context.Context, clusterID string, option *bce.SignOption) (*ccev2.GetEtcdCertsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEtcdCert", ctx, clusterID, option)
	ret0, _ := ret[0].(*ccev2.GetEtcdCertsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEtcdCert indicates an expected call of GetEtcdCert.
func (mr *MockInterfaceMockRecorder) GetEtcdCert(ctx, clusterID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEtcdCert", reflect.TypeOf((*MockInterface)(nil).GetEtcdCert), ctx, clusterID, option)
}

// GetInstance mocks base method.
func (m *MockInterface) GetInstance(ctx context.Context, clusterID, instanceID string, option *bce.SignOption) (*ccev2.GetInstanceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstance", ctx, clusterID, instanceID, option)
	ret0, _ := ret[0].(*ccev2.GetInstanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstance indicates an expected call of GetInstance.
func (mr *MockInterfaceMockRecorder) GetInstance(ctx, clusterID, instanceID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstance", reflect.TypeOf((*MockInterface)(nil).GetInstance), ctx, clusterID, instanceID, option)
}

// GetInstanceByNodeName mocks base method.
func (m *MockInterface) GetInstanceByNodeName(ctx context.Context, clusterID, nodeName string, option *bce.SignOption) (*ccev2.GetInstanceByNodeNameResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceByNodeName", ctx, clusterID, nodeName, option)
	ret0, _ := ret[0].(*ccev2.GetInstanceByNodeNameResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceByNodeName indicates an expected call of GetInstanceByNodeName.
func (mr *MockInterfaceMockRecorder) GetInstanceByNodeName(ctx, clusterID, nodeName, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceByNodeName", reflect.TypeOf((*MockInterface)(nil).GetInstanceByNodeName), ctx, clusterID, nodeName, option)
}

// GetInstanceGroup mocks base method.
func (m *MockInterface) GetInstanceGroup(ctx context.Context, clusterID, instanceGroupID string, option *bce.SignOption) (*ccev2.GetInstanceGroupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceGroup", ctx, clusterID, instanceGroupID, option)
	ret0, _ := ret[0].(*ccev2.GetInstanceGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceGroup indicates an expected call of GetInstanceGroup.
func (mr *MockInterfaceMockRecorder) GetInstanceGroup(ctx, clusterID, instanceGroupID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceGroup", reflect.TypeOf((*MockInterface)(nil).GetInstanceGroup), ctx, clusterID, instanceGroupID, option)
}

// GetWorkflow mocks base method.
func (m *MockInterface) GetWorkflow(ctx context.Context, clusterID, workflowID string, option *bce.SignOption) (*ccev2.GetWorkflowResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkflow", ctx, clusterID, workflowID, option)
	ret0, _ := ret[0].(*ccev2.GetWorkflowResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorkflow indicates an expected call of GetWorkflow.
func (mr *MockInterfaceMockRecorder) GetWorkflow(ctx, clusterID, workflowID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkflow", reflect.TypeOf((*MockInterface)(nil).GetWorkflow), ctx, clusterID, workflowID, option)
}

// InstallPlugin mocks base method.
func (m *MockInterface) InstallPlugin(ctx context.Context, clusterID, pluginName string, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InstallPlugin", ctx, clusterID, pluginName, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// InstallPlugin indicates an expected call of InstallPlugin.
func (mr *MockInterfaceMockRecorder) InstallPlugin(ctx, clusterID, pluginName, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InstallPlugin", reflect.TypeOf((*MockInterface)(nil).InstallPlugin), ctx, clusterID, pluginName, option)
}

// ListClusters mocks base method.
func (m *MockInterface) ListClusters(ctx context.Context, keywordType ccev2.ClusterKeywordType, keyword string, orderBy ccev2.ClusterOrderBy, order ccev2.Order, pageNum, pageSize int, option *bce.SignOption) (*ccev2.ListClustersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListClusters", ctx, keywordType, keyword, orderBy, order, pageNum, pageSize, option)
	ret0, _ := ret[0].(*ccev2.ListClustersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListClusters indicates an expected call of ListClusters.
func (mr *MockInterfaceMockRecorder) ListClusters(ctx, keywordType, keyword, orderBy, order, pageNum, pageSize, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListClusters", reflect.TypeOf((*MockInterface)(nil).ListClusters), ctx, keywordType, keyword, orderBy, order, pageNum, pageSize, option)
}

// ListInstanceGroups mocks base method.
func (m *MockInterface) ListInstanceGroups(ctx context.Context, clusterID string, listOption *ccev2.InstanceGroupListOption, option *bce.SignOption) (*ccev2.ListInstanceGroupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstanceGroups", ctx, clusterID, listOption, option)
	ret0, _ := ret[0].(*ccev2.ListInstanceGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstanceGroups indicates an expected call of ListInstanceGroups.
func (mr *MockInterfaceMockRecorder) ListInstanceGroups(ctx, clusterID, listOption, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstanceGroups", reflect.TypeOf((*MockInterface)(nil).ListInstanceGroups), ctx, clusterID, listOption, option)
}

// ListInstancesByInstanceGroupID mocks base method.
func (m *MockInterface) ListInstancesByInstanceGroupID(ctx context.Context, clusterID, instanceGroupID string, pageNo, pageSize int, option *bce.SignOption) (*ccev2.ListInstancesByInstanceGroupIDResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstancesByInstanceGroupID", ctx, clusterID, instanceGroupID, pageNo, pageSize, option)
	ret0, _ := ret[0].(*ccev2.ListInstancesByInstanceGroupIDResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstancesByInstanceGroupID indicates an expected call of ListInstancesByInstanceGroupID.
func (mr *MockInterfaceMockRecorder) ListInstancesByInstanceGroupID(ctx, clusterID, instanceGroupID, pageNo, pageSize, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstancesByInstanceGroupID", reflect.TypeOf((*MockInterface)(nil).ListInstancesByInstanceGroupID), ctx, clusterID, instanceGroupID, pageNo, pageSize, option)
}

// ListInstancesByPage mocks base method.
func (m *MockInterface) ListInstancesByPage(ctx context.Context, clusterID string, args *ccev2.ListInstancesByPageParams, option *bce.SignOption) (*ccev2.ListInstancesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstancesByPage", ctx, clusterID, args, option)
	ret0, _ := ret[0].(*ccev2.ListInstancesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstancesByPage indicates an expected call of ListInstancesByPage.
func (mr *MockInterfaceMockRecorder) ListInstancesByPage(ctx, clusterID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstancesByPage", reflect.TypeOf((*MockInterface)(nil).ListInstancesByPage), ctx, clusterID, args, option)
}

// ListNodesCanBeUpgradedByPage mocks base method.
func (m *MockInterface) ListNodesCanBeUpgradedByPage(ctx context.Context, clusterID string, option *bce.SignOption) (*ccev2.ListNodesCanBeUpgradedByPageResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListNodesCanBeUpgradedByPage", ctx, clusterID, option)
	ret0, _ := ret[0].(*ccev2.ListNodesCanBeUpgradedByPageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListNodesCanBeUpgradedByPage indicates an expected call of ListNodesCanBeUpgradedByPage.
func (mr *MockInterfaceMockRecorder) ListNodesCanBeUpgradedByPage(ctx, clusterID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListNodesCanBeUpgradedByPage", reflect.TypeOf((*MockInterface)(nil).ListNodesCanBeUpgradedByPage), ctx, clusterID, option)
}

// ListWorkflows mocks base method.
func (m *MockInterface) ListWorkflows(ctx context.Context, clusterID string, option *bce.SignOption) (*ccev2.ListWorkflowsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListWorkflows", ctx, clusterID, option)
	ret0, _ := ret[0].(*ccev2.ListWorkflowsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListWorkflows indicates an expected call of ListWorkflows.
func (mr *MockInterfaceMockRecorder) ListWorkflows(ctx, clusterID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListWorkflows", reflect.TypeOf((*MockInterface)(nil).ListWorkflows), ctx, clusterID, option)
}

// RecommendClusterIPCIDR mocks base method.
func (m *MockInterface) RecommendClusterIPCIDR(ctx context.Context, args *ccev2.RecommendClusterIPCIDRRequest, option *bce.SignOption) (*ccev2.RecommendClusterIPCIDRResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecommendClusterIPCIDR", ctx, args, option)
	ret0, _ := ret[0].(*ccev2.RecommendClusterIPCIDRResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecommendClusterIPCIDR indicates an expected call of RecommendClusterIPCIDR.
func (mr *MockInterfaceMockRecorder) RecommendClusterIPCIDR(ctx, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecommendClusterIPCIDR", reflect.TypeOf((*MockInterface)(nil).RecommendClusterIPCIDR), ctx, args, option)
}

// RecommendContainerCIDR mocks base method.
func (m *MockInterface) RecommendContainerCIDR(ctx context.Context, args *ccev2.RecommendContainerCIDRRequest, option *bce.SignOption) (*ccev2.RecommendContainerCIDRResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecommendContainerCIDR", ctx, args, option)
	ret0, _ := ret[0].(*ccev2.RecommendContainerCIDRResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecommendContainerCIDR indicates an expected call of RecommendContainerCIDR.
func (mr *MockInterfaceMockRecorder) RecommendContainerCIDR(ctx, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecommendContainerCIDR", reflect.TypeOf((*MockInterface)(nil).RecommendContainerCIDR), ctx, args, option)
}

// ResetClusterRetryCount mocks base method.
func (m *MockInterface) ResetClusterRetryCount(ctx context.Context, clusterID string, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResetClusterRetryCount", ctx, clusterID, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResetClusterRetryCount indicates an expected call of ResetClusterRetryCount.
func (mr *MockInterfaceMockRecorder) ResetClusterRetryCount(ctx, clusterID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetClusterRetryCount", reflect.TypeOf((*MockInterface)(nil).ResetClusterRetryCount), ctx, clusterID, option)
}

// ResetInstanceRetryCount mocks base method.
func (m *MockInterface) ResetInstanceRetryCount(ctx context.Context, clusterID, cceInstanceID string, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResetInstanceRetryCount", ctx, clusterID, cceInstanceID, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResetInstanceRetryCount indicates an expected call of ResetInstanceRetryCount.
func (mr *MockInterfaceMockRecorder) ResetInstanceRetryCount(ctx, clusterID, cceInstanceID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetInstanceRetryCount", reflect.TypeOf((*MockInterface)(nil).ResetInstanceRetryCount), ctx, clusterID, cceInstanceID, option)
}

// SetDebug mocks base method.
func (m *MockInterface) SetDebug(debug bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetDebug", debug)
}

// SetDebug indicates an expected call of SetDebug.
func (mr *MockInterfaceMockRecorder) SetDebug(debug interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDebug", reflect.TypeOf((*MockInterface)(nil).SetDebug), debug)
}

// SyncInstance mocks base method.
func (m *MockInterface) SyncInstance(ctx context.Context, clusterID string, option *bce.SignOption) (*ccev2.SyncInstancesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SyncInstance", ctx, clusterID, option)
	ret0, _ := ret[0].(*ccev2.SyncInstancesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SyncInstance indicates an expected call of SyncInstance.
func (mr *MockInterfaceMockRecorder) SyncInstance(ctx, clusterID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncInstance", reflect.TypeOf((*MockInterface)(nil).SyncInstance), ctx, clusterID, option)
}

// TargetK8SVersion mocks base method.
func (m *MockInterface) TargetK8SVersion(ctx context.Context, clusterID string, clusterRole ccetypes.ClusterRole, option *bce.SignOption) (*ccev2.TargetK8SVersionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TargetK8SVersion", ctx, clusterID, clusterRole, option)
	ret0, _ := ret[0].(*ccev2.TargetK8SVersionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TargetK8SVersion indicates an expected call of TargetK8SVersion.
func (mr *MockInterfaceMockRecorder) TargetK8SVersion(ctx, clusterID, clusterRole, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TargetK8SVersion", reflect.TypeOf((*MockInterface)(nil).TargetK8SVersion), ctx, clusterID, clusterRole, option)
}

// UpdateCluster mocks base method.
func (m *MockInterface) UpdateCluster(ctx context.Context, clusterID string, args *ccetypes.ClusterSpec, option *bce.SignOption) (*ccev2.UpdateClusterResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCluster", ctx, clusterID, args, option)
	ret0, _ := ret[0].(*ccev2.UpdateClusterResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCluster indicates an expected call of UpdateCluster.
func (mr *MockInterfaceMockRecorder) UpdateCluster(ctx, clusterID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCluster", reflect.TypeOf((*MockInterface)(nil).UpdateCluster), ctx, clusterID, args, option)
}

// UpdateInstance mocks base method.
func (m *MockInterface) UpdateInstance(ctx context.Context, clusterID, instanceID string, spec *ccetypes.InstanceSpec, option *bce.SignOption) (*ccev2.UpdateInstancesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstance", ctx, clusterID, instanceID, spec, option)
	ret0, _ := ret[0].(*ccev2.UpdateInstancesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateInstance indicates an expected call of UpdateInstance.
func (mr *MockInterfaceMockRecorder) UpdateInstance(ctx, clusterID, instanceID, spec, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstance", reflect.TypeOf((*MockInterface)(nil).UpdateInstance), ctx, clusterID, instanceID, spec, option)
}

// UpdateInstanceGroupClusterAutoscalerSpec mocks base method.
func (m *MockInterface) UpdateInstanceGroupClusterAutoscalerSpec(ctx context.Context, clusterID, instanceGroupID string, request *ccev2.ClusterAutoscalerSpec, option *bce.SignOption) (*ccev2.UpdateInstanceGroupClusterAutoscalerSpecResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstanceGroupClusterAutoscalerSpec", ctx, clusterID, instanceGroupID, request, option)
	ret0, _ := ret[0].(*ccev2.UpdateInstanceGroupClusterAutoscalerSpecResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateInstanceGroupClusterAutoscalerSpec indicates an expected call of UpdateInstanceGroupClusterAutoscalerSpec.
func (mr *MockInterfaceMockRecorder) UpdateInstanceGroupClusterAutoscalerSpec(ctx, clusterID, instanceGroupID, request, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstanceGroupClusterAutoscalerSpec", reflect.TypeOf((*MockInterface)(nil).UpdateInstanceGroupClusterAutoscalerSpec), ctx, clusterID, instanceGroupID, request, option)
}

// UpdateInstanceGroupInstanceTemplate mocks base method.
func (m *MockInterface) UpdateInstanceGroupInstanceTemplate(ctx context.Context, clusterID, instanceGroupID string, request *ccev2.UpdateInstanceGroupInstanceTemplateRequest, option *bce.SignOption) (*ccev2.UpdateInstanceGroupInstanceTemplateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstanceGroupInstanceTemplate", ctx, clusterID, instanceGroupID, request, option)
	ret0, _ := ret[0].(*ccev2.UpdateInstanceGroupInstanceTemplateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateInstanceGroupInstanceTemplate indicates an expected call of UpdateInstanceGroupInstanceTemplate.
func (mr *MockInterfaceMockRecorder) UpdateInstanceGroupInstanceTemplate(ctx, clusterID, instanceGroupID, request, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstanceGroupInstanceTemplate", reflect.TypeOf((*MockInterface)(nil).UpdateInstanceGroupInstanceTemplate), ctx, clusterID, instanceGroupID, request, option)
}

// UpdateInstanceGroupPausedStatus mocks base method.
func (m *MockInterface) UpdateInstanceGroupPausedStatus(ctx context.Context, clusterID, instanceGroupID string, request ccev2.PauseDetail, option *bce.SignOption) (*ccev2.UpdateInstanceGroupPausedStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstanceGroupPausedStatus", ctx, clusterID, instanceGroupID, request, option)
	ret0, _ := ret[0].(*ccev2.UpdateInstanceGroupPausedStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateInstanceGroupPausedStatus indicates an expected call of UpdateInstanceGroupPausedStatus.
func (mr *MockInterfaceMockRecorder) UpdateInstanceGroupPausedStatus(ctx, clusterID, instanceGroupID, request, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstanceGroupPausedStatus", reflect.TypeOf((*MockInterface)(nil).UpdateInstanceGroupPausedStatus), ctx, clusterID, instanceGroupID, request, option)
}

// UpdateInstanceGroupReplicas mocks base method.
func (m *MockInterface) UpdateInstanceGroupReplicas(ctx context.Context, clusterID, instanceGroupID string, request *ccev2.UpdateInstanceGroupReplicasRequest, option *bce.SignOption) (*ccev2.UpdateInstanceGroupReplicasResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstanceGroupReplicas", ctx, clusterID, instanceGroupID, request, option)
	ret0, _ := ret[0].(*ccev2.UpdateInstanceGroupReplicasResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateInstanceGroupReplicas indicates an expected call of UpdateInstanceGroupReplicas.
func (mr *MockInterfaceMockRecorder) UpdateInstanceGroupReplicas(ctx, clusterID, instanceGroupID, request, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstanceGroupReplicas", reflect.TypeOf((*MockInterface)(nil).UpdateInstanceGroupReplicas), ctx, clusterID, instanceGroupID, request, option)
}

// UpdateWorkflow mocks base method.
func (m *MockInterface) UpdateWorkflow(ctx context.Context, clusterID, workflowID string, request *ccev2.UpdateWorkflowRequest, option *bce.SignOption) (*ccev2.UpdateWorkflowResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWorkflow", ctx, clusterID, workflowID, request, option)
	ret0, _ := ret[0].(*ccev2.UpdateWorkflowResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateWorkflow indicates an expected call of UpdateWorkflow.
func (mr *MockInterfaceMockRecorder) UpdateWorkflow(ctx, clusterID, workflowID, request, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWorkflow", reflect.TypeOf((*MockInterface)(nil).UpdateWorkflow), ctx, clusterID, workflowID, request, option)
}
