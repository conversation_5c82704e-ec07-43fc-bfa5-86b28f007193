// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2022/01/24 11:05:00, by <EMAIL>, create
*/
/*
实现 CCE V2 SDK Workflow 相关方法
*/

package ccev2

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
	ccetypes "icode.baidu.com/baidu/cprom/cloud-stack/pkg/types"
)

// CreateWorkflow - 对 ClusterID 创建变更流程
func (c *Client) CreateWorkflow(ctx context.Context, clusterID string, args *CreateWorkflowRequest,
	option *bce.SignOption) (*CreateWorkflowResponse, error) {
	if args == nil {
		return nil, fmt.Errorf("args is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("cluster/%s/workflow", clusterID)
	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r CreateWorkflowResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// ListWorkflows - 获取集群中 Workflow 列表
func (c *Client) ListWorkflows(ctx context.Context, clusterID string, option *bce.SignOption) (*ListWorkflowsResponse, error) {
	if clusterID == "" {
		return nil, fmt.Errorf("clusterID is empty")
	}

	url := fmt.Sprintf("cluster/%s/workflow", clusterID)
	req, err := bce.NewRequest("GET", c.GetURL(url, nil), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r ListWorkflowsResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// GetWorkflow - 获取集群中 Workflow 详情
func (c *Client) GetWorkflow(ctx context.Context, clusterID, workflowID string, option *bce.SignOption) (*GetWorkflowResponse, error) {
	if clusterID == "" {
		return nil, fmt.Errorf("cluster is empty")
	}

	if workflowID == "" {
		return nil, fmt.Errorf("workflowID is empty")
	}

	url := fmt.Sprintf("cluster/%s/workflow/%s", clusterID, workflowID)
	req, err := bce.NewRequest("GET", c.GetURL(url, nil), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r GetWorkflowResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// DeleteWorkflow - 删除 Workflow
func (c *Client) DeleteWorkflow(ctx context.Context, clusterID, workflowID string, option *bce.SignOption) (*DeleteWorkflowResponse, error) {
	if clusterID == "" {
		return nil, fmt.Errorf("cluster is empty")
	}

	if workflowID == "" {
		return nil, fmt.Errorf("workflowID is empty")
	}

	url := fmt.Sprintf("cluster/%s/workflow/%s", clusterID, workflowID)
	req, err := bce.NewRequest("DELETE", c.GetURL(url, nil), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r DeleteWorkflowResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// UpdateWorkflow - 更新 Workflow
func (c *Client) UpdateWorkflow(ctx context.Context, clusterID, workflowID string, request *UpdateWorkflowRequest, option *bce.SignOption) (*UpdateWorkflowResponse, error) {
	if clusterID == "" {
		return nil, fmt.Errorf("cluster is empty")
	}

	if workflowID == "" {
		return nil, fmt.Errorf("workflowID is empty")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("cluster/%s/workflow/%s", clusterID, workflowID)
	req, err := bce.NewRequest("PUT", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r UpdateWorkflowResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil

}

// ListNodesCanBeUpgradedByPage - 集群可升级 Nodes 列表
func (c *Client) ListNodesCanBeUpgradedByPage(ctx context.Context, clusterID string, option *bce.SignOption) (*ListNodesCanBeUpgradedByPageResponse, error) {
	if clusterID == "" {
		return nil, fmt.Errorf("cluster is empty")
	}

	url := fmt.Sprintf("cluster/%s/workflow/nodes", clusterID)
	req, err := bce.NewRequest("GET", c.GetURL(url, nil), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r ListNodesCanBeUpgradedByPageResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// TargetK8SVersion - 集群可升级 K8S 版本
func (c *Client) TargetK8SVersion(ctx context.Context, clusterID string, clusterRole ccetypes.ClusterRole,
	option *bce.SignOption) (*TargetK8SVersionResponse, error) {
	if clusterID == "" {
		return nil, fmt.Errorf("cluster is empty")
	}

	url := fmt.Sprintf("cluster/%s/workflow/%s/target_k8s_version", clusterID, clusterRole)
	req, err := bce.NewRequest("GET", c.GetURL(url, nil), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r TargetK8SVersionResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}
