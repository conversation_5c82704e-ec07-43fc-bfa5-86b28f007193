// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/03/28 13:11:00, by <EMAIL>, create
*/
/*
实现 CCE V2 SDK Cluster 相关方法
*/

package ccev2

import (
	"context"
	"encoding/json"
	"fmt"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
)

// GetBindingCpromInstances - 获取 BindingCPromInsntace 列表
// PARAMS:
//   - ctx: The context to trace request
//   - ListBindingCPromInstanceResquest: 集群 ID
//   - args: 获取集群节点列表参数
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) GetBindingCpromInstances(ctx context.Context, args *ListBindingCPromInstanceResquest, option *bce.SignOption) (*ListBindingCPromInstanceResponse, error) {
	if args == nil {
		return nil, fmt.Errorf("clusterID is empty")
	}
	params := map[string]string{
		"clusterID": args.ClusterID,
	}

	url := "monitor/binding_cprom_instances"
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r ListBindingCPromInstanceResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}
