/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  instancegroup_test
 * @Version: 1.0.0
 * @Date: 2020/7/9 4:30 下午
 */
package ccev2

import (
	"context"
	"fmt"
	utils2 "icode.baidu.com/baidu/cprom/cloud-stack/cprom-common/utils"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/retrypolicy"
	ccetypes "icode.baidu.com/baidu/cprom/cloud-stack/pkg/types"
)

func TestClient_CreateInstanceGroup(t *testing.T) {
	config := bce.Config{
		Credentials: bce.NewCredentials("6aff65797f724d92a01b2630ad0b80d6", "62938cc789964b86b61e7d818d736560"),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      "gztest",
		RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
	}

	clusterID := "testClusterID"

	mockResp := CreateInstanceGroupResponse{
		CommonResponse: CommonResponse{
			RequestID: "RequestID",
		},
		InstanceGroupID: "InstanceGroupID",
	}

	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "POST" || r.URL.Path != "/cluster/"+clusterID+"/instancegroup" {
			w.WriteHeader(404)
			return
		}
		fmt.Fprintln(w, utils2.ToJSON(mockResp))
	}))
	defer ts.Close()

	config.Endpoint = ts.URL

	client := NewClient(&config)
	client.SetDebug(true)
	request := CreateInstanceGroupRequest{
		ccetypes.InstanceGroupSpec{
			InstanceGroupName:     "xxx",
			ClusterID:             clusterID,
			ClusterRole:           ccetypes.ClusterRoleNode,
			InstanceTemplate:      ccetypes.InstanceTemplate{},
			Replicas:              0,
			ClusterAutoscalerSpec: nil,
		},
	}
	resp, err := client.CreateInstanceGroup(context.Background(), clusterID, &request, nil)
	if err != nil {
		t.Errorf("create instanceGroup failed: %v", err)
	}
	if !cmp.Equal(resp, &mockResp) {
		t.Errorf("diff: %s", cmp.Diff(resp, &mockResp))
	}
}

func TestClient_ListInstanceGroups(t *testing.T) {
	config := bce.Config{
		Credentials: bce.NewCredentials("6aff65797f724d92a01b2630ad0b80d6", "62938cc789964b86b61e7d818d736560"),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      "gztest",
		RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
	}

	clusterID := "testClusterID"

	mockResp := ListInstanceGroupResponse{
		CommonResponse: CommonResponse{
			RequestID: "RequestID",
		},
		Page: ListInstanceGroupPage{
			PageNo:     1,
			PageSize:   10,
			TotalCount: 1,
			List: []*InstanceGroup{
				{
					Spec: &InstanceGroupSpec{
						CCEInstanceGroupID:    "",
						InstanceGroupName:     "",
						ClusterID:             "",
						ClusterRole:           "",
						UserID:                "",
						AccountID:             "",
						ShrinkPolicy:          "",
						UpdatePolicy:          "",
						CleanPolicy:           "",
						InstanceTemplate:      InstanceTemplate{},
						Replicas:              10,
						ClusterAutoscalerSpec: nil,
					},
					Status: &InstanceGroupStatus{
						ReadyReplicas: 10,
						Pause:         nil,
					},
					CreatedAt: time.Now(),
				},
			},
		},
	}

	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "GET" || r.URL.Path != "/cluster/"+clusterID+"/instancegroups" {
			w.WriteHeader(404)
			return
		}
		fmt.Fprintln(w, utils2.ToJSON(mockResp))
	}))
	defer ts.Close()

	config.Endpoint = ts.URL

	client := NewClient(&config)
	client.SetDebug(true)

	resp, err := client.ListInstanceGroups(context.Background(), clusterID, &InstanceGroupListOption{pageSize: 10, pageNo: 1}, nil)
	if err != nil {
		t.Errorf("list instanceGroups failed: %v", err)
	}
	if !cmp.Equal(resp, &mockResp) {
		t.Errorf("diff: %s", cmp.Diff(resp, &mockResp))
	}
}

func TestClient_GetInstanceGroup(t *testing.T) {
	config := bce.Config{
		Credentials: bce.NewCredentials("6aff65797f724d92a01b2630ad0b80d6", "62938cc789964b86b61e7d818d736560"),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      "gztest",
		RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
	}

	clusterID := "testClusterID"
	instanceGroupID := "testID"

	mockResp := GetInstanceGroupResponse{
		CommonResponse: CommonResponse{},
		InstanceGroup: &InstanceGroup{
			Spec: &InstanceGroupSpec{
				CCEInstanceGroupID:    instanceGroupID,
				InstanceGroupName:     "",
				ClusterID:             "",
				ClusterRole:           "",
				UserID:                "",
				AccountID:             "",
				ShrinkPolicy:          "",
				UpdatePolicy:          "",
				CleanPolicy:           "",
				InstanceTemplate:      InstanceTemplate{},
				Replicas:              10,
				ClusterAutoscalerSpec: nil,
			},
			Status: &InstanceGroupStatus{
				ReadyReplicas: 10,
				Pause:         nil,
			},
			CreatedAt: time.Now(),
		},
	}
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "GET" || r.URL.Path != "/cluster/"+clusterID+"/instancegroup/"+instanceGroupID {
			w.WriteHeader(404)
			return
		}
		fmt.Fprintln(w, utils2.ToJSON(mockResp))
	}))
	defer ts.Close()

	config.Endpoint = ts.URL

	client := NewClient(&config)
	client.SetDebug(true)

	resp, err := client.GetInstanceGroup(context.Background(), clusterID, instanceGroupID, nil)
	if err != nil {
		t.Errorf("get instanceGroups failed: %v", err)
	}
	if !cmp.Equal(resp, &mockResp) {
		t.Errorf("diff: %s", cmp.Diff(resp, &mockResp))
	}
}

func TestClient_UpdateInstanceGroupReplicas(t *testing.T) {
	config := bce.Config{
		Credentials: bce.NewCredentials("6aff65797f724d92a01b2630ad0b80d6", "62938cc789964b86b61e7d818d736560"),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      "gztest",
		RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
	}

	clusterID := "testClusterID"
	instanceGroupID := "testID"

	mockResp := UpdateInstanceGroupReplicasResponse{
		CommonResponse: CommonResponse{
			RequestID: "xxx",
		},
	}

	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "PUT" || r.URL.Path != "/cluster/"+clusterID+"/instancegroup/"+instanceGroupID+"/replicas" {
			w.WriteHeader(404)
			return
		}
		fmt.Fprintln(w, utils2.ToJSON(mockResp))
	}))
	defer ts.Close()

	config.Endpoint = ts.URL

	client := NewClient(&config)
	client.SetDebug(true)

	request := UpdateInstanceGroupReplicasRequest{
		Replicas:       20,
		InstanceIDs:    []string{"xxxID"},
		DeleteInstance: false,
	}
	resp, err := client.UpdateInstanceGroupReplicas(context.Background(), clusterID, instanceGroupID, &request, nil)
	if err != nil {
		t.Errorf("update instanceGroups replicas failed: %v", err)
	}
	if !cmp.Equal(resp, &mockResp) {
		t.Errorf("diff: %s", cmp.Diff(resp, &mockResp))
	}
}

func TestClient_UpdateInstanceGroupClusterAutoscalerSpec(t *testing.T) {
	config := bce.Config{
		Credentials: bce.NewCredentials("6aff65797f724d92a01b2630ad0b80d6", "62938cc789964b86b61e7d818d736560"),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      "gztest",
		RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
	}

	clusterID := "testClusterID"
	instanceGroupID := "testID"

	mockResp := UpdateInstanceGroupClusterAutoscalerSpecResponse{
		CommonResponse: CommonResponse{
			RequestID: "xxx",
		},
	}

	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "PUT" || r.URL.Path != "/cluster/"+clusterID+"/instancegroup/"+instanceGroupID+"/autoscaler" {
			w.WriteHeader(404)
			return
		}
		fmt.Fprintln(w, utils2.ToJSON(mockResp))
	}))
	defer ts.Close()

	config.Endpoint = ts.URL

	client := NewClient(&config)
	client.SetDebug(true)

	request := ClusterAutoscalerSpec{
		Enabled:              true,
		MinReplicas:          0,
		MaxReplicas:          10,
		ScalingGroupPriority: 1,
	}
	resp, err := client.UpdateInstanceGroupClusterAutoscalerSpec(context.Background(), clusterID, instanceGroupID, &request, nil)
	if err != nil {
		t.Errorf("update instanceGroups clusterAutoscalerSpec failed: %v", err)
	}
	if !cmp.Equal(resp, &mockResp) {
		t.Errorf("diff: %s", cmp.Diff(resp, &mockResp))
	}
}

func TestClient_DeleteInstanceGroup(t *testing.T) {
	config := bce.Config{
		Credentials: bce.NewCredentials("6aff65797f724d92a01b2630ad0b80d6", "62938cc789964b86b61e7d818d736560"),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      "gztest",
		RetryPolicy: retrypolicy.NewIntervalRetryPolicy(context.TODO(), 0, 0),
	}

	clusterID := "testClusterID"
	instanceGroupID := "testID"

	mockResp := DeleteInstanceGroupResponse{
		CommonResponse: CommonResponse{
			RequestID: "xxx",
		},
	}

	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "DELETE" || r.URL.Path != "/cluster/"+clusterID+"/instancegroup/"+instanceGroupID {
			w.WriteHeader(404)
			return
		}
		fmt.Fprintln(w, utils2.ToJSON(mockResp))
	}))
	defer ts.Close()

	config.Endpoint = ts.URL

	client := NewClient(&config)
	client.SetDebug(true)

	resp, err := client.DeleteInstanceGroup(context.Background(), clusterID, instanceGroupID, true, nil)
	if err != nil {
		t.Errorf("delete instanceGroups failed: %v", err)
	}
	if !cmp.Equal(resp, &mockResp) {
		t.Errorf("diff: %s", cmp.Diff(resp, &mockResp))
	}
}
