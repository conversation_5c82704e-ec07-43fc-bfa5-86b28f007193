// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/03/28 13:11:00, by <EMAIL>, create
*/
/*
Client 实现 ccev2.Interface
*/

package ccev2

import (
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
)

var _ Interface = &Client{}

// Endpoints - CCE V2 各地域 Endpoints
var Endpoints = map[string]string{
	"bj":  "cce.bj.baidubce.com/api/cce/service/v2",
	"gz":  "cce.gz.baidubce.com/api/cce/service/v2",
	"su":  "cce.su.baidubce.com/api/cce/service/v2",
	"nj":  "cce.nj.baidubce.com/api/cce/service/v2",
	"hkg": "cce.hkg.baidubce.com/api/cce/service/v2",
	"fwh": "cce.fwh.baidubce.com/api/cce/service/v2",
	"bd":  "cce.bd.baidubce.com/api/cce/service/v2",
}

// Client 实现 ccev2.Interface
type Client struct {
	*bce.Client
}

// NewClient client of CCE
func NewClient(config *bce.Config) *Client {
	return &Client{
		Client: bce.NewClient(config),
	}
}

// SetDebug 是否开启 debug 模式
func (c *Client) SetDebug(debug bool) {
	c.Client.SetDebug(debug)
}

// GetURL generates the full URL of http request for Baidu Cloud BOS API.
func (c *Client) GetURL(version string, params map[string]string) string {
	host := c.Endpoint
	uriPath := version
	return c.Client.GetURL(host, uriPath, params)
}
