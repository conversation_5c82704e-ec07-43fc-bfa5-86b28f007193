// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2022/02/24 15:17:00, by ch<PERSON><PERSON><EMAIL>, create
*/
/*
实现 CCE V2 SDK Step 相关方法, 展示集群等操作过程
*/

package ccev2

import (
	"context"
	utils2 "icode.baidu.com/baidu/cprom/cloud-stack/cprom-common/utils"
	"testing"
)

func testClient_GetClusterEventSteps(t *testing.T) {
	clusterID := "cce-mstequ8b"

	resp, err := client.GetClusterEventSteps(context.TODO(), clusterID, nil)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("GetClusterEventSteps failed: %v", err)
		return
	}

	t.<PERSON><PERSON><PERSON>("GetClusterEventSteps success: %s", utils2.ToJSON(resp))
}
