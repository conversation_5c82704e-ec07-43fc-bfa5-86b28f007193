// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2022/02/23 10:37:00, by <EMAIL>, create
*/
/*
实现 CCE V2 SDK Instance 相关方法
*/

package ccev2

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
)

// CreateRBACKubeConfig - 根据 RBAC 授权创建 Kubeconfig, 支持临时 Kubeconfig
// PARAMS:
//   - ctx: The context to trace request
//   - request: *KubeConfigRequest
//   - option: *bce.SignOption 代签名方法
//
// RETURNS:
//
//	*CreateRBACResponse: 返回
//	error: nil if succeed, error if fail
func (c *Client) CreateRBACKubeConfig(ctx context.Context, request *KubeConfigRequest, option *bce.SignOption) (*CreateRBACResponse, error) {
	if request == nil {
		return nil, fmt.Errorf("request is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("rbac", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var r CreateRBACResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}
