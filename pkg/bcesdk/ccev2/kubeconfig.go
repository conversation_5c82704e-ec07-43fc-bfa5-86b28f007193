// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/04/15 13:11:00, by <EMAIL>, create
*/
/*
实现 CCE V2 SDK Cluster 相关方法
*/

package ccev2

import (
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/logger"
)

// CheckKubeConfigType - 检查 KubeConfigType 是否合法
func CheckKubeConfigType(ctx context.Context, kubeConfigType string) error {
	if kubeConfigType != string(KubeConfigTypePublic) &&
		kubeConfigType != string(KubeConfigTypeInternal) &&
		kubeConfigType != string(KubeConfigTypeVPC) {
		return fmt.Errorf("KubeConfigType %s not valid", kubeConfigType)
	}

	return nil
}

// GetAdminKubeConfig - 获取集群 Admin KubeConfig
func (c *Client) GetAdminKubeConfig(ctx context.Context, clusterID string, kubeConfigType KubeConfigType,
	option *bce.SignOption) (*GetKubeConfigResponse, error) {
	// 参数校验
	if clusterID == "" {
		return nil, fmt.Errorf("clusterID is empty")
	}

	if err := CheckKubeConfigType(ctx, string(kubeConfigType)); err != nil {
		return nil, err
	}

	// 请求 kubeconfig
	url := fmt.Sprintf("kubeconfig/%s/admin/%s", clusterID, kubeConfigType)
	req, err := bce.NewRequest("GET", c.GetURL(url, nil), nil)
	if err != nil {
		logger.Errorf(ctx, "Get %s failed: %s", url, err)
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	// 返回结果
	var r GetKubeConfigResponse
	err = json.Unmarshal(bodyContent, &r)
	if err != nil {
		return nil, err
	}

	return &r, nil
}
