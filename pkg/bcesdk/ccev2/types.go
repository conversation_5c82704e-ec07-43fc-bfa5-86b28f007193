// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/03/28 13:11:00, by <EMAIL>, create
*/
/*
CCE V2 版本 GO SDK, Interface 定义
*/

package ccev2

import (
	"context"

	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	schedulingv1beta1 "volcano.sh/apis/pkg/apis/scheduling/v1beta1"

	cpromv1 "icode.baidu.com/baidu/cprom/cloud-stack/pkg/apis/cprom/v1"
	ccecrd "icode.baidu.com/baidu/cprom/cloud-stack/pkg/crd/api/v1"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bbc"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/eip"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/iam"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/internalblb"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/internalvpc"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/logicbcc"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/logicimage"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/vpc"
	ccetypes "icode.baidu.com/baidu/cprom/cloud-stack/pkg/types"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock -source=./types.go

// Interface 定义 CCE V2 SDK
// 新增 Interface 后注意更新 doc.go !!!!!!!
type Interface interface {
	SetDebug(debug bool)

	CreateCluster(ctx context.Context, args *CreateClusterRequest,
		option *bce.SignOption) (*CreateClusterResponse, error)
	UpdateCluster(ctx context.Context, clusterID string, args *ccetypes.ClusterSpec,
		option *bce.SignOption) (*UpdateClusterResponse, error)
	GetCluster(ctx context.Context, clusterID string,
		option *bce.SignOption) (*GetClusterResponse, error)
	GetEtcdCert(ctx context.Context, clusterID string,
		option *bce.SignOption) (*GetEtcdCertsResponse, error)
	DeleteCluster(ctx context.Context, clusterID string, options *DeleteOptions,
		option *bce.SignOption) (*DeleteClusterResponse, error)
	ListClusters(ctx context.Context, keywordType ClusterKeywordType, keyword string,
		orderBy ClusterOrderBy, order Order, pageNum, pageSize int, option *bce.SignOption) (*ListClustersResponse, error)

	CreateInstances(ctx context.Context, clusterID string, args []*InstanceSet,
		option *bce.SignOption) (*CreateInstancesResponse, error)
	UpdateInstance(ctx context.Context, clusterID string, instanceID string,
		spec *ccetypes.InstanceSpec, option *bce.SignOption) (*UpdateInstancesResponse, error)
	GetInstance(ctx context.Context, clusterID, instanceID string, option *bce.SignOption) (*GetInstanceResponse, error)
	GetInstanceByNodeName(ctx context.Context, clusterID string, nodeName string, option *bce.SignOption) (*GetInstanceByNodeNameResponse, error)
	DeleteInstances(ctx context.Context, clusterID string, args *DeleteInstancesRequest,
		option *bce.SignOption) (*DeleteInstancesResponse, error)
	ListInstancesByPage(ctx context.Context, clusterID string, args *ListInstancesByPageParams,
		option *bce.SignOption) (*ListInstancesResponse, error)
	ListInstancesByInstanceGroupID(ctx context.Context, clusterID string,
		instanceGroupID string, pageNo, pageSize int, option *bce.SignOption) (*ListInstancesByInstanceGroupIDResponse, error)
	CordonInstances(ctx context.Context, cordonReq *CordonNodesRequest, option *bce.SignOption) error
	SyncInstance(ctx context.Context, clusterID string, option *bce.SignOption) (*SyncInstancesResponse, error)

	GetClusterQuota(ctx context.Context, option *bce.SignOption) (*GetQuotaResponse, error)
	GetClusterNodeQuota(ctx context.Context, clusterID string, option *bce.SignOption) (*GetQuotaResponse, error)

	// Cluster/Instance 重试 API
	ResetClusterRetryCount(ctx context.Context, clusterID string, option *bce.SignOption) error
	ResetInstanceRetryCount(ctx context.Context, clusterID, cceInstanceID string, option *bce.SignOption) error

	// AdminKubeConfig API
	GetAdminKubeConfig(ctx context.Context, clusterID string, kubeConfigType KubeConfigType,
		option *bce.SignOption) (*GetKubeConfigResponse, error)

	// 容器网络校验 API
	CheckContainerNetworkCIDR(ctx context.Context, args *CheckContainerNetworkCIDRRequest,
		option *bce.SignOption) (*CheckContainerNetworkCIDRResponse, error)
	RecommendContainerCIDR(ctx context.Context, args *RecommendContainerCIDRRequest,
		option *bce.SignOption) (*RecommendContainerCIDRResponse, error)
	RecommendClusterIPCIDR(ctx context.Context, args *RecommendClusterIPCIDRRequest,
		option *bce.SignOption) (*RecommendClusterIPCIDRResponse, error)

	// InstanceGroup API
	CreateInstanceGroup(ctx context.Context, clusterID string, request *CreateInstanceGroupRequest,
		option *bce.SignOption) (*CreateInstanceGroupResponse, error)
	ListInstanceGroups(ctx context.Context, clusterID string, listOption *InstanceGroupListOption,
		option *bce.SignOption) (*ListInstanceGroupResponse, error)
	GetInstanceGroup(ctx context.Context, clusterID, instanceGroupID string, option *bce.SignOption) (*GetInstanceGroupResponse, error)
	UpdateInstanceGroupReplicas(ctx context.Context, clusterID, instanceGroupID string,
		request *UpdateInstanceGroupReplicasRequest, option *bce.SignOption) (*UpdateInstanceGroupReplicasResponse, error)
	UpdateInstanceGroupClusterAutoscalerSpec(ctx context.Context, clusterID, instanceGroupID string,
		request *ClusterAutoscalerSpec, option *bce.SignOption) (*UpdateInstanceGroupClusterAutoscalerSpecResponse, error)
	UpdateInstanceGroupInstanceTemplate(ctx context.Context, clusterID, instanceGroupID string,
		request *UpdateInstanceGroupInstanceTemplateRequest, option *bce.SignOption) (*UpdateInstanceGroupInstanceTemplateResponse, error)
	UpdateInstanceGroupPausedStatus(ctx context.Context, clusterID, instanceGroupID string,
		request PauseDetail, option *bce.SignOption) (*UpdateInstanceGroupPausedStatusResponse, error)
	DeleteInstanceGroup(ctx context.Context, clusterID, instanceGroupID string, deleteInstances bool,
		option *bce.SignOption) (*DeleteInstanceGroupResponse, error)
	CreateScaleUpInstanceGroupTask(ctx context.Context, clusterID, instanceGroupID string,
		targetReplicas int, option *bce.SignOption) (*CreateTaskResp, error)
	CreateScaleDownInstanceGroupTask(ctx context.Context, clusterID, instanceGroupID string,
		instancesToBeRemoved []string, option *bce.SignOption) (*CreateTaskResp, error)
	CreateScaleDownInstanceGroupByCleanPolicy(ctx context.Context, clusterID, instanceGroupID string, instancesToBeRemoved []string,
		cleanPolicy CleanPolicy, deleteOption *ccetypes.DeleteOption, option *bce.SignOption) (*CreateTaskResp, error)
	InstallPlugin(ctx context.Context, clusterID, pluginName string, option *bce.SignOption) error

	// RBAC API
	CreateRBACKubeConfig(ctx context.Context, request *KubeConfigRequest, option *bce.SignOption) (*CreateRBACResponse, error)

	// Workflow API
	CreateWorkflow(ctx context.Context, clusterID string, args *CreateWorkflowRequest, option *bce.SignOption) (*CreateWorkflowResponse, error)
	ListWorkflows(ctx context.Context, clusterID string, option *bce.SignOption) (*ListWorkflowsResponse, error) // 数量很少, 前端可能用不到, 暂时不分页
	GetWorkflow(ctx context.Context, clusterID, workflowID string, option *bce.SignOption) (*GetWorkflowResponse, error)
	DeleteWorkflow(ctx context.Context, clusterID, workflowID string, option *bce.SignOption) (*DeleteWorkflowResponse, error)
	UpdateWorkflow(ctx context.Context, clusterID, workflowID string, request *UpdateWorkflowRequest, option *bce.SignOption) (*UpdateWorkflowResponse, error)
	ListNodesCanBeUpgradedByPage(ctx context.Context, clusterID string, option *bce.SignOption) (*ListNodesCanBeUpgradedByPageResponse, error)
	TargetK8SVersion(ctx context.Context, clusterID string, clusterRole ccetypes.ClusterRole, option *bce.SignOption) (*TargetK8SVersionResponse, error)

	//Monitor API
	GetBindingCpromInstances(ctx context.Context, args *ListBindingCPromInstanceResquest, option *bce.SignOption) (*ListBindingCPromInstanceResponse, error)
}

// CreateClusterRequest - 创建 Cluster 参数
type CreateClusterRequest struct {
	Options     CreateClusterOptions  `json:"options"`
	ClusterSpec *ccetypes.ClusterSpec `json:"cluster"`
	MasterSpecs []*InstanceSet        `json:"masters,omitempty"`
	NodeSpecs   []*InstanceSet        `json:"nodes,omitempty"`
}

type CreateClusterOptions struct {
	SkipNetworkCheck *bool `json:"skipNetworkCheck"`
}

// InstanceSet 避免跟节点组参数冲突，同时需要保留这个结构，一直到正式支持节点组
type InstanceSet struct {
	InstanceSpec ccetypes.InstanceSpec `json:"instanceSpec"`
	Count        int                   `json:"count"`
}

// ListInstancesByPageParams - 分页查询集群实例列表参数
type ListInstancesByPageParams struct {
	KeywordType InstanceKeywordType `json:"keywordType"`
	Keyword     string              `json:"keyword"`
	OrderBy     InstanceOrderBy     `json:"orderBy"`
	Order       Order               `json:"order"`
	PageNo      int                 `json:"pageNo"`
	PageSize    int                 `json:"pageSize"`

	ClusterRole ccetypes.ClusterRole `json:"clusterRole"` // 不设置则返回 Master 和 Node
	BECRegion   string               `json:"becRegion"`
	IPList      string               `json:"ipList"` // 按 IP 过滤, 格式: ************,192.168.0,12

	// 是否返回内部字段
	EnableInternalFields bool `json:"enableInternalFields,omitempty"`

	// 是否返回升级相关字段
	EnableUpgradeNodeFields bool `json:"enableUpgradeNodeFields,omitempty"`
}

// CreateClusterResponse - 创建 Cluster 返回
type CreateClusterResponse struct {
	ClusterID string `json:"clusterID"`
	RequestID string `json:"requestID"`
}

// UpdateClusterResponse - 更新 Cluster 返回
type UpdateClusterResponse struct {
	Cluster   *Cluster `json:"cluster"`
	RequestID string   `json:"requestID"`
}

// GetClusterResponse - 查询 Cluster 返回
type GetClusterResponse struct {
	Cluster   *Cluster `json:"cluster"`
	RequestID string   `json:"requestID"`
}

// GetClusterResponse - 查询 Cluster 返回
type GetEtcdCertsResponse struct {
	Ca            string    `json:"ca"`
	ClientCert    string    `json:"clientCert"`
	ClientCertKey string    `json:"clientCertKey"`
	RequestID     string    `json:"requestID"`
	CreatedAt     time.Time `json:"createdAt,omitempty"`
	ExpiredAt     time.Time `json:"expiredAt,omitempty"`
}

// GetClusterExtraInfoResponse - 查询 GetCluster 以外的信息
type GetClusterExtraInfoResponse struct {
	*ClusterExtraInfo `json:",inline"`
	RequestID         string `json:"requestID"`
}

type ClusterExtraInfo struct {
	MasterBLBSubnet *Subnet  `json:"masterBLBSubnet"`
	LBServiceSubnet *Subnet  `json:"lbServiceSubnet"`
	Subnets         []Subnet `json:"subnets"`
	ENISubnets      []Subnet `json:"eniSubnets"`
}

type Subnet struct {
	SubnetID      string                    `json:"subnetID"`
	SubnetName    string                    `json:"subnetName"`
	SubnetType    vpc.SubnetType            `json:"subnetType"`
	SubnetCIDR    string                    `json:"subnetCIDR"`
	AvailableZone internalvpc.AvailableZone `json:"availableZone"`
}

// DeleteClusterResponse - 删除 Cluster 返回
type DeleteClusterResponse struct {
	RequestID string `json:"requestID"`
}

// ListClustersResponse - List 用户 Cluster 返回
type ListClustersResponse struct {
	ClusterPage *ClusterPage `json:"clusterPage"`
	RequestID   string       `json:"requestID"`
}

// CreateInstancesResponse - 创建 Instances 返回
type CreateInstancesResponse struct {
	CCEInstanceIDs []string `json:"cceInstanceIDs"`
	RequestID      string   `json:"requestID"`
}

// UpdateInstancesResponse - 更新 Instances 返回
type UpdateInstancesResponse struct {
	Instance  *Instance `json:"instance"`
	RequestID string    `json:"requestID"`
}

// ClusterPage - 集群分页查询返回
type ClusterPage struct {
	KeywordType ClusterKeywordType `json:"keywordType"`
	Keyword     string             `json:"keyword"`
	OrderBy     ClusterOrderBy     `json:"orderBy"`
	Order       Order              `json:"order"`
	PageNo      int                `json:"pageNo"`
	PageSize    int                `json:"pageSize"`
	TotalCount  int                `json:"totalCount"`
	ClusterList []*Cluster         `json:"clusterList"`
}

// ClusterKeywordType 集群模糊查询字段
type ClusterKeywordType string

const (
	// ClusterKeywordTypeClusterName 集群模糊查询字段: ClusterName
	ClusterKeywordTypeClusterName ClusterKeywordType = "clusterName"
	// ClusterKeywordTypeClusterID 集群模糊查询字段: ClusterID
	ClusterKeywordTypeClusterID ClusterKeywordType = "clusterID"
)

// ClusterOrderBy 集群查询排序字段
type ClusterOrderBy string

const (
	// ClusterOrderByClusterName 集群查询排序字段: ClusterName
	ClusterOrderByClusterName ClusterOrderBy = "clusterName"
	// ClusterOrderByClusterID 集群查询排序字段: ClusterID
	ClusterOrderByClusterID ClusterOrderBy = "clusterID"
	// ClusterOrderByCreatedAt 集群查询排序字段: CreatedAt
	ClusterOrderByCreatedAt ClusterOrderBy = "createdAt"
)

// Order 集群查询排序
type Order string

const (
	// OrderASC 集群查询排序: 升序
	OrderASC Order = "ASC"
	// OrderDESC 集群查询排序: 降序
	OrderDESC Order = "DESC"
)

const (
	// PageNoDefault 分页查询默认页码
	PageNoDefault int = 1
	// PageSizeDefault 分页查询默认页面元素数目
	PageSizeDefault int = 10
)

// GetInstanceByNodeNameResponse - 根据 NodeName 查询 Instance 返回
type GetInstanceByNodeNameResponse struct {
	Instance  *Instance `json:"instance"`
	RequestID string    `json:"requestID"`
}

// GetInstanceResponse - 查询 Instance 返回
type GetInstanceResponse struct {
	Instance *Instance `json:"instance"`

	RequestID string `json:"requestID"`
}

// DeleteInstancesResponse - 删除 Instances 返回
type DeleteInstancesResponse struct {
	RequestID string `json:"requestID"`
}

// ListInstancesResponse - List Instances 返回
type ListInstancesResponse struct {
	InstancePage *InstancePage `json:"instancePage"`
	RequestID    string        `json:"requestID"`
}

// GetQuotaResponse - 查询 Quota 返回
type GetQuotaResponse struct {
	ccetypes.Quota
	RequestID string `json:"requestID"`
}

// GetKubeConfigResponse - 查询 KubeConfig 返回
type GetKubeConfigResponse struct {
	KubeConfigType KubeConfigType `json:"kubeConfigType"`
	KubeConfig     string         `json:"kubeConfig"`
	RequestID      string         `json:"requestID"`
}

type GetClusterCRDResponse struct {
	Cluster   *ccecrd.Cluster `json:"cluster"`
	RequestID string          `json:"requestID"`
}

type UpdateClusterCRDRequest struct {
	Cluster *ccecrd.Cluster `json:"cluster"`
}

type UpdateClusterCRDLabelsRequest struct {
	Labels map[string]string `json:"labels"`
}

// GetInstanceCRDResponse - 查询 instance crd 返回
type GetInstanceCRDResponse struct {
	Instance  *ccecrd.Instance `json:"instance"`
	RequestID string           `json:"requestID"`
}

// UpdateInstanceCRDRequest - update instance crd 请求体
type UpdateInstanceCRDRequest struct {
	Instance *ccecrd.Instance `json:"instance"`
}

type CordonNodesRequest struct {
	ClusterID      string   `json:"clusterID"`
	CCEInstanceIDs []string `json:"cceInstanceIDs"`
	Cordon         bool     `json:"cordon"`
}

type GPUShareNodesRequest struct {
	ClusterID      string   `json:"clusterID"`
	CCEInstanceIDs []string `json:"cceInstanceIDs"`
	GPUShare       bool     `json:"gpuShare"`
}

// *****************RBAC 接口相关参数 Start***********************

// KubeConfigRequest - 多租户RBAC KubeConfig 请求
type KubeConfigRequest struct {
	ClusterID string   `json:"clusterID"`
	UserID    string   `json:"userID"`
	NameSpace string   `json:"namespace"`
	Role      RBACRole `json:"role"`

	// 用于生成临时 KubeConfig, 如果设置 Temp = true, 则 Response 直接反馈 KubeConfig 内容, 不落数据库
	Temp           bool           `json:"temp,omitempty"`
	ExpireHours    int            `json:"expireHours,omitempty"`
	KubeConfigType KubeConfigType `json:"kubeConfigType,omitempty"`
}

// KubeConfigType - kube config 类型
type KubeConfigType string

const (
	// KubeConfigTypeInternal 使用 BLB FloatingIP
	KubeConfigTypeInternal KubeConfigType = "internal"

	// KubeConfigTypeVPC 使用 BLB VPCIP
	KubeConfigTypeVPC KubeConfigType = "vpc"

	// KubeConfigTypePublic 使用 BLB EIP
	KubeConfigTypePublic KubeConfigType = "public"
)

// RBACRole - RBAC 角色
type RBACRole string

const (
	// RoleAdmin - 管理员权限
	RoleAdmin RBACRole = "cce:admin" // 管理员
	// RoleDevOps - 运维开发权限
	RoleDevOps RBACRole = "cce:devops" // 运维开发
	// RoleReadonly - 只读权限
	RoleReadonly RBACRole = "cce:readonly" // 只读
)

// CreateRBACResponse - 创建 RBAC 权限接口的返回
type CreateRBACResponse struct {
	RequestID string `json:"requestID,omitempty"` // request id
	Data      []*CreateRBACMessage

	// 如果 KubeConfigRequest 设置 ExpireHours, 则直接返回 TemporaryKubeConfig
	TemporaryKubeConfig string `json:"temporaryKubeConfig,omitempty"`
}

// 创建RBAC权限接口的主要信息
type CreateRBACMessage struct {
	Success   bool   `json:"success, omitempty"` // 是否创建成功
	ClusterID string `json:"clusterID"`
	Message   string `json:"message, omitempty"` // 创建结果的信息
}

// 删除RBAC权限接口的返回
type DeleteRBACResponse struct {
	RequestID string `json:"requestID,omitempty"` // request id
}

// 获取RBAC权限列表的返回
type GetRBACResponse struct {
	RequestID string `json:"requestID,omitempty"` // request id
	Data      []*GetRBACMessage
}

// 获取RBAC集群列表的返回
type ListRBACClustersResponse struct {
	RequestID string `json:"requestID,omitempty"` // request id
	Data      map[string]string
}

// 获取RBAC指定集群namespace列表的返回
type ListRBACNamespacesResponse struct {
	RequestID string `json:"requestID,omitempty"` // request id
	Data      []string
}

// 获取RBAC权限列表的主要信息
type GetRBACMessage struct {
	Role        RBACRole `json:"role, omitempty"`
	ClusterID   string   `json:"clusterID, omitempty"`
	Namespace   string   `json:"namespace, omitempty"`
	ClusterName string   `json:"clusterName, omitempty"`
}

// *****************RBAC 接口相关参数 End***********************

// Cluster - Cluster 返回
type Cluster struct {
	Spec   *ClusterSpec   `json:"spec"`
	Status *ClusterStatus `json:"status"`

	CreatedAt time.Time `json:"createdAt,omitempty"`
	UpdatedAt time.Time `json:"updatedAt,omitempty"`
}

// ClusterSpec - Cluster Spec
type ClusterSpec struct {
	ClusterID   string               `json:"clusterID"`
	ClusterName string               `json:"clusterName"`
	ClusterType ccetypes.ClusterType `json:"clusterType"`

	Description string `json:"description"`

	K8SVersion     ccetypes.K8SVersion  `json:"k8sVersion"`
	RuntimeType    ccetypes.RuntimeType `json:"runtimeType"`
	RuntimeVersion string               `json:"runtimeVersion"`

	VPCID   string `json:"vpcID"`
	VPCCIDR string `json:"vpcCIDR"`

	Plugins ccetypes.PluginListType `json:"plugins"`

	MasterConfig           MasterConfig                    `json:"masterConfig"`
	ContainerNetworkConfig ccetypes.ContainerNetworkConfig `json:"containerNetworkConfig"`

	// K8S 自定义配置
	K8SCustomConfig ccetypes.K8SCustomConfig `json:"k8sCustomConfig,omitempty"`
}

// ClusterStatus - Cluster Status
type ClusterStatus struct {
	ClusterBLB BLB `json:"clusterBLB"`

	InfrastructureReady    bool `json:"infrastructureReady"`
	APIServerAccessSuccess bool `json:"apiServerAccessSuccess"`
	K8SPluginDeploySuccess bool `json:"k8sPluginDeploySuccess"`

	ClusterPhase ccetypes.ClusterPhase `json:"clusterPhase"`

	NodeNum int `json:"nodeNum"`

	UpgradeWorkflowID string `json:"upgradeWorkflowID,omitempty"`
}

// BLB 定义 BLB 类型
type BLB struct {
	ID    string `json:"id"`
	VPCIP string `json:"vpcIP"`
	EIP   string `json:"eip"`
}

// MasterConfig - 集群 Master 配置
type MasterConfig struct {
	MasterType ccetypes.MasterType `json:"masterType"`

	ClusterHA ccetypes.ClusterHA `json:"clusterHA"`

	ExposedPublic bool `json:"exposedPublic"`

	ClusterBLBVPCSubnetID string `json:"clusterBLBVPCSubnetID"`

	*ManagedClusterMasterOption `json:"managedClusterMasterOption,omitempty"`
	// *CustomBCCClusterMasterOption `json:"customBCCClusterMasterOption,omitempty"`
}

// ManagedClusterMasterOption - 托管 Master 配置
type ManagedClusterMasterOption struct {
	MasterFlavor        ccetypes.MasterFlavor     `json:"masterFlavor"`
	MasterVPCSubnetZone internalvpc.AvailableZone `json:"masterVPCSubnetZone,omitempty"`
}

// // CustomBCCClusterMasterOption - 自定义 Master 配置
// type CustomBCCClusterMasterOption struct {
// 	MasterVPCSubnetID     string `json:"masterVPCSubnetID,omitempty"`
// 	MasterSecurityGroupID string `json:"masterSecurityGroupID,omitempty"`
// }

// InstancePage - 节点分页查询返回
type InstancePage struct {
	ClusterID    string              `json:"clusterID"`
	KeywordType  InstanceKeywordType `json:"keywordType"`
	Keyword      string              `json:"keyword"`
	OrderBy      InstanceOrderBy     `json:"orderBy"`
	Order        Order               `json:"order"`
	PageNo       int                 `json:"pageNo"`
	PageSize     int                 `json:"pageSize"`
	Phases       string              `json:"phases"`
	TotalCount   int                 `json:"totalCount"`
	InstanceList []*Instance         `json:"instanceList"`
}

// InstanceKeywordType 节点模糊查询字段
type InstanceKeywordType string

const (
	// InstanceKeywordTypeInstanceName 节点模糊查询字段: InstanceName
	InstanceKeywordTypeInstanceName InstanceKeywordType = "instanceName"
	// InstanceKeywordTypeInstanceID 节点模糊查询字段: InstanceID
	InstanceKeywordTypeInstanceID InstanceKeywordType = "instanceID"
)

// InstanceOrderBy 节点查询排序字段
type InstanceOrderBy string

const (
	// InstanceOrderByInstanceName 节点查询排序字段: InstanceName
	InstanceOrderByInstanceName InstanceOrderBy = "instanceName"
	// InstanceOrderByInstanceID 节点查询排序字段: InstanceID
	InstanceOrderByInstanceID InstanceOrderBy = "instanceID"
	// InstanceOrderByCreatedAt 节点查询排序字段: CreatedAt
	InstanceOrderByCreatedAt InstanceOrderBy = "createdAt"
)

// Instance - 节点详情
type Instance struct {
	Spec   *InstanceSpec   `json:"spec"`
	Status *InstanceStatus `json:"status"`

	// 节点详情展示依赖 K8S Node 信息, 可选
	// TODO: 目前仅在 GetInstance 中实现, ListInstance 实现存在性能问题
	K8SNode interface{} `json:"k8sNode,omitempty"`

	// CFC/BBE 等内部客户特殊需求, 可选
	InternalFields *InstanceInternalFields `json:"internalFields,omitempty"`

	// K8S 升级选择 Nodes 列表, 可选
	UpgradeNodeFields *UpgradeNodeFields `json:"upgradeNodeFields,omitempty"`

	CreatedAt time.Time `json:"createdAt,omitempty"`
	UpdatedAt time.Time `json:"updatedAt,omitempty"`
}

// InstanceSpec - Instance Spec
type InstanceSpec struct {
	CCEInstanceID string `json:"cceInstanceID"`
	InstanceName  string `json:"instanceName"`

	RuntimeType    ccetypes.RuntimeType `json:"runtimeType"`
	RuntimeVersion string               `json:"runtimeVersion"`

	// 由 cluster-service 生成并更新
	ClusterID   string               `json:"clusterID"`
	ClusterRole ccetypes.ClusterRole `json:"clusterRole"`
	UserID      string               `json:"userID"`

	InstanceGroupID   string `json:"instanceGroupID"`
	InstanceGroupName string `json:"instanceGroupName"`

	// BCC, BBC, 裸金属
	MachineType ccetypes.MachineType `json:"machineType"`
	// 机器规格: 普通一, 普通二 ...
	InstanceType bcc.InstanceType `json:"instanceType"`
	// BBC 选项
	BBCOption *ccetypes.BBCOption `json:"bbcOption,omitempty"`
	// BEC 选项
	BECOption *ccetypes.BECOption `json:"becOption,omitempty"`

	// VPC 相关配置
	VPCConfig `json:"vpcConfig"`

	// 集群规格相关配置
	InstanceResource ccetypes.InstanceResource `json:"instanceResource"`

	// 部署相关高级配置
	DeployCustomConfig ccetypes.DeployCustomConfig `json:"deployCustomConfig,omitempty"`

	ImageID    string              `json:"imageID"`
	InstanceOS ccetypes.InstanceOS `json:"instanceOS"`

	// EIP
	NeedEIP   bool                `json:"needEIP"`
	EIPOption *ccetypes.EIPOption `json:"eipOption"`

	// GPU
	NeedGPU *bool `json:"needGPU,omitempty"`

	SSHKeyID string `json:"sshKeyID"`

	InstanceChargingType bcc.PaymentTiming `json:"instanceChargingType"`

	DeleteOption *ccetypes.DeleteOption `json:"deleteOption"`

	Tags   ccetypes.TagList        `json:"tags,omitempty"`
	Labels ccetypes.InstanceLabels `json:"labels,omitempty"`
	Taints ccetypes.InstanceTaints `json:"taints,omitempty"`

	CCEInstancePriority int `json:"cceInstancePriority,omitempty"`

	// 竞价实例
	Bid       bool               `json:"bid,omitempty"`
	BidOption ccetypes.BidOption `json:"bidOption,omitempty"`

	// 部署集
	DeploySetID string `json:"deploySetID"`

	// 自动快照策略ID
	AutoSnapshotID string `json:"autoSnapshotID"`
}

// InstanceStatus - Instance Status
type InstanceStatus struct {
	Machine Machine `json:"machine"`

	InstancePhase ccetypes.InstancePhase `json:"instancePhase"`
	MachineStatus logicbcc.ServerStatus  `json:"machineStatus"`
}

// InstanceInternalFields - Instance InternalField
// 默认不对客户暴露, 需通过特殊的传参进行暴露
type InstanceInternalFields struct {
	FloatingIP string `json:"floatingIP,omitempty"`
}

// VPCConfig 定义 Instance VPC
type VPCConfig struct {
	VPCID           string `json:"vpcID"`
	VPCSubnetID     string `json:"vpcSubnetID"`
	SecurityGroupID string `json:"securityGroupID"`

	VPCSubnetType     vpc.SubnetType `json:"vpcSubnetType"`
	VPCSubnetCIDR     string         `json:"vpcSubnetCIDR"`
	VPCSubnetCIDRIPv6 string         `json:"vpcSubnetCIDRIPv6"`

	AvailableZone     string        `json:"availableZone"`
	SecurityGroup     SecurityGroup `json:"securityGroup"`
	SecurityGroupType string        `json:"securityGroupType"`
}

// SecurityGroup 安全组信息
type SecurityGroup struct {
	// 是否附加 CCE 必须安全组
	EnableCCERequiredSecurityGroup bool `json:"enableCCERequiredSecurityGroup"`
	// 是否附加 CCE 可选安全组
	EnableCCEOptionalSecurityGroup bool `json:"enableCCEOptionalSecurityGroup"`
	// 用户自定义安全组 ID 列表
	CustomSecurityGroupIDs []string `json:"customSecurityGroups"`
}

// Machine - 定义机器相关信息
type Machine struct {
	InstanceID   string `json:"instanceID"`
	InstanceUUID string `json:"instanceUUID"`

	OrderID string `json:"orderID,omitempty"`

	MountList []ccetypes.MountConfig `json:"mountList,omitempty"`

	VPCIP     string `json:"vpcIP,omitempty"`
	VPCIPIPv6 string `json:"vpcIPIPv6,omitempty"`

	EIP string `json:"eip,omitempty"`

	Hostname string `json:"hostname,omitempty"`

	K8SNodeName string `json:"k8sNodeName,omitempty"`
}

// DeleteInstancesRequest - 删除节点请求
type DeleteInstancesRequest struct {
	InstanceIDs  []string               `json:"instanceIDs,omitempty"`
	DeleteOption *ccetypes.DeleteOption `json:"deleteOption,omitempty"`
}

// InstanceKeyType - ListInstanceByPage 参数
type InstanceKeyType string

// NetworkConflictType 冲突类型
type NetworkConflictType string

const (
	// ContainerCIDRAndNodeCIDRConflict 容器网段和本集群的节点网段冲突
	ContainerCIDRAndNodeCIDRConflict NetworkConflictType = "ContainerCIDRAndNodeCIDR"
	// ContainerCIDRAndExistedClusterContainerCIDRConflict 容器网段和 VPC 内已有集群的容器网段冲突
	ContainerCIDRAndExistedClusterContainerCIDRConflict NetworkConflictType = "ContainerCIDRAndExistedClusterContainerCIDR"
	// ContainerCIDRAndVPCRouteConflict 容器网段与 VPC 路由冲突
	ContainerCIDRAndVPCRouteConflict NetworkConflictType = "ContainerCIDRAndVPCRoute"
	// ClusterIPCIDRAndNodeCIDRConflict ClusterIP 网段与本集群节点网段冲突
	ClusterIPCIDRAndNodeCIDRConflict NetworkConflictType = "ClusterIPCIDRAndNodeCIDR"
	// ClusterIPCIDRAndContainerCIDRConflict ClusterIP 网段与本集群容器网段冲突
	ClusterIPCIDRAndContainerCIDRConflict NetworkConflictType = "ClusterIPCIDRAndContainerCIDR"
)

// PrivateNetString IPv4/IPv6 私有网络地址类型
type PrivateNetString string

const (
	// PrivateIPv4Net10 - IPv4 10 段
	PrivateIPv4Net10 PrivateNetString = "10.0.0.0/8"

	// PrivateIPv4Net172 - IPv4 172 段
	PrivateIPv4Net172 PrivateNetString = "**********/12"

	// PrivateIPv4Net192 - IPv4 192 段
	PrivateIPv4Net192 PrivateNetString = "***********/16"

	// PrivateIPv6Net - IPv6 段
	PrivateIPv6Net PrivateNetString = "fc00::/7"
)

const (
	// https://github.com/kubernetes/kubernetes/blob/ef70690e42b1b9c26453661223b3101d754a12a1/pkg/master/services.go#L41-L44
	// MaxClusterIPServiceNum 集群最大的 ClusterIP Service 数量
	MaxClusterIPServiceNum = 65536
	// MinClusterIPServiceNum 集群最小的 ClusterIP Service 数量
	MinClusterIPServiceNum = 8
)

// CheckContainerNetworkCIDRRequest 包含检查容器网络网段冲突的请求参数
type CheckContainerNetworkCIDRRequest struct {
	VPCID                  string                          `json:"vpcID"`
	VPCCIDR                string                          `json:"vpcCIDR"`
	VPCCIDRIPv6            string                          `json:"vpcCIDRIPv6"`
	ContainerCIDR          string                          `json:"containerCIDR"`
	ContainerCIDRIPv6      string                          `json:"containerCIDRIPv6"`
	ClusterIPCIDR          string                          `json:"clusterIPCIDR"`
	ClusterIPCIDRIPv6      string                          `json:"clusterIPCIDRIPv6"`
	MaxPodsPerNode         int                             `json:"maxPodsPerNode"`
	IPVersion              ccetypes.ContainerNetworkIPType `json:"ipVersion"` // if not set, set ipv4
	SkipContainerCIDRCheck bool                            `json:"skipContainerCIDRCheck"`
}

// CheckContainerNetworkCIDRResponse 检查容器网络网段冲突的响应
type CheckContainerNetworkCIDRResponse struct {
	MaxNodeNum int `json:"maxNodeNum"`
	NetworkConflictInfo
	RequestID string `json:"requestID"`
}

// CheckClusterIPCIDRequest - 检查 ClusterIP CIDR 请求
type CheckClusterIPCIDRequest struct {
	VPCID             string                          `json:"vpcID"`
	VPCCIDR           string                          `json:"vpcCIDR"`
	VPCCIDRIPv6       string                          `json:"vpcCIDRIPv6"`
	ClusterIPCIDR     string                          `json:"clusterIPCIDR"`
	ClusterIPCIDRIPv6 string                          `json:"clusterIPCIDRIPv6"`
	IPVersion         ccetypes.ContainerNetworkIPType `json:"ipVersion"` // if not set, set ipv4
}

// CheckClusterIPCIDRResponse - 检查 ClusterIP CIDR 返回
type CheckClusterIPCIDRResponse struct {
	IsConflict bool   `json:"isConflict"`
	ErrMsg     string `json:"errMsg"`
	RequestID  string `json:"requestID"`
}

// RecommendContainerNetworkCIDRRequest - 一次性推荐容器网段、ClusterIP 网段的请求参数
type RecommendContainerNetworkCIDRRequest struct {
	// 公共参数
	K8SVersion ccetypes.K8SVersion             `json:"k8sVersion"`
	IPVersion  ccetypes.ContainerNetworkIPType `json:"ipVersion"` // if not set, set ipv4
	VPCID      string                          `json:"vpcID"`

	// 推荐容器网段必需的参数
	VPCCIDR                      string             `json:"vpcCIDR"`
	VPCCIDRIPv6                  string             `json:"vpcCIDRIPv6"`
	ClusterMaxNodeNum            int                `json:"clusterMaxNodeNum"`            // 集群节点的最大规模
	MaxPodsPerNode               int                `json:"maxPodsPerNode"`               // 单个节点上最大容器组数
	ContainerPrivateNetCIDRs     []PrivateNetString `json:"containerPrivateNetCIDRs"`     // 候选的容器网段列表，只能从 [10.0.0.0/8, **********/12, ***********/16] 里选择
	ContainerPrivateNetCIDRIPv6s []PrivateNetString `json:"containerPrivateNetCIDRIPv6s"` // 候选的容器网段列表，只能从 [fc00::/7] 里选择

	// 推荐 ClusterIP 网段必需的参数
	ClusterMaxServiceNum         int                `json:"clusterMaxServiceNum"`         // 集群 Service 最大规模
	ClusterIPPrivateNetCIDRs     []PrivateNetString `json:"clusterIPPrivateNetCIDRs"`     // 候选的 ClusterIP 网段列表，只能从 [10.0.0.0/8, **********/12, ***********/16] 里选择
	ClusterIPPrivateNetCIDRIPv6s []PrivateNetString `json:"clusterIPPrivateNetCIDRIPv6s"` // 候选的 ClusterIP 网段列表，只能从 [fc00::/7] 里选择
}

// RecommendContainerNetworkCIDRRequest - 一次性推荐容器网段、ClusterIP 网段的请求参数
type RecommendContainerNetworkCIDRResponse struct {
	RecommendedContainerCIDR     string `json:"recommendedContainerCIDR"`
	RecommendedContainerCIDRIPv6 string `json:"recommendedContainerCIDRIPv6"`
	RecommendedClusterIPCIDR     string `json:"recommendedClusterIPCIDR"`
	RecommendedClusterIPCIDRIPv6 string `json:"recommendedClusterIPCIDRIPv6"`
	IsSuccess                    bool   `json:"isSuccess"`
	ErrMsg                       string `json:"errMsg"`
	RequestID                    string `json:"requestID"`
}

// RecommendContainerCIDRRequest 推荐容器网段的请求参数
type RecommendContainerCIDRRequest struct {
	VPCID       string `json:"vpcID"`
	VPCCIDR     string `json:"vpcCIDR"`
	VPCCIDRIPv6 string `json:"vpcCIDRIPv6"`
	// ClusterMaxNodeNum 集群节点的最大规模
	ClusterMaxNodeNum int `json:"clusterMaxNodeNum"`
	MaxPodsPerNode    int `json:"maxPodsPerNode"`
	// PrivateNetCIDRs 候选的容器网段列表，只能从 [10.0.0.0/8, **********/12, ***********/16] 里选择
	PrivateNetCIDRs []PrivateNetString `json:"privateNetCIDRs"`
	// PrivateNetCIDRIPv6s 候选的容器网段列表，只能从 [fc00::/7] 里选择
	PrivateNetCIDRIPv6s []PrivateNetString              `json:"privateNetCIDRIPv6s"`
	K8SVersion          ccetypes.K8SVersion             `json:"k8sVersion"`
	IPVersion           ccetypes.ContainerNetworkIPType `json:"ipVersion"` // if not set, set ipv4
}

// RecommendContainerCIDRResponse 推荐容器网段的响应
type RecommendContainerCIDRResponse struct {
	RecommendedContainerCIDRs     []string `json:"recommendedContainerCIDRs"`
	RecommendedContainerCIDRIPv6s []string `json:"recommendedContainerCIDRIPv6s"`
	IsSuccess                     bool     `json:"isSuccess"`
	ErrMsg                        string   `json:"errMsg"`
	RequestID                     string   `json:"requestID"`
}

// RecommendClusterIPCIDRRequest 推荐 ClusterIP 网段的请求参数
type RecommendClusterIPCIDRRequest struct {
	VPCCIDR           string `json:"vpcCIDR"`
	VPCCIDRIPv6       string `json:"vpcCIDRIPv6"`
	ContainerCIDR     string `json:"containerCIDR"`
	ContainerCIDRIPv6 string `json:"containerCIDRIPv6"`
	// ClusterMaxServiceNum 集群 Service 最大规模
	ClusterMaxServiceNum int `json:"clusterMaxServiceNum"`
	// PrivateNetCIDRs 候选的 ClusterIP 网段列表，只能从 [10.0.0.0/8, **********/12, ***********/16] 里选择
	PrivateNetCIDRs     []PrivateNetString              `json:"privateNetCIDRs"`
	PrivateNetCIDRIPv6s []PrivateNetString              `json:"privateNetCIDRIPv6s"`
	IPVersion           ccetypes.ContainerNetworkIPType `json:"ipVersion"` // if not set, set ipv4
}

// RecommendClusterIPCIDRResponse 推荐 ClusterIP 网段的响应
type RecommendClusterIPCIDRResponse struct {
	RecommendedClusterIPCIDRs     []string `json:"recommendedClusterIPCIDRs"`
	RecommendedClusterIPCIDRIPv6s []string `json:"recommendedClusterIPCIDRIPv6s"`
	IsSuccess                     bool     `json:"isSuccess"`
	ErrMsg                        string   `json:"errMsg"`
	RequestID                     string   `json:"requestID"`
}

// NetworkConflictInfo 容器网络整体配置冲突信息
type NetworkConflictInfo struct {
	IsConflict            bool                   `json:"isConflict"`
	ErrMsg                string                 `json:"errMsg"`
	ContainerCIDRConflict *ContainerCIDRConflict `json:"containerCIDRConflict"` // 容器网段冲突信息
	ClusterIPCIDRConflict *ClusterIPCIDRConflict `json:"clusterIPCIDRConflict"` // ClusterIP 网段冲突信息
}

// ContainerCIDRConflict 容器网段冲突信息
type ContainerCIDRConflict struct {
	// NetworkConflictType 冲突类型，可取的值： ContainerCIDRAndNodeCIDRConflict、ContainerCIDRAndExistedClusterContainerCIDRConflict、ContainerCIDRAndVPCRouteConflict
	ConflictType NetworkConflictType `json:"conflictType"`
	// ConflictNodeCIDR 与容器网段冲突的节点网段，当且仅当 NetworkConflictType 为 ContainerCIDRAndNodeCIDRConflict 不为 nil
	ConflictNodeCIDR *ConflictNodeCIDR `json:"conflictNodeCIDR"`
	// ConflictCluster 与容器网段冲突的VPC内集群，当且仅当 NetworkConflictType 为 ContainerCIDRAndExistedClusterContainerCIDRConflict 不为 nil
	ConflictCluster *ConflictCluster `json:"conflictCluster"`
	// ConflictVPCRoute 与容器网段冲突的VPC路由，当且仅当 NetworkConflictType 为 ContainerCIDRAndVPCRouteConflict 不为 nil
	ConflictVPCRoute *ConflictVPCRoute `json:"conflictVPCRoute"`
}

// ClusterIPCIDRConflict ClusterIP 网段冲突信息
type ClusterIPCIDRConflict struct {
	// NetworkConflictType 冲突类型，可取的值： ClusterIPCIDRAndNodeCIDRConflict、ClusterIPCIDRAndContainerCIDRConflict
	ConflictType NetworkConflictType `json:"conflictType"`
	// ConflictNodeCIDR 与 ClusterIP 网段冲突的节点网段，当且仅当 NetworkConflictType 为 ClusterIPCIDRAndNodeCIDRConflict 不为 nil
	ConflictNodeCIDR *ConflictNodeCIDR `json:"conflictNodeCIDR"`
	// ConflictContainerCIDR 与 ClusterIP 网段冲突的节点网段，当且仅当 NetworkConflictType 为 ClusterIPCIDRAndContainerCIDRConflict 不为 nil
	ConflictContainerCIDR *ConflictContainerCIDR `json:"conflictContainerCIDR"`
}

// ConflictNodeCIDR 节点网段冲突信息
type ConflictNodeCIDR struct {
	NodeCIDR string `json:"nodeCIDR"`
}

// ConflictContainerCIDR 容器网段冲突信息
type ConflictContainerCIDR struct {
	ContainerCIDR string `json:"containerCIDR"`
}

// ConflictCluster 同一 VPC 内容器网段冲突的集群信息
type ConflictCluster struct {
	ClusterID     string `json:"clusterID"`
	ContainerCIDR string `json:"containerCIDR"`
}

// ConflictVPCRoute 冲突的 VPC 路由
type ConflictVPCRoute struct {
	RouteRule vpc.RouteRule `json:"routeRule"`
}

// GetEventStepsResponse 查询 Cluster/Instance 创建/删除 返回
type GetEventStepsResponse struct {
	Status    EventStatus `json:"status"`
	Steps     []*Step     `json:"steps"`
	RequestID string      `json:"requestID"`
}

// EventStatus - Event 类型
type EventStatus string

const (
	// EventStatusCreating -
	EventStatusCreating EventStatus = "creating"

	// EventStatusCreated -
	EventStatusCreated EventStatus = "created"

	// EventStatusCreateFailed -
	EventStatusCreateFailed EventStatus = "create_failed"

	// EventStatusDeleting -
	EventStatusDeleting EventStatus = "deleting"

	// EventStatusDeleted -
	EventStatusDeleted EventStatus = "deleted"

	// EventStatusDeleteFailed -
	EventStatusDeleteFailed EventStatus = "delete_failed"

	// EventStatusUpgrading -
	EventStatusUpgrading EventStatus = "upgrading"

	// EventStatusUpgraded -
	EventStatusUpgraded EventStatus = "upgraded"

	// EventStatusUpgradeFailed -
	EventStatusUpgradeFailed EventStatus = "upgrade_failed"
)

// Step - 集群操作步骤
type Step struct {
	StepName   StepName   `json:"stepName"`
	StepStatus StepStatus `json:"stepStatus"`
	ccetypes.Step
}

type StepName string

const (
	ClusterStepCreateCertificateAuthority StepName = "创建基础证书"
	ClusterStepCreateLB                   StepName = "创建 BLB"
	ClusterStepCreateEIP                  StepName = "创建 EIP"
	ClusterStepWaitMasterInfrastructure   StepName = "部署 Master"
	ClusterStepWaitAPIServerAccess        StepName = "连通 APIServer"
	ClusterStepDeployK8SPlugin            StepName = "部署 K8S 插件"

	ClusterStepDeleteK8SResource     StepName = "删除 K8S 资源"
	ClusterStepDeleteNodeInstance    StepName = "删除 Worker"
	ClusterStepDeleteMasterInstance  StepName = "删除 Master"
	ClusterStepDeleteEIP             StepName = "删除 EIP"
	ClusterStepDeleteLB              StepName = "删除 BLB"
	ClusterStepDeleteConfigMapInMeta StepName = "清理控制资源"

	InstanceStepCreateMachine          StepName = "准备机器"
	InstanceStepEnsureSecurityGroups   StepName = "绑定安全组"
	InstanceStepWaitAPIServerWhiteList StepName = "前置检查"
	InstanceStepDeploy                 StepName = "部署 K8S"
	InstanceStepWaitForNodeReady       StepName = "等待就绪"

	NodeStepDeleteK8SNode                   StepName = "移出集群"
	InstanceStepDeleteMachineAssociatedIaaS StepName = "回收关联资源"
	InstanceStepDeleteMachine               StepName = "删除机器"
	InstanceStepDeleteMachineAssociatedENI  StepName = "回收关联弹性网卡"
)

// StepStatus - 集群操作步骤状态
type StepStatus string

const (
	// StepStatusTodo 待开始
	StepStatusTodo StepStatus = "todo"

	// StepStatusDoing 正在进行中
	StepStatusDoing StepStatus = "doing"

	// StepStatusPaused 暂停
	StepStatusPaused StepStatus = "paused"

	// StepStatusDone 已完成
	StepStatusDone StepStatus = "done"

	// StepStatusFailed 已失败
	StepStatusFailed StepStatus = "failed"
)

// CheckCCESGRulesAllIncludedResponse - 检查指定安全组是否包含全部的 CCE 安全组规则 (Custom Master or Node)
type CheckCCESGRulesAllIncludedResponse struct {
	CCESGRulesAllIncluded bool   `json:"cceSGRulesAllIncluded"`
	RequestID             string `json:"requestID"`
}

// SecurityGroupResponse - 查询、创建 CCE 默认安全组, 添加 CCE 安全组规则返回
type SecurityGroupResponse struct {
	SecurityGroupID string                  `json:"securityGroupID"`
	SGRules         []bcc.SecurityGroupRule `json:"sgRules,omitempty"`
	RequestID       string                  `json:"requestID"`
}

// QueryBLBPriceRequest - BLB 询价请求, 只支持后付费
type QueryBLBPriceRequest struct {
	BLBType     internalblb.BLBType `json:"blbType"`
	PurchaseNum int                 `json:"purchaseNum"`
}

// QueryEIPPriceRequest - EIP 询价请求
// (1) 后付费有两种计费方式: 按带宽计费, 按流量计费; 预付费仅有: 按带宽计费
// (2) 按带宽计费仅产生带宽费用, 按流量计费除了较少量的带宽费用外还会产生流量费用
type QueryEIPPriceRequest struct {
	BandwidthInMbps  int               `json:"bandwidthInMbps"`
	PaymentTiming    eip.PaymentTiming `json:"paymentTiming"`
	BillingMethod    eip.BillingMethod `json:"billingMethod"`
	PrepaidUnitCount int               `json:"prepaidUnitCount"`
	PrepaidUnit      Unit              `json:"prepaidUnit"`
	PurchaseNum      int               `json:"purchaseNum"`
}

// QueryBCCPriceRequest - 单配置 BCC 询价请求, 含附属的 CDS 和 EIP
type QueryBCCPriceRequest struct {
	PaymentTiming bcc.PaymentTiming `json:"paymentTiming"`
	// PurchaseNum 购买数量
	PurchaseNum int `json:"purchaseNum"`
	// PrepaidTime 预付费购买时长, 单位: month
	PrepaidTime int `json:"prepaidUnitCount"`

	// BCC 询价参数
	BCC *BCCPriceArgs `json:"bcc"`
	// CDS 询价参数
	CDS *CDSPriceArgs `json:"cds"`
	// EIP 询价参数
	EIP *EipPriceArgs `json:"eip"`
}

// QueryBCCPriceRequest - 多配置 BCC 询价请求, 含附属的 CDS 和 EIP
type QueryBCCsPriceRequest struct {
	PaymentTiming bcc.PaymentTiming `json:"paymentTiming"`

	PriceArgs []PriceArgs `json:"priceArgs"`
}

type PriceArgs struct {
	// PurchaseNum 购买数量
	PurchaseNum int `json:"purchaseNum"`
	// PrepaidTime 预付费购买时长, 单位: month
	PrepaidTime int `json:"prepaidUnitCount"`

	// BCC 询价参数
	BCC *BCCPriceArgs `json:"bcc"`
	// CDS 询价参数
	CDS *CDSPriceArgs `json:"cds"`
	// EIP 询价参数
	EIP *EipPriceArgs `json:"eip"`
}

type QueryBBCPriceRequest struct {
	*bbc.PriceRequest `json:",inline"`
}

// BCCPriceArgs - BCC 询价参数
type BCCPriceArgs struct {
	InstanceType bcc.InstanceType `json:"instanceType"`

	CPU                 int             `json:"cpu"`
	Memory              int             `json:"memory"`
	RootDiskSizeInGB    int             `json:"rootDiskSizeInGB"`
	RootDiskStorageType bcc.StorageType `json:"rootDiskStorageType"`

	// GPU 配置
	GPUCard       string `json:"gpuCard"`
	GPUCount      int    `json:"gpuCount"`
	LocalDiskSize int    `json:"localDiskSize"`
	SpecID        string `json:"specId"`
	Spec          string `json:"spec"`

	// FPGA 配置
	FPGACard     string `json:"fpgaCard"`
	FPGACount    int    `json:"fpgaCount"`
	ContainsFPGA bool   `json:"containsFPGA"`

	// KunLun 配置
	KunLunCard  string `json:"kunLunCard"`
	KunLunCount int    `json:"kunLunCount"`
}

// CDSPriceArgs - CDS 询价参数
type CDSPriceArgs struct {
	Disks []Disk `json:"disks"`
}

type Disk struct {
	SizeInGB    int             `json:"sizeInGB"`
	StorageType bcc.StorageType `json:"storageType"`
	SnapshotID  string          `json:"snapshotID"`
}

// EIPPriceArgs - EIP 询价参数
type EipPriceArgs struct {
	BillingMethod   eip.BillingMethod `json:"billingMethod"`
	BandwidthInMbps int               `json:"bandwidthInMbps"`
}

// QueryClusterInfrastructurePriceRequest - 集群询价请求
type QueryClusterInfrastructurePriceRequest struct {
	MasterType    ccetypes.MasterType `json:"masterType"`
	ExposedPublic bool                `json:"exposedPublic"`
	EIPBandwidth  int                 `json:"eipBandwidth"`
}

// QueryPriceResponse - 询价返回, 通用
type QueryPriceResponse struct {
	// BasePrice 除 EIP 以外的 IaaS 资源费用
	BasePrice *Price `json:"basePrice"`
	// EIPBandwidthPrice EIP 的带宽费用
	EIPBandwidthPrice *Price `json:"eipBandwidthPrice,omitempty"`
	// EIPTrafficPrice EIP 的流量费用, 仅后付费按流量计费的 EIP 包含此费用
	EIPTrafficPrice *Price `json:"eipTrafficPrice,omitempty"`
	RequestID       string `json:"requestID"`
}

// Price IaaS 资源的价格, 举例如下
// (1) 1 台 BCC 每分钟 0.0032 元, 后付费 (PurchaseNum=1 在用户请求中指定的, UnitCount=1, Unit=minute, Money=0.0032)
// (2) 2 台 BCC 每分钟 0.0064 元, 后付费 (PurchaseNum=2 在用户请求中指定的, UnitCount=1, Unit=minute, Money=0.0064)
// (3) 1 台 BCC 2 个月 200 元, 预付费 (PurchaseNum=1 在用户请求中指定的, UnitCount=2, Unit=month, Money=200)
// (4) 1 个 EIP 每 GB 流量 0.76 元, 后付费 (PurchaseNum=1 在用户请求中指定的, UnitCount=1, Unit=GB, Money=0.76)
type Price struct {
	// Money 购买 UnitCount * Unit * PurchaseNum 资源所需花费 (注: 资源购买的数量不一定为 1, 由用户请求参数 PurchaseNum 指定)
	Money float64 `json:"money"`
	// UnitCount 购买资源 Unit 数, 后付费返回固定为 1, 预付费返回 >= 1
	UnitCount int `json:"unitCount"`
	// Unit 资源计费单位
	Unit Unit `json:"unit"`
}

// Unit - 资源计费单位
type Unit string

const (
	// UnitMinute 时间单位分钟, 后付费使用
	UnitMinute Unit = "minute"
	// UnitMonth 时间单位月, 预付费使用
	UnitMonth Unit = "month"
	// UnitGB 流量单位 GB, 后付费 EIP 按流量计费使用
	UnitGB Unit = "GB"
)

const (
	DefaultShrinkPolicy               = PriorityShrinkPolicy
	PriorityShrinkPolicy ShrinkPolicy = "Priority"
	RandomShrinkPolicy   ShrinkPolicy = "Random"

	DefaultUpdatePolicy                  = ConcurrencyUpdatePolicy
	RollingUpdatePolicy     UpdatePolicy = "Rolling"
	ConcurrencyUpdatePolicy UpdatePolicy = "Concurrency"

	DefaultCleanPolicy             = RemainCleanPolicy
	RemainCleanPolicy  CleanPolicy = "Remain"
	DeleteCleanPolicy  CleanPolicy = "Delete"
)

type InstanceGroup struct {
	Spec      *InstanceGroupSpec   `json:"spec"`
	Status    *InstanceGroupStatus `json:"status"`
	CreatedAt time.Time            `json:"createdAt"`
	DeletedAt time.Time            `json:"deletedAt"`
	Deleted   bool                 `json:"deleted"`
}

// TODO: 考虑到如果要把sdk开源出去，其实不应该import一些内部的包，比如ccetypes和models，内部的包包含了一些我们实现细节相关的字段， 最好在sdk里面定义一份仅仅包含暴露给用户的字段的结构体，后面再改吧
type InstanceGroupSpec struct {
	CCEInstanceGroupID string `json:"cceInstanceGroupID,omitempty"`
	InstanceGroupName  string `json:"instanceGroupName"`

	ClusterID   string               `json:"clusterID,omitempty"`
	ClusterRole ccetypes.ClusterRole `json:"clusterRole,omitempty"`
	UserID      string               `json:"userID,omitempty" gorm:"column:user_id"`
	AccountID   string               `json:"accountID,omitempty" gorm:"column:account_id"`

	ShrinkPolicy ShrinkPolicy `json:"shrinkPolicy,omitempty"`
	UpdatePolicy UpdatePolicy `json:"updatePolicy,omitempty"`
	CleanPolicy  CleanPolicy  `json:"cleanPolicy,omitempty"`

	InstanceTemplate InstanceTemplate `json:"instanceTemplate"`
	Replicas         int              `json:"replicas"`

	ClusterAutoscalerSpec *ClusterAutoscalerSpec `json:"clusterAutoscalerSpec,omitempty"`
}

type InstanceTemplate struct {
	InstanceSpec `json:",inline"`
}

type InstanceLabels map[string]string

type InstanceTaints []corev1.Taint

type ShrinkPolicy string
type UpdatePolicy string
type CleanPolicy string

type ClusterAutoscalerSpec struct {
	Enabled              bool `json:"enabled"`
	MinReplicas          int  `json:"minReplicas"`
	MaxReplicas          int  `json:"maxReplicas"`
	ScalingGroupPriority int  `json:"scalingGroupPriority"`
}

// InstanceGroupStatus -
type InstanceGroupStatus struct {
	ActualReplicas   int          `json:"actualReplicas"`
	ReadyReplicas    int          `json:"readyReplicas"`
	ScalingReplicas  int          `json:"scalingReplicas"`
	DeletingReplicas int          `json:"deletingReplicas"`
	OtherReplicas    int          `json:"otherReplicas"`
	Pause            *PauseDetail `json:"pause,omitempty"`
}

type PauseDetail struct {
	Paused bool   `json:"paused"`
	Reason string `json:"reason"`
}

// CreateInstanceGroupRequest - 创建InstanceGroup request
type CreateInstanceGroupRequest struct {
	ccetypes.InstanceGroupSpec
}

// CreateInstanceGroupResponse - 创建InstanceGroup response
type CreateInstanceGroupResponse struct {
	CommonResponse
	InstanceGroupID string `json:"instanceGroupID"`
}

type ListInstanceGroupResponse struct {
	CommonResponse
	Page ListInstanceGroupPage `json:"page"`
}

type ListInstanceGroupPage struct {
	PageNo     int              `json:"pageNo"`
	PageSize   int              `json:"pageSize"`
	TotalCount int              `json:"totalCount"`
	List       []*InstanceGroup `json:"list"`
}

type GetInstanceGroupResponse struct {
	CommonResponse
	InstanceGroup *InstanceGroup `json:"instanceGroup"`
}

type UpdateInstanceGroupReplicasRequest struct {
	Replicas       int                    `json:"replicas"`
	InstanceIDs    []string               `json:"instanceIDs"`
	DeleteInstance bool                   `json:"deleteInstance"`
	DeleteOption   *ccetypes.DeleteOption `json:"deleteOption,omitempty"`
}

type UpdateInstanceGroupReplicasResponse struct {
	CommonResponse
}

type UpdateInstanceGroupClusterAutoscalerSpecResponse struct {
	CommonResponse
}

type UpdateInstanceGroupInstanceTemplateRequest struct {
	DeployCustomConfig ccetypes.DeployCustomConfig `json:"deployCustomConfig,omitempty"`

	Tags   ccetypes.TagList        `json:"tags,omitempty"`
	Labels ccetypes.InstanceLabels `json:"labels,omitempty"`
	Taints ccetypes.InstanceTaints `json:"taints,omitempty"`
}

type UpdateInstanceGroupInstanceTemplateResponse struct {
	CommonResponse
}

type UpdateInstanceGroupPausedStatusResponse struct {
	CommonResponse
}

type DeleteInstanceGroupResponse struct {
	CommonResponse
}

type ListInstancesByInstanceGroupIDPage struct {
	PageNo     int         `json:"pageNo"`
	PageSize   int         `json:"pageSize"`
	TotalCount int         `json:"totalCount"`
	List       []*Instance `json:"list"`
}

type ListInstancesByInstanceGroupIDResponse struct {
	CommonResponse
	Page ListInstancesByInstanceGroupIDPage `json:"page"`
}

type CommonResponse struct {
	RequestID string `json:"requestID"`
}

// GetClusterOfBBEResponse - 兼容 BBE V1 接口, 仅提供 BBE 使用
// 尽量保证和先前接口一致
type GetClusterOfBBEResponse struct {
	ClusterID   string `json:"clusterId"`
	ClusterName string `json:"clusterName"`
	UserID      string `json:"userId"`

	// Read from DB
	CAPem string `json:"ca_cert"`

	// Read from Master
	AdminPem    string `json:"admin_cert"`
	AdminKeyPem string `json:"adminKey"`

	// Cluster BLB
	ClusterEIP           string `json:"master_eip"`
	ClusterBLBFloatingIP string `json:"master_blb_vip"`
	ClusterBLBVPCIP      string `json:"master_blb_vpc_ip"`

	// 需包含 floatingIP
	Nodes []*BBEInstance `json:"workerInfo"`
}

// BBEInstance - 兼容 BBE V1 接口, 仅提供 BBE 使用
type BBEInstance struct {
	InstanceID string `json:"instanceId"`
	FixedIP    string `json:"fixedIP"`
	FloatingIP string `json:"floatingIP"`
	RootPasswd string `json:"rootPasswd"`
	CPU        int    `json:"cpu"`
	MEM        int    `json:"memory"`
}

// SyncInstancesResponse - 集群 instances 信息同步
type SyncInstancesResponse struct {
	ClusterID string `json:"clusterID"`
	RequestID string `json:"requestID"`
}

// SupportedOSRequest 支持的镜像OS
type SupportedOSRequest struct {
	logicimage.ImageListRequest `json:",inline"`
	SkipFilter                  bool `json:"skipFilter"`
}

// SupportedOSResponse 支持的镜像OS
type SupportedOSResponse struct {
	logicimage.ImageListResponse `json:",inline"`
	CommonResponse
}

// UnSupportedInstanceTypeResponse 不支持的机型
type UnSupportedInstanceTypeResponse struct {
	InstanceTypes []logicbcc.InstanceType `json:"instanceTypes"`
	CommonResponse
}

// SupportedImageTypesResponse 不支持的机型
type SupportedImageTypesResponse struct {
	ImageTypes []logicimage.ImageType `json:"imageTypes"`
	CommonResponse
}

// SupportedBBCOSResponse 支持的BBC镜像OS
type SupportedBBCOSResponse struct {
	bbc.FlavorImagesResponse `json:",inline"`
	CommonResponse
}

type DeleteOptions struct {
	MoveOut           bool `json:"moveOut,omitempty"`
	DeleteResource    bool `json:"deleteResource,omitempty"`
	DeleteCDSSnapshot bool `json:"deleteCDSSnapshot,omitempty"`
}

// CCE 标准错误检查
type CheckRequest struct {
	CheckType    CheckType             `json:"checkType"`
	CheckOptions map[OptionName]string `json:"checkOptions"`
}

type CheckType string

const (
	CheckTypeInstanceQuota        CheckType = "INSTANCE_QUOTA"
	CheckTypeBLBQuota             CheckType = "BLB_QUOTA"
	CheckTypeAccountBalance       CheckType = "ACCOUNT_BALANCE"
	CheckTypeSubnetAvailableIP    CheckType = "SUBNET_AVAILABLE_IP"
	CheckTypeClusterQuota         CheckType = "CLUSTER_QUOTA"
	CheckTypeClusterNodeQuota     CheckType = "CLUSTER_NODE_QUOTA"
	CheckTypeRealNameAuthenticate CheckType = "REAL_NAME_AUTHENTICATE"
)

type OptionName string

const (
	OptionNameSubnetID      OptionName = "subnetID"
	OptionNamePaymentTiming OptionName = "paymentTiming"
	OptionNameAddNum        OptionName = "addNum"
	OptionNameClusterID     OptionName = "clusterID"
	OptionNameLevel         OptionName = "level"
)

type CheckResponse struct {
	Pass      bool   `json:"pass"`
	RequestID string `json:"requestID"`
}

// ClusterBCMSetting - 集群的 BCM 事件推送设置
type ClusterBCMSetting struct {
	ClusterID string `json:"clusterID"`
	Enable    bool   `json:"enable"`
	RequestID string `json:"requestID"`
}

type AIQueueDetail struct {
	AIQueue   AIQueue `json:"queue"`
	RequestID string  `json:"requestID"`
}

type ListAIQueuesResponse struct {
	AIQueuePage *AIQueuePage `json:"queuePage"`
	RequestID   string       `json:"requestID"`
}

type AIQueuePage struct {
	OrderBy     string     `json:"orderBy"`
	Order       string     `json:"order"`
	Keyword     string     `json:"keyword"`
	KeywordType string     `json:"keywordType"`
	PageNo      int        `json:"pageNo"`
	PageSize    int        `json:"pageSize"`
	TotalCount  int        `json:"totalCount"`
	QueueList   []*AIQueue `json:"queueList"`
}

type AIQueue struct {
	Name            string                       `json:"name"`
	Description     string                       `json:"description"`
	Capability      AIQueueCapability            `json:"capability"`
	State           schedulingv1beta1.QueueState `json:"state"`
	Users           []iam.UserInfo               `json:"users"`
	CreatedTime     time.Time                    `json:"createdTime"`
	DisableOversell bool                         `json:"disableOversell"`
	PodGroupNum     int32                        `json:"podGroupNum"`
}

type AIQueueCapability struct {
	CPURequest          int                   `json:"cpu"`
	MemoryRequest       MemoryUnit            `json:"memory"`
	ExclusiveCapability map[string]int        `json:"exclusiveCapability"` // 独占
	SharingCapability   map[string]MemoryUnit `json:"sharingCapability"`   // 共享
}

type MemoryUnit struct {
	Capacity int64  `json:"capacity"`
	Unit     string `json:"unit"`
}

type QueueJobListResponse struct {
	QueueJobs map[string][]*AIJob `json:"queueJobs"`
	RequestID string              `json:"requestID"`
}

type UpdateAIQueueRequest struct {
	Description     string            `json:"description"`
	Capability      AIQueueCapability `json:"capability"`
	DisableOversell bool              `json:"disableOversell"`
	Users           []string          `json:"users"`
}

type NodesCapability struct {
	CPU                 int64           `json:"cpu"`
	Memory              int64           `json:"memory"`
	ExclusiveCapability []GPUCapability `json:"exclusiveCapability"` // 独占
	SharingCapability   []GPUCapability `json:"sharingCapability"`   // 共享
}

type GPUCapability struct {
	DisplayName  string `json:"displayName"`
	ResourceName string `json:"resourceName"`
	Quantity     int64  `json:"quantity"`
}

type NodeCapabilityResponse struct {
	RequestID  string           `json:"requestID"`
	Capability *NodesCapability `json:"capability"`
}

// MonitorResponse - 插叙 cce-thanos 监控返回结果
type MonitorResponse struct {
	Data      interface{} `json:"data,omitempty"`
	RequestID string      `json:"requestID"`
	Status    string      `json:"status"`
}

// MonitorQueryData 保持数据结构与 prometheus v1 http 接口一致
type MonitorQueryData struct {
	ResultType interface{} `json:"resultType"`
	Result     interface{} `json:"result"`

	// Additional Thanos Response field.
	Warnings []string `json:"warnings,omitempty"`
}

// AIJob
type CreateAIJobRequest struct {
	Name                 string            `json:"name"`
	Namespace            string            `json:"namespace"`
	Kind                 string            `json:"kind"`
	Queue                string            `json:"queue"`
	Manifest             string            `json:"manifest"`
	DryRun               bool              `json:"dryRun"`
	EnableOversell       bool              `json:"enableOversell"`
	EnableFaultTolerance bool              `json:"enableFaultTolerance"`
	Priority             string            `json:"priority"`
	Datasource           []AIJobDatasource `json:"datasource"`
}

type AIJobV2 struct {
	// 基本信息
	Name           string `json:"name"`
	Namespace      string `json:"namespace"`
	Queue          string `json:"queue"`
	Priority       string `json:"priority"`
	Oversell       bool   `json:"oversell"`
	FaultTolerance bool   `json:"faultTolerance"`
	// Code 代码配置
	Code *AIJobV2CodeConfig `json:"code,omitempty"`
	// Command 启动命令
	Command string `json:"command"`
	// Datasource 设置数据源
	Datasource     []AIJobDatasource    `json:"datasource"`
	JobFramework   string               `json:"jobFramework"`
	JobDistributed bool                 `json:"jobDistributed"`
	JobSpec        map[string]AIJobSpec `json:"jobSpec"`
	// TimeLimit 任务最大运行时间，单位秒，0为不限制
	TimeLimit        int                     `json:"timeLimit,omitempty"`
	ImagePullSecrets []string                `json:"imagePullSecrets"`
	Tensorboard      *AIJobTensorboardConfig `json:"tensorboard,omitempty"`
	Labels           map[string]string       `json:"labels"`
	Annotations      map[string]string       `json:"annotations"`

	// AITrainingJobFramework AITrainingJob Operator 还可以指定框架类型 horovod/paddle
	AITrainingJobFramework string `json:"aiTrainingJobFramework,omitempty"`
	// AITrainingJobMinReplicas AITrainingJob Operator 实现弹性需要指定最小实例数量
	AITrainingJobMinReplicas int32 `json:"aiTrainingJobMinReplicas,omitempty"`
	// AITrainingJobMaxReplicas AITrainingJob Operator 实现弹性需要指定最大实例数量
	AITrainingJobMaxReplicas int32 `json:"aiTrainingJobMaxReplicas,omitempty"`
}

type AIJobV2CodeConfig struct {
	MountPath string                   `json:"mountPath"`
	BOS       *AIJobV2CodeConfigBOS    `json:"bos,omitempty"`
	Upload    *AIJobV2CodeConfigUpload `json:"upload,omitempty"`
}
type AIJobV2CodeConfigBOS struct {
	Bucket             string                         `json:"bucket"`
	Endpoint           string                         `json:"endpoint"`
	FilePath           string                         `json:"filePath"`
	AccessKeyIDRef     *AIJobV2CodeConfigBOSSecretRef `json:"accessKeyIdRef"`
	AccessKeySecretRef *AIJobV2CodeConfigBOSSecretRef `json:"accessKeySecretRef"`
}
type AIJobV2CodeConfigBOSSecretRef struct {
	Secret string `json:"secret"`
	Key    string `json:"key"`
}

type AIJobV2CodeConfigUpload struct {
	Files []AIJobV2CodeConfigUploadFile `json:"files"`
}

type AIJobV2CodeConfigUploadFile struct {
	Name string `json:"file"`
	URL  string `json:"url"`
}

type AIJobSpec struct {
	Replicas      int32              `json:"replicas"`
	RestartPolicy string             `json:"restartPolicy"`
	Image         string             `json:"image"`
	Tag           string             `json:"tag"`
	Resource      map[string]float64 `json:"resource"`
	Env           map[string]string  `json:"env"`
	Command       string             `json:"command"`
	Args          string             `json:"args"`
	PostStart     string             `json:"postStart"`
	PreStop       string             `json:"preStop"`
}

type AIJobTensorboardConfig struct {
	Enable      bool               `json:"enable"`
	LogPath     string             `json:"logPath"`
	ServiceType corev1.ServiceType `json:"serviceType"`
}

type AIJobDatasource struct {
	// Type 数据源类型 dataset/pvc
	Type      string `json:"type"`
	Name      string `json:"name"`
	PVC       string `json:"pvc"`
	MountPath string `json:"mountPath"`
}

type AIJobList struct {
	List  []*AIJob `json:"list"`
	Total int      `json:"total"`
}

type AIJob struct {
	Name                 string            `json:"name"`
	K8SName              string            `json:"k8sName"`
	K8SNamespace         string            `json:"k8sNamespace"`
	Queue                string            `json:"queue"`
	Kind                 string            `json:"kind"`
	Status               string            `json:"status"`
	EnableOversell       bool              `json:"enableOversell"`
	EnableFaultTolerance bool              `json:"enableFaultTolerance"`
	Priority             string            `json:"priority"`
	CodeConfig           string            `json:"codeConfig"`
	Command              string            `json:"command"`
	TimeLimit            int               `json:"timeLimit"`
	Tensorboard          string            `json:"tensorboard"`
	LogPath              string            `json:"logPath"`
	RawRequest           string            `json:"rawRequest"`
	Datasource           []AIJobDatasource `json:"datasource"`
	CreatedAt            metav1.Time       `json:"createdAt"`
	FinishedAt           *metav1.Time      `json:"finishedAt"`
}

type ListDatasetResponse struct {
	CommonResponse
	PageNo     int           `json:"pageNo,omitempty"`
	PageSize   int           `json:"pageSize,omitempty"`
	TotalCount int           `json:"totalCount"`
	Datasets   []DatasetItem `json:"datasets"`
}

type DatasetItem struct {
	Name       string    `json:"name"`
	Namespace  string    `json:"namespace"`
	CreateTime time.Time `json:"createTime"`

	// MountPoints 数据源挂载点，可能有多个
	MountPoints []string `json:"mountPoints"`
	// DatasetStatus 数据集状态 `Pending`, `Bound`, `NotBound`, `Failed`
	DatasetStatus string `json:"datasetStatus"`
	// SpeedupStatus 判断 dataset 是否有相应的 runtime
	SpeedupStatus bool `json:"speedUpStatus"`
	// CacheStates 缓存状态如：缓存容量、已缓存大小、缓存百分比等
	CacheStates map[string]string `json:"cacheStatus"`
	// FileNum 文件数量
	FileNum string `json:"fileNum"`
	// UfsTotal 源数据总大小
	UfsTotal string `json:"ufsTotal"`
}

type GetDatasetResponse struct {
	CommonResponse
	Dataset string `json:"dataset"`
	Runtime string `json:"runtime"`
}

type CreateDatasetRequest struct {
	Dataset string `json:"dataset"`
	Runtime string `json:"runtime"`
}

type DataloadTarget struct {
	Path     string `json:"path"`
	Replicas int    `json:"replicas,omitempty"`
}

type CreateDataloadRequest struct {
	Target []DataloadTarget `json:"target,omitempty"`
}

type DataloadItem struct {
	Name             string           `json:"name"`
	Namespace        string           `json:"namespace"`
	DatasetName      string           `json:"datasetName"`
	DatasetNamespace string           `json:"datasetNamespace"`
	Status           string           `json:"status"`
	Target           []DataloadTarget `json:"target"`
	Duration         string           `json:"duration"`
	CreateTime       time.Time        `json:"createTime"`
	FinishTime       time.Time        `json:"finishTime"`
}

type ListDataloadResponse struct {
	CommonResponse
	PageNo     int            `json:"pageNo,omitempty"`
	PageSize   int            `json:"pageSize,omitempty"`
	TotalCount int            `json:"totalCount"`
	Dataloads  []DataloadItem `json:"dataloads"`
}

type CreateRuntimeRequest struct {
	Runtime string `json:"runtime"`
}

type CreateTaskResp struct {
	CommonResponse
	TaskID string `json:"taskID"`
}

type RemoveInstancesFromInstanceGroup struct {
	InstancesToBeRemoved []string               `json:"instancesToBeRemoved"`
	CleanPolicy          CleanPolicy            `json:"cleanPolicy"`
	DeleteOption         *ccetypes.DeleteOption `json:"deleteOption,omitempty"`
}

type GetTaskResp struct {
	CommonResponse
	Task *Task `json:"task"`
}

type ListTaskOption struct {
	targetID string
	pageNo   int
	pageSize int
}

type ListTaskResp struct {
	CommonResponse
	Page ListTaskPage
}

type ListTaskPage struct {
	PageNo     int     `json:"pageNo,omitempty"`
	PageSize   int     `json:"pageSize,omitempty"`
	TotalCount int     `json:"totalCount"`
	Items      []*Task `json:"items"`
}

type Task struct {
	ID          string     `json:"id"`
	Type        string     `json:"type"`
	Description string     `json:"description"`
	StartTime   time.Time  `json:"startTime"`
	FinishTime  *time.Time `json:"finishTime,omitempty"`
	Phase       string     `json:"phase"`

	TaskProcesses []TaskProcess `json:"processes,omitempty"`
	ErrMessage    string        `json:"errMessage,omitempty"`
}

type TaskProcess struct {
	Name       string     `json:"name"`
	Phase      string     `json:"phase,omitempty"`
	StartTime  *time.Time `json:"startTime,omitempty"`
	FinishTime *time.Time `json:"finishTime,omitempty"`

	Metrics      map[string]string `json:"metrics,omitempty"`
	SubProcesses []TaskProcess     `json:"subProcesses,omitempty"`
	ErrMessage   string            `json:"errMessage,omitempty"`
}

type GetNginxIngressClassListResponse struct {
	IngressClassList []string `json:"ingressClassList"`
	RequestID        string   `json:"requestID"`
}

// CreateManagedMasterRequest - 创建 托管 master 参数
type CreateManagedMasterRequest struct {
	ClusterID     string `json:"clusterID"`
	AvailableZone string `json:"availableZone"`
	CPU           int    `json:"cpu"`
	Memory        int    `json:"memory"`
	DiskSize      int    `json:"diskSize"`
	Ephemeal      int    `json:"ephemeral"`
	SnapshotID    string `json:"snapshotID"`
	Force         bool   `json:"force"`
}

// SLA 混部水位线
type SLA struct {
	CPU               CPUSLA `json:"cpu"`
	Mem               MemSLA `json:"memory"`
	Net               NetSLA `json:"net"`
	CoolDownSec       int    `json:"coolDownSec"`
	ExpulsionDelaySec int    `json:"expulsionDelaySec"`
	MaxInstances      int    `json:"maxInstances"`
}

type CPUSLA struct {
	HighPercent        int   `json:"highPercent"`
	BestEffortMaxCores int64 `json:"bestEffortMaxCores"`
}

type NetSLA struct {
	InHigh            int64 `json:"inHigh"`
	BestEffortInHigh  int64 `json:"bestEffortInHigh"`
	OutHigh           int64 `json:"outHigh"`
	BestEffortOutHigh int64 `json:"bestEffortOutHigh"`
}

type MemSLA struct {
	HighPercent   int   `json:"highPercent"`
	BestEffortMax int64 `json:"bestEffortMax"`
}

// 混部配置请求体
type BaseSLA struct {
	Enabled bool `json:"slaEnable"`
	SLA     SLA  `json:"sla"`
}

type NodeSLA struct {
	BaseSLA
	NodeName string `json:"nodeName"`
}

type PoolSLA struct {
	BaseSLA
	InstanceGroupID string `json:"instanceGroupID"`
}

type BatchNodeSLA struct {
	BaseSLA
	Nodes []string `json:"nodes"`
}

type BatchResults struct {
	SuccessList []string `json:"successList"`
	FailedList  []string `json:"failedList"`
}

type SLAItems []BaseSLA

type NodeSLAList []NodeSLA

type PoolSLAList []PoolSLA

type SLAList struct {
	NodeSLAItems NodeSLAList `json:"nodeSLAItems"`
	PoolSLAItems PoolSLAList `json:"poolSLAItems"`
}

// CreateWorkflowRequest - 创建集群变更流程参数
type CreateWorkflowRequest struct {
	WorkflowType   ccetypes.WorkflowType   `json:"workflowType"`
	WorkflowConfig ccetypes.WorkflowConfig `json:"workflowConfig"`
	WatchDogConfig ccetypes.WatchDogConfig `json:"watchDogConfig"`
}

// CreateWorkflowResponse - 创建集群变更返回
type CreateWorkflowResponse struct {
	WorkflowID string `json:"workflowID"`
	RequestID  string `json:"requestID"`
}

// ListWorkflowsResponse - 集群内 Workflow List
type ListWorkflowsResponse struct {
	ClusterID string      `json:"clusterID"`
	Workflows []*Workflow `json:"workflows"`
}

// GetWorkflowResponse - 查询 Workflow 返回
type GetWorkflowResponse struct {
	Workflow *Workflow `json:"workflow"`
}

// Workflow - Workflow 对象
// TODO: 是否有不适合展示的字段, 需要重新定义
type Workflow struct {
	Spec   *ccetypes.WorkflowSpec   `json:"spec"`
	Status *ccetypes.WorkflowStatus `json:"status"`
}

// ListNodesCanBeUpgradedByPageResponse - 升级集群, 用于前端选取 Nodes List
type ListNodesCanBeUpgradedByPageResponse struct {
	ClusterID    string      `json:"clusterID"`
	PageNo       int         `json:"pageNo"`
	PageSize     int         `json:"pageSize"`
	TotalCount   int         `json:"totalCount"`
	InstanceList []*Instance `json:"instanceList"`

	RequestID string `json:"requestID"`
}

// TargetK8SVersionResponse - 允许被升级到的 K8S 版本
type TargetK8SVersionResponse struct {
	K8SVersions []ccetypes.K8SVersion `json:"k8sVersions"`
	RequestID   string                `json:"requestID"`
}

// UpgradeNodeFields - 升级集群, 用于前端选取 Nodes List 特殊字段
type UpgradeNodeFields struct {
	K8SVersion    ccetypes.K8SVersion `json:"k8sVersion"`
	CanBeSelected bool                `json:"canBeSelected"`
	Reason        string              `json:"reason,omitempty"`
}

// DeleteWorkflowResponse - 删除 Workflow 返回
type DeleteWorkflowResponse struct {
	ClusterID  string `json:"clusterID"`
	WorkflowID string `json:"workflowID"`
	RequestID  string `json:"requestID"`
}

// UpdateWorkflowRequest - 更新 Workflow 配置
type UpdateWorkflowRequest struct {
	Action UpdateWorkflowAction `json:"action"`

	Spec *ccetypes.WorkflowSpec `json:"spec,omitempty"` // Action = UpdateSpec 时传入
}

// UpdateWorkflowAction - 更新 Workflow 操作类型
type UpdateWorkflowAction string

const (
	// UpdateWorkflowActionPause - 暂停
	UpdateWorkflowActionPause UpdateWorkflowAction = "pause"

	// UpdateWorkflowActionResume - 恢复
	UpdateWorkflowActionResume UpdateWorkflowAction = "resume"

	// UpdateWorkflowActionUpdateSpec - 修改 Spec
	UpdateWorkflowActionUpdateSpec UpdateWorkflowAction = "updateSpec"
)

// UpdateWorkflowResponse - 更新 Workflow 返回
type UpdateWorkflowResponse struct {
	ClusterID  string `json:"clusterID"`
	WorkflowID string `json:"workflowID"`
	RequestID  string `json:"requestID"`
}

// ListBindingCPromInstanceResquest 请求参数
type ListBindingCPromInstanceResquest struct {
	ClusterID string `json:"clusterID"`
}

// ListBindingCPromInstanceResponse 响应参数
type ListBindingCPromInstanceResponse struct {
	RequestID string                 `json:"requestID"`
	Items     []BindingCPromInsntace `json:"items"`
}

// BindingCPromInsntace 信息
type BindingCPromInsntace struct {
	Instance cpromv1.MonitorInstance `json:"instance"`
	Agent    cpromv1.MonitorAgent    `json:"agent"`
}
