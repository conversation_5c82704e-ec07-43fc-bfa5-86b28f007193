// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2022/01/24 11:05:00, by <EMAIL>, create
*/
/*
实现 CCE V2 SDK Workflow 相关方法
*/

package ccev2

import (
	"context"
	utils2 "icode.baidu.com/baidu/cprom/cloud-stack/cprom-common/utils"
	"testing"
	"time"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/iam"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/sts"
	ccetypes "icode.baidu.com/baidu/cprom/cloud-stack/pkg/types"
)

var stsEndpoints = map[string]string{
	"bj":      "sts.bj.iam.sdns.baidu.com:8586/v1",
	"gz":      "sts.gz.iam.sdns.baidu.com:8586/v1",
	"su":      "sts.su.iam.sdns.baidu.com:8586/v1",
	"hkg":     "sts.hkg.bce.baidu-int.com:8586/v1",
	"fwh":     "10.70.16.62:8586/v1",
	"bd":      "10.70.0.93:8586/v1",
	"sandbox": "sts.bj.internal-qasandbox.baidu-int.com:8586/v1",
}

func testCreateResizeMasterWorkflow(t *testing.T) {
	// 填写、修改以下信息
	clusterID := "" // 用户集群ID，例：cce-06w1u9nk
	cpuCoreNum := 0 // 预期的 cpu 核数
	memInGB := 0    // 预期的 memory（GB）
	accountID := "" // 账号ID
	region := ""    // bj,bd,fwh,su,gz,hkg

	cceak := "c0d3690042364b47a8e5b91e3d834b83"
	ccesk := "fe2e4b7c70234a2c926a159112996820"

	ctx := context.TODO()
	// 初始化 sts client
	stsclient := sts.NewClient(ctx, &bce.Config{
		Endpoint: stsEndpoints[region],
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   region,
	}, &bce.Config{
		Endpoint: iam.Endpoints[region],
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   region,
	}, "BceServiceRole_SERVICE_CCE", "cce", "c8QBqhOKxmGu0Sl2p13guMil3U2grbYt")

	// stsclient.SetDebug(true)

	client := NewClient(&bce.Config{
		Checksum: true,
		Timeout:  30 * time.Second,
		Endpoint: Endpoints[region],
	})

	client.SetDebug(true)

	// 升级全集群
	args := &CreateWorkflowRequest{
		WorkflowType: ccetypes.WorkflowTypeResizeMasterMachine,
		WorkflowConfig: ccetypes.WorkflowConfig{
			ResizeMasterMachineConfig: &ccetypes.ResizeMasterMachineConfig{
				CPUCoreNum: cpuCoreNum,
				MemoryInGB: memInGB,
			},
		},
	}

	resp, err := client.CreateWorkflow(context.TODO(), clusterID, args, stsclient.NewSignOptionWithCCEAKSk(ctx, cceak, ccesk, accountID))
	if err != nil {
		t.Errorf("CreateWorkflow failed: %v", err)
		return
	}

	t.Logf("CreateWorkflow success: %s", utils2.ToJSON(resp))
}

func testClient_CreateWorkflow(t *testing.T) {
	client.SetDebug(true)
	clusterID := "cce-06w1u9nk"

	// 升级全集群
	// args := &CreateWorkflowRequest{
	// 	WorkflowType: ccetypes.WorkflowTypeUpgradeClusterK8SVersion,
	// 	WorkflowConfig: ccetypes.WorkflowConfig{
	// 		UpgradeClusterK8SVersionWorkflowConfig: &ccetypes.UpgradeClusterK8SVersionWorkflowConfig{
	// 			TargetK8SVersion: ccetypes.K8S_1_18_9,
	// 		},
	// 	},
	// }

	// 升级容器化 Master
	args := &CreateWorkflowRequest{
		WorkflowType: ccetypes.WorkflowTypeUpgradeMasterK8SVersion,
		WorkflowConfig: ccetypes.WorkflowConfig{
			UpgradeMasterK8SVersionWorkflowConfig: &ccetypes.UpgradeMasterK8SVersionWorkflowConfig{
				TargetK8SVersion: ccetypes.K8S_1_18_9,
			},
		},
		WatchDogConfig: ccetypes.WatchDogConfig{
			UnhealthyPodsPercent: 20,
		},
	}

	// 升级 Nodes
	// args := &CreateWorkflowRequest{
	// 	WorkflowType: ccetypes.WorkflowTypeUpgradeNodesK8SVersion,
	// 	WorkflowConfig: ccetypes.WorkflowConfig{
	// 		UpgradeNodesK8SVersionWorkflowConfig: &ccetypes.UpgradeNodesK8SVersionWorkflowConfig{
	// 			TargetK8SVersion: ccetypes.K8S_1_18_9,
	// 			UpgradeAllNodes:  true,
	// 		},
	// 	},
	// 	WatchDogConfig: ccetypes.WatchDogConfig{
	// 		UnhealthyPodsPercent: 30,
	// 	},
	// }

	resp, err := client.CreateWorkflow(context.TODO(), clusterID, args, nil)
	if err != nil {
		t.Errorf("CreateWorkflow failed: %v", err)
		return
	}

	t.Errorf("CreateWorkflow success: %s", utils2.ToJSON(resp))
}

func testClient_ListWorkflows(t *testing.T) {
	clusterID := "cce-3mujfi7x"

	resp, err := client.ListWorkflows(context.TODO(), clusterID, nil)
	if err != nil {
		t.Errorf("ListWorkflows failed: %v", err)
		return
	}

	t.Errorf("ListWorkflows success: %s", utils2.ToJSON(resp))
}

func testClient_GetWorkflow(t *testing.T) {
	clusterID := "cce-g4t10qqf"

	resp, err := client.GetWorkflow(context.TODO(), clusterID, "workflow-cce-g4t10qqf-urzo5swy", nil)
	if err != nil {
		t.Errorf("GetWorkflow failed: %v", err)
		return
	}

	t.Errorf("GetWorkflow success: %s", utils2.ToJSON(resp))
}

func testClient_DeleteWorkflow(t *testing.T) {
	clusterID := "cce-g4t10qqf"

	resp, err := client.DeleteWorkflow(context.TODO(), clusterID, "workflow-cce-g4t10qqf-urzo5swy", nil)
	if err != nil {
		t.Errorf("DeleteWorkflow failed: %v", err)
		return
	}

	t.Errorf("DeleteWorkflow success: %s", utils2.ToJSON(resp))
}

func testClient_UpdateWorkflow(t *testing.T) {
	clusterID := "cce-g4t10qqf"

	client.SetDebug(true)
	resp, err := client.UpdateWorkflow(context.TODO(), clusterID, "workflow-cce-g4t10qqf-urzo5swy", &UpdateWorkflowRequest{
		Action: UpdateWorkflowActionResume,
		// Action: UpdateWorkflowActionPause,
	}, nil)
	if err != nil {
		t.Errorf("UpdateWorkflow failed: %v", err)
		return
	}

	t.Errorf("UpdateWorkflow success: %s", utils2.ToJSON(resp))
}

func testClient_TargetK8SVersion(t *testing.T) {
	clusterID := "cce-g4t10qqf"

	resp, err := client.TargetK8SVersion(context.TODO(), clusterID, ccetypes.ClusterRoleNode, nil)
	if err != nil {
		t.Errorf("TargetK8SVersion failed: %v", err)
		return
	}

	t.Errorf("TargetK8SVersion success: %s", utils2.ToJSON(resp))
}
