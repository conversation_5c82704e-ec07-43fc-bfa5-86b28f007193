package logicbcc

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
)

func init() {
	region := "gz"
	accessKeyID := "e0c68be8495540f280b3e9ef03ec25d2"
	secretAccessKey := "b599d5f4f30e41bcb0e1482c9de65bd6"

	bccclient = NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      region,
		Endpoint:    Endpoints[region],
	})
	bccclient.SetDebug(true)
}

func testGetQuota(t *testing.T) {
	resp, err := bccclient.GetQuota(context.TODO(), QuotaTypeBCC, false, NewSignOption())
	if err != nil {
		t.<PERSON>rrorf("GetQuo<PERSON> failed: %v", err)
	}
	if respBytes, err := json.Marshal(resp); err == nil {
		t.Logf("GetQuota succeeded: %s", string(respBytes))
	}
}
