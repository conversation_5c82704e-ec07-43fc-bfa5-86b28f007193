package logicbcc

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/iam"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/logicimage"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/sts"
)

var bbcclient *Client
var stsclient *sts.Client

func init() {
	//accessKeyID     := "5fded23b03594981872fbfadaad70ef6"
	//secretAccessKey := "6b109261727a4fee8fc5db4bf5ec6c51"

	accessKeyID := "e0c68be8495540f280b3e9ef03ec25d2"
	secretAccessKey := "b599d5f4f30e41bcb0e1482c9de65bd6"
	serviceRoleName := "BceServiceRole_SERVICE_CCE"
	serviceName := "cce"
	servicePassword := "c8QBqhOKxmGu0Sl2p13guMil3U2grbYt"

	bbcclient = NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Endpoint:    Endpoints["su"],
	})
	bbcclient.SetDebug(true)

	// 初始化 sts client
	stsclient = sts.NewClient(context.TODO(), &bce.Config{
		Endpoint: sts.Endpoints["su"],
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   "gz",
	}, &bce.Config{
		Endpoint: iam.Endpoints["su"],
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   "gz",
	}, serviceRoleName, serviceName, servicePassword)
}

func testGetBBC(t *testing.T) {
	response, err := bbcclient.GetBBC(context.TODO(), "i-zazP6rbb", NewSignOption())
	if err != nil {
		t.Errorf("GetBBC failed: %v", err)
	}
	if responseByte, err := json.Marshal(response); err == nil {
		t.Logf("GetBBC succeeded: %s", string(responseByte))
	}
}

func testReinstallBBCs(t *testing.T) {
	req := &ReinstallBBCArgs{
		ServerIDs: []string{"i-IcgqzDPT"},
		ImageID:   "m-ix2tRLmn",
		Password:  "test123!T",

		KeepData:    true,
		RaidID:      "",
		SysRootSize: 0,
	}
	err := bbcclient.ReinstallBBCs(context.TODO(), req, NewSignOption())
	if err != nil {
		t.Errorf("ReinstallBBCs failed: %v", err)
	}
	t.Logf("ReinstallBBCs succeeded!")
}

func testCreateBBC(t *testing.T) {
	//createServerArgs := &CreateServersArgs{
	//	Items: []Item{
	//		{
	//			Config: &BBCConfig{
	//				ServiceType: ServiceTypeBBC,
	//				PurchaseNum: 1,
	//				Tags: []Tag{
	//					{
	//						TagKey:   "source",
	//						TagValue: "CCE",
	//					},
	//				},
	//				Flavor:        BBC_I2_01,
	//				//Raid:          Raid5,
	//				DiskInfo:      "Raid5",
	//				ImageType:     logicimage.ImageTypeBBCSystem,
	//				ImageID:       "ea398cca-14f6-4d7c-aaa0-75b00428eb77",
	//				AdminPass:     "test123!T",
	//				SubnetID:      "f7398e10-247a-42bd-93dc-dbf6d84f70ef",
	//				AvailableZone: "zoneB",
	//				ProductType:   ProductTypePostpay,
	//				SYSDiskSize:   100,
	//			},
	//		},
	//	},
	//}

	//createServerArgs := &CreateServersArgs{
	//	Items: []Item{
	//		{
	//			Config: &BBCConfig{
	//				ServiceType: ServiceTypeBBC,
	//				PurchaseNum: 1,
	//				Tags: []Tag{
	//					{
	//						TagKey:   "source",
	//						TagValue: "CCE",
	//					},
	//				},
	//				Flavor:        BBC_I2_01,
	//				//Raid:          Raid5,
	//				DiskInfo:      "Raid5",
	//				ImageType:     logicimage.ImageTypeBBCSystem,
	//				ImageID:       "ea398cca-14f6-4d7c-aaa0-75b00428eb77",
	//				AdminPass:     "test123!T",
	//				SubnetID:      "sbn-ec1kce47h8w2",
	//				AvailableZone: "zoneC",
	//				ProductType:   ProductTypePostpay,
	//				SYSDiskSize:   100,
	//			},
	//		},
	//	},
	//}

	// 智能卡bbc, 用普通子网, 需要安全组ID， 不需要 raid 的bbc diskInfo = "NoRaid"
	createServerArgs := &CreateServersArgs{
		Items: []Item{
			{
				Config: &BBCConfig{
					ServiceType: ServiceTypeBBC,
					PurchaseNum: 1,
					Tags: []Tag{
						{
							TagKey:   "source",
							TagValue: "CCE",
						},
					},
					Flavor: BBC_G4_05S,
					// Raid:          "NoRaid",
					DiskInfo:  "NoRaid",
					ImageType: logicimage.ImageTypeBBCSystem,
					// ImageID:         "m-auCzCSWx",
					AdminPass:       "test123!T",
					SecurityGroupID: "4721c66c-388b-4675-83f9-67716e58a1f0",
					SubnetID:        "sbn-95tvvtfzcbqq",
					AvailableZone:   "zoneA",
					ProductType:     ProductTypePostpay,
					SYSDiskSize:     100,
				},
			},
		},
	}
	//resp, err := bbcclient.CreateBBC(context.TODO(), createServerArgs, stsclient.NewSignOption(context.TODO(), "eca97e148cb74e9683d7b7240829d1ff"))
	resp, err := bbcclient.CreateBBC(context.TODO(), createServerArgs, NewSignOption())
	if err != nil {
		t.Errorf("create bbc failed: %v", err)
	}
	if responseByte, err := json.Marshal(resp); err == nil {
		t.Logf("create bbc succeeded: %s", string(responseByte))
	}
}
