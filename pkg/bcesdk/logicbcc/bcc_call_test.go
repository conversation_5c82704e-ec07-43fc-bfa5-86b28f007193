package logicbcc

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/internalvpc"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/logicimage"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/sts"
)

var bccclient *Client

func init() {
	region := "gz"
	accessKeyID := "e0c68be8495540f280b3e9ef03ec25d2"
	secretAccessKey := "b599d5f4f30e41bcb0e1482c9de65bd6"

	bccclient = NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      region,
		Endpoint:    Endpoints[region],
	})
	bccclient.SetDebug(true)
}

func testCreateServers(t *testing.T) {
	instanceConfig := InstanceConfig{
		ServiceType:  "BCC",
		InstanceType: "10",
		Application:  "bcc",

		Name:      "test-bidding-instance",
		AdminPass: "test123!T",
		KeyPairID: "",

		CPU:                 1,
		Memory:              4,
		LocalDiskSize:       0,
		RootDiskSizeInGb:    50,
		RootDiskStorageType: "ssd",
		GPUType:             "",
		GPUCount:            0,

		ImageType: logicimage.ImageTypeSystem,
		ImageID:   "be7efb85-df38-4c10-852a-2f5c83a02fad",

		AvailableZone:   internalvpc.ZoneA,
		SubnetID:        "sbn-f0ewipic2zh4",
		SecurityGroupID: "g-et8vbmrspe2a",
		EnableKata:      false,

		Tags: []Tag{
			{
				TagKey:   "source",
				TagValue: "CCE",
			},
		},
		RelationTag: false,

		ProductType:       "bidding",
		PurchaseNum:       1,
		PurchaseLength:    0,
		AutoRenewTimeUnit: "",
		AutoRenewTime:     0,

		// 竞价实例参数
		BidMode:       BidModeCustomPrice,
		BidPrice:      "0.0000032",
		BidReleaseCDS: true,
		BidReleaseEIP: true,
	}

	// volumeConfig := VolumeConfig{
	// 	ServiceType: "CDS",
	// 	LogicalZone: "zoneA",

	// 	CDSList: []CDSConfig{
	// 		{
	// 			SizeInGB:    5,
	// 			Size:        "5", // 必须有
	// 			VolumeType:  "sata",
	// 			LogicalZone: "zoneA",
	// 			SnapshotID:  "",
	// 		},
	// 	},

	// 	ProductType:       "postpay",
	// 	PurchaseNum:       1,
	// 	PurchaseLength:    1,
	// 	AutoRenewTimeUnit: "month",
	// 	AutoRenewTime:     0,
	// }

	// eipConfig := EIPConfig{
	// 	ServiceType:     "EIP",
	// 	Name:            "yuhongwei-test-eip-config",
	// 	BandwidthInMbps: 100,
	// 	BillingMethod:   "netraffic",

	// 	ProductType:       "postpay",
	// 	PurchaseNum:       1,
	// 	PurchaseLength:    1,
	// 	AutoRenewTimeUnit: "month",
	// 	AutoRenewTime:     0,
	// }

	request := CreateServersArgs{
		Items: []Item{
			{
				Config: instanceConfig,
			},
			// {
			// 	Config: volumeConfig,
			// },
			// {
			// 	Config: eipConfig,
			// },
		},
	}

	if requestBytes, err := json.Marshal(request); err == nil {
		t.Logf("BCC config: %s", string(requestBytes))
	}

	response, err := bccclient.CreateServers(context.TODO(), &request, "cce-pbp13g1z-y5gg11nc-cce-pbp13g1z-y5gg11nc", NewSignOption())
	if err != nil {
		t.Errorf("CreateServers failed: %v", err)
	}

	if responseByte, err := json.Marshal(response); err == nil {
		t.Logf("CreateServers succeeded: %s", string(responseByte))
	}
}

func testListServersByIDs(t *testing.T) {
	serverUUIDs := []string{"i-GdqPoKff"}
	response, err := bccclient.ListServersByIDs(context.TODO(), serverUUIDs, NewSignOption())
	if err != nil {
		t.Errorf("ListServersByIDs failed: %v", err)
	}
	if responseByte, err := json.Marshal(response); err == nil {
		t.Logf("ListServersByIDs succeeded: %s", string(responseByte))
	}
}

func testGetServerByID(t *testing.T) {
	response, err := bccclient.GetServerByID(context.Background(), "i-uo3WftuZ", NewSignOption())
	if err != nil {
		t.Errorf("GetServerByID failed: %v", err)
	}
	if responseByte, err := json.Marshal(response); err == nil {
		t.Logf("GetServerByID succeeded: %s", string(responseByte))
	}
}

func testGetServers(t *testing.T) {
	getServersArgs := &GetServersArgs{
		KeywordType: "name",
		Keyword:     "test-bidding-instance",
	}

	response, err := bccclient.GetServers(context.TODO(), getServersArgs, NewSignOption())
	if err != nil {
		t.Errorf("GetServers failed: %v", err)
	}

	if responseByte, err := json.Marshal(response); err == nil {
		t.Logf("GetServers succeeded: %s", string(responseByte))
	}
}

func testDeleteServers(t *testing.T) {
	serverDeleteRequest := &DeleteServersArgs{
		ServerIDs: []string{
			"i-uD4Uv6Ce",
		},
		DeleteResource:    true,
		DeleteCDSSnapshot: true,
	}

	if err := bccclient.DeleteServers(context.TODO(), serverDeleteRequest, NewSignOption()); err == nil {
		t.Logf("DeleteServers succeeded")
	} else {
		t.Errorf("DeleteServers failed: %s", err)
	}
}

func testBatchUpdateApplication(t *testing.T) {
	updateAppReq := &BatchUpdateApplicationArgs{
		InstanceIDs: []string{
			"i-ZkcnqrDK",
		},
		Application: ApplicationBCC,
	}
	err := bccclient.BatchUpdateApplication(context.TODO(), updateAppReq, NewSignOption())
	if err == nil {
		t.Logf("BatchUpdateApplication succeeded")
	} else {
		t.Errorf("BatchUpdateApplication failed: %s", err)
	}
}

func testBatchUpdateApplicationWithSTS(t *testing.T) {
	ctx := context.TODO()

	stsclient := sts.NewClient(ctx, &bce.Config{
		Endpoint: "sts.bj.iam.sdns.baidu.com:8586/v1",
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   "su",
	}, &bce.Config{
		Endpoint: "iam.bj.bce-internal.baidu.com/v3",
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   "su",
	}, "BceServiceRole_SERVICE_CCE", "cce", "c8QBqhOKxmGu0Sl2p13guMil3U2grbYt")

	client := NewClient(&bce.Config{
		Checksum: true,
		Timeout:  30 * time.Second,
		Endpoint: "bcclogic.bce-internal.baidu.com",
	})

	client.SetDebug(true)

	updateAppReq := &BatchUpdateApplicationArgs{
		InstanceIDs: []string{
			"i-TBCFvstG",
			"i-4Tjrx3aY",
			"i-GraSUdN0",
			"i-J87kh7hu",
		},
		Application: ApplicationBCC,
	}

	err := client.BatchUpdateApplication(context.TODO(), updateAppReq, stsclient.NewSignOption(ctx, "24a294c15a9a43c5962e0682e49bd0e2"))
	if err == nil {
		t.Logf("BatchUpdateApplication succeeded")
	} else {
		t.Errorf("BatchUpdateApplication failed: %s", err)
	}
}
