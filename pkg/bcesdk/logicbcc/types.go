// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2018/12/25 19:30:00, by <EMAIL>, create
*/
/*
DESCRIPTION
包定义了 BCC Logic 方法相关接口
*/

package logicbcc

import (
	"context"
	"fmt"
	"time"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/internalvpc"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/logicimage"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/vpc"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/logicbcc Interface

// Interface 定义请求 BCC Logic 相关方法
type Interface interface {
	SetDebug(bool)

	CreateServers(ctx context.Context, args *CreateServersArgs, clientToken string, option *bce.SignOption) (*CreateServersResponse, error)
	GetServers(ctx context.Context, args *GetServersArgs, option *bce.SignOption) (*GetServersResponse, error)
	GetServerByID(ctx context.Context, instanceID string, option *bce.SignOption) (*Server, error)
	ListServersByIDs(ctx context.Context, instanceIDs []string, option *bce.SignOption) (*ListServersResponse, error) // 支持长短 ID
	DeleteServers(ctx context.Context, args *DeleteServersArgs, option *bce.SignOption) error
	RenameServers(ctx context.Context, args *RenameServersArgs, option *bce.SignOption) error
	ReinstallServers(ctx context.Context, args *ReinstallServersArgs, option *bce.SignOption) error
	BatchUpdateApplication(ctx context.Context, args *BatchUpdateApplicationArgs, option *bce.SignOption) error
	GetFlavors(ctx context.Context, args *GetFlavorsArgs, option *bce.SignOption) (*GetFlavorsResponse, error)

	// BBC
	CreateBBC(ctx context.Context, args *CreateServersArgs, option *bce.SignOption) (*CreateServersResponse, error)
	GetBBC(ctx context.Context, serverID string, option *bce.SignOption) (*Server, error)
	ReinstallBBCs(ctx context.Context, args *ReinstallBBCArgs, option *bce.SignOption) error
	DeleteBBCs(ctx context.Context, serverIDs []string, option *bce.SignOption) error

	// CDS
	ListCDS(ctx context.Context, instanceID, diskType string, option *bce.SignOption) (*ListCDSResponse, error)

	// 查询 BCC (带 CDS/EIP) 价格
	QueryBCCPrice(ctx context.Context, request *QueryBCCPriceRequest, option *bce.SignOption) (*QueryBCCPriceResponse, error)

	// quota
	GetQuota(ctx context.Context, quotaType QuotaType, needGlobalQuota bool, option *bce.SignOption) (*GetQuotaResponse, error)

	// tag
	AssignTags(ctx context.Context, assignReq *AssignTagsRequest, option *bce.SignOption) error

	// 竞价实例
	GetBidEvents(ctx context.Context, req *GetBidEventsRequest, option *bce.SignOption) (*GetBidEventsResponse, error)
}

const SpecIDGN3 = "gn3"

// CreateServersArgs 创建虚机参数
type CreateServersArgs struct {
	Items []Item `json:"items"`
}

// Item 创建虚机配置
type Item struct {
	Config Config `json:"config"`
}

// Config 定义 config() Interface
type Config interface {
	config()
}

func (instanceConfig InstanceConfig) config() {}
func (volumeConfig VolumeConfig) config()     {}
func (eipConfig EIPConfig) config()           {}
func (bbcConfig BBCConfig) config()           {}

// CreateServersResponse 创建虚机返回
type CreateServersResponse struct {
	OrderID     string   `json:"orderId"`
	InstanceIDs []string `json:"instanceIds"`
}

// ReinstallServersArgs 重装 BCC 参数
type ReinstallServersArgs struct {
	ServerIDs []string `json:"serverIds"`
	ImageUUID string   `json:"imageId"`
	Password  string   `json:"password"`
}

// BatchUpdateApplicationArgs 批量更新 applications
type BatchUpdateApplicationArgs struct {
	Application Application `json:"application"`
	InstanceIDs []string    `json:"instanceIds"`
}

// DeleteServersArgs 删除 Servers 参数
type DeleteServersArgs struct {
	ServerIDs         []string `json:"serverIds"`
	DeleteResource    bool     `json:"relatedReleaseFlag"`
	DeleteCDSSnapshot bool     `json:"deleteCdsSnapshotFlag"`
}

// ListServersResponse 返回
type ListServersResponse struct {
	OrderBy    string           `json:"orderBy"`
	PageNo     int              `json:"pageNo"`
	PageSize   int              `json:"pageSize"`
	TotalCount int              `json:"totalCount"`
	Result     []ServerAbstract `json:"result"`
}

// ServerAbstract server 概要
type ServerAbstract struct {
	Name            string       `json:"name"`
	InstanceID      string       `json:"instanceId"`
	InstanceUUID    string       `json:"instanceUuid"`
	OrderUUID       string       `json:"orderUuid"`
	UserID          string       `json:"userId"`
	Description     string       `json:"description"`
	CPU             int          `json:"cpuNum"`
	MEM             int          `json:"memoryGb"`
	ImageUUID       string       `json:"imageUuid"`
	ProductType     ProductType  `json:"productType"`
	InternalIP      string       `json:"internalIp"`
	FloatingIP      string       `json:"floatingIp"`
	EIP             string       `json:"eip"`
	BandwidthInMbps int          `json:"eipSize"`
	IPV6            string       `json:"ipv6"`
	SubnetUUID      string       `json:"subnetUuid"`
	ZoneID          string       `json:"zoneId"`
	Application     Application  `json:"application"`
	HasScheduleTask bool         `json:"hasScheduleTask"` // 是多可用区的开关？
	DCCUUID         string       `json:"dccUuid"`
	Status          ServerStatus `json:"status"`
	CreatedTime     time.Time    `json:"createdTime"`
	UpdatedTime     time.Time    `json:"updatedTime"`
	ExpireTime      time.Time    `json:"expireTime"`
}

// ServerStatus BCC 虚机状态
type ServerStatus string

const (
	// ServerStatusActive 虚机运行中
	ServerStatusActive ServerStatus = "ACTIVE"

	// ServerStatusBuild 虚机创建中
	ServerStatusBuild ServerStatus = "BUILD"

	// ServerStatusRebuild 虚机重装系统中
	ServerStatusRebuild ServerStatus = "REBUILD"

	// ServerStatusDeleted 虚机已删除
	ServerStatusDeleted ServerStatus = "DELETED"

	// ServerStatusSnapshot 创建快照
	ServerStatusSnapshot ServerStatus = "SNAPSHOT"

	// ServerStatusDeleteSnapshot 删除快照
	ServerStatusDeleteSnapshot ServerStatus = "DELETE_SNAPSHOT"

	// ServerStatusVolumeResize VOLUME_RESIZE
	ServerStatusVolumeResize ServerStatus = "VOLUME_RESIZE"

	// ServerStatusError 虚机异常
	ServerStatusError ServerStatus = "ERROR"

	// ServerStatusExpired 虚机欠费释放
	ServerStatusExpired ServerStatus = "EXPIRED"

	// ServerStatusReboot 虚机重启
	ServerStatusReboot ServerStatus = "REBOOT"

	// ServerStatusRecharge 虚机续费
	ServerStatusRecharge ServerStatus = "RECHARGE"

	// ServerStatusShutoff 虚机关机
	ServerStatusShutoff ServerStatus = "SHUTOFF"

	// ServerStatusStopped 虚机关机
	ServerStatusStopped ServerStatus = "STOPPED"

	// ServerStatusUnknown 虚机状态未知
	ServerStatusUnknown ServerStatus = "UNKNOWN"
)

// ProductType 产品类型
type ProductType string

const (
	// ProductTypePostpay 后付费
	ProductTypePostpay ProductType = "postpay"

	// ProductTypePrepay 预付费
	ProductTypePrepay ProductType = "prepay"

	// ProductTypeBidding 竞价实例
	ProductTypeBidding ProductType = "bidding"
)

// Application 产品线标识
type Application string

const (
	// ApplicationCCE 产品线标识为 CCE
	ApplicationCCE Application = "cce"

	// ApplicationBCC 产品线标识为 BCC
	ApplicationBCC Application = "bcc"
)

// Tag key-value 用户标识
type Tag struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

// InstanceConfig 定义创建 Instance 的参数
type InstanceConfig struct {
	ServiceType ServiceType `json:"serviceType"`

	InstanceType InstanceType `json:"instanceType"`
	Name         string       `json:"name"`
	AdminPass    string       `json:"adminPass"`
	KeyPairID    string       `json:"keypairId"`
	Application  Application  `json:"application"`

	CPU                 int         `json:"cpu"`
	Memory              int         `json:"memory"`
	LocalDiskSize       int         `json:"diskSize"`
	RootDiskSizeInGb    int         `json:"rootDiskSizeInGb"`
	RootDiskStorageType StorageType `json:"rootDiskStorageType,omitempty"`
	GPUType             GPUType     `json:"gpuCard,omitempty"`
	GPUCount            int         `json:"gpuCount,omitempty"`
	SpecID              string      `json:"specId,omitempty"`

	// EphemeralDiskConfig - 本地盘配置
	EphemeralDisks []EphemeralDisk `json:"createEphemeralList,omitempty"`

	ImageID   string               `json:"imageId"`
	ImageType logicimage.ImageType `json:"imageType"`

	AvailableZone   internalvpc.AvailableZone `json:"logicalZone"`
	SubnetID        string                    `json:"subnetUuid"`
	DeploySetID     string                    `json:"deployId"`
	SecurityGroupID string                    `json:"securityGroupId"`
	EnableKata      bool                      `json:"enableKata"`

	Tags        []Tag `json:"tags"`
	RelationTag bool  `json:"relationTag"`

	ProductType       ProductType `json:"productType"`
	PurchaseNum       int         `json:"purchaseNum"`
	PurchaseLength    int         `json:"purchaseLength"`
	AutoRenewTimeUnit string      `json:"autoRenewTimeUnit"`
	AutoRenewTime     int         `json:"autoRenewTime"`

	// 竞价实例选项
	BidMode       BidMode `json:"bidModel"`
	BidPrice      string  `json:"bidPrice"`
	BidReleaseEIP bool    `json:"bidReleaseEipFlag"`
	BidReleaseCDS bool    `json:"bidReleaseCDSFlag"`
}

// EphemeralDisk - 本地盘
// createEphemeralList: [
//
//	{
//	  "sizeInGB":3720,
//	  "storageType":"ssd"
//	},
//
//	{
//	  "sizeInGB":3720,
//	  "storageType":"ssd"
//	}]
type EphemeralDisk struct {
	StorageType StorageType `json:"storageType,omitempty"`
	SizeInGB    int         `json:"sizeInGB,omitempty"`
}

// BidMode 竞价实例的竞价模式
type BidMode string

const (
	// BidModeMarketPrice 跟随市场价出价
	BidModeMarketPrice BidMode = "MARKET_PRICE_BID"

	// BidModeCustomPrice 用户自定义出价
	BidModeCustomPrice BidMode = "CUSTOM_BID"

	// BidModeMarketTidy 潮汐实例跟随市场出价
	BidModeMarketTidy BidMode = "market"
)

// ServiceType Service 类型
type ServiceType string

const (
	// ServiceTypeBCC Service 类型 BCC
	ServiceTypeBCC ServiceType = "BCC"

	// ServiceTypeCDS Service 类型 CDS
	ServiceTypeCDS ServiceType = "CDS"

	// ServiceTypeEIP Service 类型 EIP
	ServiceTypeEIP ServiceType = "EIP"

	// ServiceTypeSysCDS Service 类型, 系统盘, 询价接口返回的类型之一
	ServiceTypeSysCDS ServiceType = "SYS-CDS"

	ServiceTypeBBC ServiceType = "BBC"
)

// InstanceType - BCC Instance 类型
type InstanceType string

const (
	// InstanceTypeN1 普通型 BCC 实例
	InstanceTypeN1 InstanceType = "0"

	// InstanceTypeN2 普通型II BCC 实例
	InstanceTypeN2 InstanceType = "7"

	// InstanceTypeN3 普通型Ⅲ BCC 实例
	InstanceTypeN3 InstanceType = "10"

	// InstanceTypeN4 网络增强型 BCC 实例
	InstanceTypeN4 InstanceType = "12"

	// InstanceTypeN5 普通IV BCC 实例
	InstanceTypeN5 InstanceType = "13"

	// InstanceTypeC1 计算优化型实例
	InstanceTypeC1 InstanceType = "4"

	// InstanceTypeC2 计算优化 Ⅱ 型实例
	InstanceTypeC2 InstanceType = "11"

	// InstanceTypeS1 存储优化型实例
	InstanceTypeS1 InstanceType = "5"

	// InstanceTypeG1 GPU 型实例
	InstanceTypeG1 InstanceType = "9"

	// InstanceTypeF1 FPGA 型实例
	InstanceTypeF1 InstanceType = "8"

	// InstanceTypeDCC DCC
	InstanceTypeDCC InstanceType = "1"

	// InstanceTypeBBC BBC
	InstanceTypeBBC InstanceType = "2"

	// InstanceTypeBBCGPU BBC GPU； 但是从实际情况来看，GPU还是2，所以在fillspec中，通过判断 bbcFlavor 是否包含GPU来确定instanceType
	InstanceTypeBBCGPU InstanceType = "3"
)

// StorageType 存储类型
type StorageType string

const (
	// StorageTypeSTD1 上一代云磁盘
	StorageTypeSTD1 StorageType = "sata"

	// StorageTypeHP1 高性能型
	StorageTypeHP1 StorageType = "ssd"

	// StorageTypeCloudHP1 SSD 型
	StorageTypeCloudHP1 StorageType = "premium_ssd"

	StorageTypeNNME StorageType = "nvme"

	// TODO: 下面的值待确定，目前 CCE 不支持

	// StorageTypeHDD 普通型
	StorageTypeHDD StorageType = "hdd"

	// StorageTypeLocal 本地盘
	StorageTypeLocal StorageType = "local"

	// StorageTypeDCCSATA Sata 盘, 创建 DCC 实例专用
	StorageTypeDCCSATA StorageType = "SATA"

	// StorageTypeDCCSSD SSD 盘, 创建 DCC 实例专用
	StorageTypeDCCSSD StorageType = "SSD"

	// StorageTypeEnhancedSSD 增强型SSD
	StorageTypeEnhancedSSD = "enhanced_ssd_pl1"

	// open api
	// StorageTypeStd1          StorageType = "std1"
	// StorageTypeHP1           StorageType = "hp1"
	// StorageTypeCloudHP1      StorageType = "cloud_hp1"
	// StorageTypeLocal         StorageType = "local"
	// StorageTypeSATA          StorageType = "sata"
	// StorageTypeSSD           StorageType = "ssd"
	// StorageTypeHDDThroughput StorageType = "HDD_Throughput"
	// StorageTypeHdd           StorageType = "hdd"
	// StorageTypeLocalSSD      StorageType = "local-ssd"
	// StorageTypeLocalHDD      StorageType = "local-hdd"
	// StorageTypeLocalNVME     StorageType = "local-nvme"
)

// GPUType GPU 类型
type GPUType string

const (
	// GPUTypeV100_32 V100-32
	GPUTypeV100_32 GPUType = "nTeslaV100-32"

	// GPUTypeV100_16 V100-16
	GPUTypeV100_16 GPUType = "nTeslaV100-16"

	// GPUTypeV100 V100
	GPUTypeV100 GPUType = "nTeslaV100"

	// GPUTypeP40 P40
	GPUTypeP40 GPUType = "nTeslaP40"

	// GPUTypeP4 P4
	GPUTypeP4 GPUType = "nTeslaP4"

	// GPUTypeK40 K40
	GPUTypeK40 GPUType = "nTeslaK40"
)

// VolumeConfig 定义创建 Volume 的参数
type VolumeConfig struct {
	ServiceType       ServiceType               `json:"serviceType"`
	AvailableZone     internalvpc.AvailableZone `json:"logicalZone"`
	CDSList           []CDSConfig               `json:"cdsDiskSize"`
	ProductType       ProductType               `json:"productType"`
	PurchaseNum       int                       `json:"purchaseNum"`
	PurchaseLength    int                       `json:"purchaseLength"`
	AutoRenewTimeUnit string                    `json:"autoRenewTimeUnit"`
	AutoRenewTime     int                       `json:"autoRenewTime"`
}

// CDSConfig 定义创建 CDS 的参数
type CDSConfig struct {
	SizeInGB      int                       `json:"sizeInGB"`
	Size          string                    `json:"size"`
	StorageType   StorageType               `json:"volumeType"`
	AvailableZone internalvpc.AvailableZone `json:"logicalZone"`
	SnapshotID    string                    `json:"snapshotId"`
}

// EIPConfig 定义创建 EIP 的参数
type EIPConfig struct {
	ServiceType     ServiceType      `json:"serviceType"`
	Name            string           `json:"name"`
	BillingMethod   EIPBillingMethod `json:"subProductType"`
	BandwidthInMbps int              `json:"bandwidthInMbps"`

	ProductType       ProductType  `json:"productType"`
	PurchaseNum       int          `json:"purchaseNum"`
	PurchaseType      PurchaseType `json:"purchaseType"`
	PurchaseLength    int          `json:"purchaseLength"`
	AutoRenewTimeUnit string       `json:"autoRenewTimeUnit"`
	AutoRenewTime     int          `json:"autoRenewTime"`
}

type PurchaseType string

const (
	PurchaseTypeBGP         PurchaseType = "BGP"
	PurchaseTypeChinaMobile PurchaseType = "ChinaMobile"
	PurchaseTypeChinaTelcom PurchaseType = "ChinaTelcom"
	PurchaseTypeChinaUnicom PurchaseType = "ChinaUnicom"
	PurchaseTypeStatic      PurchaseType = "Static"
)

// EIPBillingMethod EIP 计费方式
type EIPBillingMethod string

const (
	// EIPBillingMethodNetTraffic 通过流量计费
	EIPBillingMethodNetTraffic EIPBillingMethod = "netraffic"

	// EIPBillingMethodBandwidth 通过带宽计费
	EIPBillingMethodBandwidth EIPBillingMethod = "bandwidth"

	// PeakBandwidth_Percent_95_A
	PeakBandwidthPercent95A EIPBillingMethod = "PeakBandwidth_Percent_95_A"
)

// GetServersResponse 查询 BCC 返回
type GetServersResponse struct {
	OrderBy    string `json:"orderBy"`
	Order      string `json:"order"`
	PageNo     int    `json:"pageNo"`
	PageSize   int    `json:"pageSize"`
	TotalCount int    `json:"totalCount"`

	Result []Server `json:"result"`
}

// Server 虚机详情
type Server struct {
	HostID   string `json:"hostId"`
	HostUuid string `json:"hostUuid"`
	HostName string `json:"hostName"`
	UUID     string `json:"uuid"`

	InstanceName  string       `json:"name"`
	InstanceID    string       `json:"instanceId"`
	InstanceUUID  string       `json:"id"`
	InstanceType  InstanceType `json:"instanceType"`
	SpecificType  int          `json:"specificType"`
	InstanceStyle string       `json:"instanceStyle"`
	Status        ServerStatus `json:"status"`
	OrderID       string       `json:"orderId"`
	OrderStatus   string       `json:"orderStatus"`
	Payment       ProductType  `json:"payment"`
	VNCEnabled    bool         `json:"vncEnabled"`
	Application   Application  `json:"application"`
	Description   string       `json:"desc"`
	CreateTime    string       `json:"createTime"`
	ReleaseTime   string       `json:"releaseTime"`

	Flavor        string `json:"flavor"`
	FlavorType    string `json:"flavorType"`
	CPU           int    `json:"cpu"`
	Memory        int    `json:"memory"`
	SysDiskSize   int    `json:"sysDisk"`
	LocalDiskSize int    `json:"diskSize"`
	EBSSize       []CDS  `json:"ebsSize"`

	FPGACard  string  `json:"fpgaCard"`
	FPGACount int     `json:"fpgaCount"`
	GPUType   GPUType `json:"gpuCard"`
	GPUCount  int     `json:"gpuCount"`

	Raid          string    `json:"raid"`
	BBCFlavorID   string    `json:"bbcFlavorId"`
	BBCFlavor     BBCFlavor `json:"bbcFlavor"`
	BBCFlavorType string    `json:"bbcFlavorType"`

	ImageUUID string               `json:"imageId"`
	ImageType logicimage.ImageType `json:"imageType"`
	OSName    string               `json:"osName"`
	OSType    string               `json:"osType"`
	OSVersion string               `json:"osVersion"`
	OSArch    string               `json:"osArch"`
	OSBuild   string               `json:"osBuild"`
	OSLang    string               `json:"osLang"`

	Region          string                    `json:"region"`
	VPCID           string                    `json:"vpcId"`
	VPCName         string                    `json:"vpcName"`
	VPCCIDR         string                    `json:"vpcCidr"`
	SubnetName      string                    `json:"subnetName"`
	AvailableZone   internalvpc.AvailableZone `json:"logicalZone"`
	SubnetType      vpc.SubnetType            `json:"subnetType"`
	SubnetUUID      string                    `json:"subnetUuid"`
	InternalIP      string                    `json:"internalIp"`
	FloatingIP      string                    `json:"floatingIp"`
	PublicIP        string                    `json:"publicIp"`
	Network         int                       `json:"network"`
	EIPID           string                    `json:"eipId"`
	EIPAllocationID string                    `json:"eipAllocationId"`
	EIPStatus       string                    `json:"eipStatus"`
	EIPGroupID      string                    `json:"eipGroupId"`
	EIPType         EIPType                   `json:"eipType"`
	IPv6            string                    `json:"ipv6"`

	ProductType    ProductType `json:"payment"`
	AutoRenew      bool        `json:"autoRenew"`
	ExpireDate     int         `json:"expireDate"`
	PurchaseNum    int         `json:"purchaseNum"`
	PurchaseLength int         `json:"purchaseLength"`
	ExpireTime     string      `json:"expireTime"`

	Extra string `json:"extra"`
}

type BBC struct {
	HostID   string `json:"hostId"`
	HostUuid string `json:"hostUuid"`
	HostName string `json:"hostName"`
	UUID     string `json:"uuid"`

	InstanceName string       `json:"name"`
	InstanceID   string       `json:"instanceId"`
	InstanceUUID string       `json:"id"`
	InstanceType InstanceType `json:"instanceType"`
	Status       ServerStatus `json:"status"`
	OrderID      string       `json:"orderId"`
	OrderStatus  string       `json:"orderStatus"`
	Payment      ProductType  `json:"payment"`
	SpecificType int          `json:"specificType"`
	VNCEnabled   bool         `json:"vncEnabled"`
	Description  string       `json:"desc"`
	CreateTime   string       `json:"createTime"`
	ReleaseTime  string       `json:"releaseTime"`

	Raid        string    `json:"raid"`
	BBCFlavorID string    `json:"bbcFlavorId"`
	BBCFlavor   BBCFlavor `json:"bbcFlavor"`
	CPU         int       `json:"cpu"`
	Memory      int       `json:"memory"`
	SysDisk     int       `json:"sysDisk"`
	DiskSize    int       `json:"diskSize"`

	ImageUUID string               `json:"imageId"`
	ImageType logicimage.ImageType `json:"imageType"`
	OSName    string               `json:"osName"`
	OSType    string               `json:"osType"`
	OSVersion string               `json:"osVersion"`
	OSArch    string               `json:"osArch"`
	OSBuild   string               `json:"osBuild"`
	OSLang    string               `json:"osLang"`

	Network         int                       `json:"network"`
	AvailableZone   internalvpc.AvailableZone `json:"logicalZone"`
	SubnetType      vpc.SubnetType            `json:"subnetType"`
	SubnetUUID      string                    `json:"subnetUuid"`
	InternalIP      string                    `json:"internalIp"`
	FloatingIP      string                    `json:"floatingIp"`
	PublicIP        string                    `json:"publicIp"`
	EIPID           string                    `json:"eipId"`
	EIPAllocationID string                    `json:"eipAllocationId"`
	EIPStatus       string                    `json:"eipStatus"`
	EIPGroupID      string                    `json:"eipGroupId"`
	EIPType         EIPType                   `json:"eipType"`

	Extra string `json:"extra"`
}

type BBCFlavor struct {
	FlavorName string `json:"name_fri"`
	FlavorType string `json:"flavorType"`
	CPU        int    `json:"bbc_cpu_cores"`
	CPUType    string `json:"bbc_cpu_type"`
	MEM        string `json:"bbc_memory"` // eg: 128GB
	DISK       string `json:"bbc_disk"`
	GPU        string `json:"bbc_gpu"`
	Network    string `json:"bbc_network"`
}

// ReinstallBBCArgs 重装 BBC 参数
type ReinstallBBCArgs struct {
	ServerIDs []string `json:"serverIds"`
	ImageID   string   `json:"imageId"`
	Password  string   `json:"password"`

	// KeepData 是否保留数据; 值为 true 时, RaidID 和 SysRootSize 字段不生效
	KeepData bool `json:"keepData"`
	// RaidID 在 KeepData=false 时必填, KeepData=true 时不生效
	RaidID string `json:"raidId"`
	// SysRootSize 系统盘根分区大小, 默认为20G, 取值范围为20-100; KeepData=false 时必填，KeepData=true 时不生效
	SysRootSize int `json:"sysRootSize"`
}

// EIPType EIP Type
type EIPType string

const (
	// EIPTypeNormal normal EIP
	EIPTypeNormal EIPType = "normal"
	// EIPTypeShared shared EIP
	EIPTypeShared EIPType = "shared"
)

// RenameServersArgs 重命名虚机参数
type RenameServersArgs struct {
	ServerIDs []string `json:"serverIds"`
	Name      string   `json:"name"`
}

// CDS CDS 详情
type CDS struct {
	SnapshotID   string `json:"snapshotId"`
	SnapshotName string `json:"snapshotName"`
	Size         int    `json:"size"`
	VolumeType   string `json:"volumeType"`
}

// GetServersArgs 查询虚机参数
type GetServersArgs struct {
	KeywordType  KeywordType
	Keyword      string
	OrderBy      string
	Order        string
	PageNo       string
	PageSize     string
	InstanceType InstanceType
}

// KeywordType 查询虚机列表关键字类型
type KeywordType string

// KeywordTypeName 查询虚机列表关键字类型 name
const (
	KeywordTypeName KeywordType = "name"
)

type Volume struct {
	ID          string `json:"id"`
	VolumeID    string `json:"volumeId"`
	InstanceID  string `json:"instanceId"`
	Device      string `json:"device"`
	StorageType string `json:"storageType"`
	Type        string `json:"type"` // "1" 、"2"
	DiskSize    int    `json:"diskSize"`
}

type ListCDSResponse struct {
	PageSize   int      `json:"pageSize"`
	PageNo     int      `json:"pageNo"`
	TotalCount int      `json:"totalCount"`
	Result     []Volume `json:"result"`
}

type VolumeType string

const (
	VolumeTypeCDS       VolumeType = "Cds"
	VolumeTypeSystem    VolumeType = "System"
	VolumeTypeEphemeral VolumeType = "Ephemeral"
)

var (
	StorageTypeMap = map[string]StorageType{
		"1": StorageTypeHP1,
		"2": StorageTypeSTD1,
		"3": StorageTypeNNME,
		"4": StorageTypeCloudHP1,
	}

	VolumeTypeMap = map[string]VolumeType{
		"1": VolumeTypeCDS,
		"2": VolumeTypeSystem,
		"3": VolumeTypeEphemeral,
	}
)

// TransStorageNumToOpenBCC - logicbcc.StorageType 转成 bcc.StorageType
func TransStorageNumToOpenBCC(ctx context.Context, Type string) (bcc.StorageType, error) {
	switch Type {
	case "1":
		return bcc.StorageTypeHP1, nil
	case "2":
		return bcc.StorageTypeSTD1, nil
	case "4":
		return bcc.StorageTypeCloudHP1, nil
	case "6":
		return bcc.StorageTypeHDD, nil
	case "21":
		return bcc.StorageTypeEnhancedSSD, nil
	default:
		return "", fmt.Errorf("Unsupported LogicBCC type: %s", Type)

	}
}

// QueryBCCPriceRequest - BCC 询价请求
type QueryBCCPriceRequest struct {
	// PurchaseNum 购买数量, BCC CDS EIP Mkt 共用
	PurchaseNum int `json:"purchaseNum"`

	// PrepayTime 购买时长, 预付费类型生效, 单位: month
	PrepayTime int `json:"purchaseLength"`

	// BCC 配置
	BCC *BCCPriceArgs `json:"bcc"`

	// CDS 配置
	CDS *CDSPriceArgs `json:"cds"`

	// EIP 配置
	EIP *EIPPriceArgs `json:"eip"`

	// Mkt 配置, 暂时忽略
	Mkt *MktPriceArgs `json:"mkt"`
}

// BCCPriceArgs BCC 询价参数
type BCCPriceArgs struct {
	ProductType  ProductType  `json:"productType"`
	InstanceType InstanceType `json:"instanceType"`

	CPU                 int         `json:"cpu"`
	Memory              int         `json:"memory"`
	RootDiskSizeInGB    int         `json:"rootDiskSizeInGb"`
	RootDiskStorageType StorageType `json:"rootDiskStorageType"`
	EphemeralSizeGB     int         `json:"ephemeralSizeGb"`

	// FPGA 配置
	FPGACard     string `json:"fpgaCard"`
	FPGACount    int    `json:"fpgaCount"`
	ContainsFPGA bool   `json:"containsFpga"`

	// GPU 配置
	GPUCard  string `json:"gpuCard"`
	GPUCount int    `json:"gpuCount"`
	SpecID   string `json:"specId"`
	Spec     string `json:"spec"`

	// KunLun 配置
	KunLunCard  string `json:"kunlunCard"`
	KunLunCount int    `json:"kunlunCount"`
}

// CDSPriceArgs CDS 询价参数
type CDSPriceArgs struct {
	ProductType ProductType `json:"productType"`
	Disks       []Disk      `json:"diskConfigs"`
}

// Disk CDS Disk 询价参数
type Disk struct {
	SizeInGB    int         `json:"sizeInGB"`
	StorageType StorageType `json:"storageType"`
	SnapshotID  string      `json:"snapshotId"`
}

// EIPPriceArgs EIP 询价参数
type EIPPriceArgs struct {
	ProductType      ProductType      `json:"productType"`
	EIPBillingMethod EIPBillingMethod `json:"subProductType"`
	BandwidthInMbps  int              `json:"bandwidthInMbps"`
}

// MktPriceArgs Mkt 询价参数
type MktPriceArgs struct {
	ProductType ProductType `json:"productType"`
	ImageID     string      `json:"imageId"`
	ProductID   string      `json:"productId"`
	PackageID   string      `json:"packageId"`
	MktType     string      `json:"type"`
}

// QueryBCCPriceResponse - BCC 询价返回
type QueryBCCPriceResponse struct {
	// Money = PerMoney * PurchaseNum, 不包含 EIP 的价格
	Money        string        `json:"money"`
	PurchaseNum  int           `json:"count"`
	PerMoney     string        `json:"perMoney"`
	PriceDetails []PriceDetail `json:"priceDetail"`

	// 暂时忽略, 接口返回为空
	// DayCount    int64   `json:"dayCount"`
	// UnitPrice   float64 `json:"unitPrice"`
	// OriginMoney float64 `json:"originMoney"`
}

// PriceDetail 价格详情
type PriceDetail struct {
	ServiceType ServiceType `json:"servicetype"`

	// Prices = PricePerResourceList * PurchaseNum
	// Prices 当 CDS 有多种配置时, 每种配置价格对应一个数组元素, 对于 BCC EIP 只有一个 Price
	Prices               []float64 `json:"price"`
	PricePerResourceList []float64 `json:"pricePerResourceList"`

	// EIP 专属
	EIPNetTrafficUnitPrice string            `json:"networkMoney"`
	EIPNetTrafficUnit      EIPNetTrafficUnit `json:"networkUnit"`

	// CDS 专属, 可以忽略
	CDSPricesOnStorageType map[string]Detail `json:"detail"`
}

// EIPNetTrafficUnit EIP 流量单位
type EIPNetTrafficUnit string

const (
	// EIPNetTrafficUnitGB EIP 流量单位: GB
	EIPNetTrafficUnitGB EIPNetTrafficUnit = "GB"
)

// Detail 详细价格
type Detail struct {
	// Total 总价
	Total float64 `json:"total"`
	// Unit 单价
	Unit float64 `json:"unit"`
}

type GetFlavorsArgs struct {
	PhysicalZone string `json:"physicalZone"`
	ProductType  string `json:"productType"`
	LogicalZone  string `json:"logicalZone"`
}

type GetFlavorsResponse struct {
	Resources []ZoneResourceDetail `json:"zoneResources"`
}

// 按地域的资源
type ZoneResourceDetail struct {
	DefaultZone     bool                 `json:"defaultZone"`
	LogicalZone     string               `json:"logicalZone"`
	PhysicalZone    string               `json:"physicalZone"`
	CDSResources    []DiskFlavor         `json:"cdsResources"`
	BCCResources    []InstanceTypeDetail `json:"bccResources"`
	SysCDSResources []DiskFlavor         `json:"sysCdsResources"`
}

type DiskFlavor struct {
	StorageType string `json:"storageType"`
	Status      string `json:"status"`
}

type InstanceTypeDetail struct {
	IndexValue      int          `json:"indexValue"`
	BillingValue    string       `json:"billingValue"`
	InstanceFamily  string       `json:"instanceFamily"`
	ServerType      string       `json:"serverType"`
	Status          string       `json:"status"`
	EnableEhpemeral bool         `json:"enableEhpemeral"`
	CustomFlavor    CustomFlavor `json:"customFlavor"`
	Flavors         []BCCFlavor  `json:"flavors"`
}

type CustomFlavor struct {
	Enable                bool `json:"enable"`
	MinCPUCount           int  `json:"minCpuCount"`
	MinMemoryCapacityInGB int  `json:"minMemoryCapacityInGB"`
	MaxCpuCount           int  `json:"maxCpuCount"`
	MaxMemoryCapacityInGB int  `json:"maxMemoryCapacityInGB"`
}

type BCCFlavor struct {
	FlavorID           string `json:"flavorId"`
	FlavorUUID         string `json:"flavorUuid"`
	CPUCount           int    `json:"cpuCount"`
	MemoryCapacityInGB int    `json:"memoryCapacityInGB"`
	EphemeralDiskInGb  int    `json:"ephemeralDiskInGb"`
	EphemeralDiskCount int    `json:"ephemeralDiskCount"`
	EphemeralDiskType  string `json:"ephemeralDiskType"`
	GPUCardType        string `json:"gpuCardType"`
	GPUCardCount       int    `json:"gpuCardCount"`
	FPGACardType       string `json:"fpgaCardType"`
	FPGACardCount      int    `json:"fpgaCardCount"`
	KunlunCardType     string `json:"kunlunCardType"`
	KunlunCardCount    int    `json:"kunlunCardCount"`
	Status             string `json:"status"`
	ProductType        string `json:"productType"`
	InstanceType       int    `json:"instanceType"`
	InstanceFamily     string `json:"instanceFamily"`
	Spec               string `json:"spec"`
	SpecID             string `json:"specId"`
	CPUModel           string `json:"cpuModel"`
	CPUGHz             string `json:"cpuGHz"`
	NetworkBandwidth   string `json:"networkBandwidth"`
	NetworkPackage     string `json:"networkPackage"`
	GPUCapcacity       int    `json:"gpuCapcacity"`
}

type QuotaType string

const (
	QuotaTypeBCC QuotaType = "bcc"
)

type GetQuotaResponse struct {
	PostInstanceTotal   int  `json:"postInstanceTotal"`
	PostInstanceCreated int  `json:"postInstanceCreated"`
	PostInstanceIsLeft  bool `json:"postInstanceIsLeft"`
	PreInstanceTotal    int  `json:"preInstanceTotal"`
	PreInstanceCreated  int  `json:"preInstanceCreated"`

	CDSTotal           int `json:"cdsTotal"`
	CDSCreated         int `json:"cdsCreated"`
	CDSRatio           int `json:"cdsRatio"`
	CDSTotalCapacityGB int `json:"cdsTotalCapacityGB"`
	CDSUsedCapacityGB  int `json:"cdsUsedCapacityGB"`

	SnapshotTotal             int `json:"snapshotTotal"`
	SnapshotCreated           int `json:"snapshotCreated"`
	TemplateTotal             int `json:"templateTotal"`
	TemplateCreated           int `json:"templateCreated"`
	AcceptedSharedImageNum    int `json:"acceptedSharedImageNum"`
	AcceptSharedImageNumLimit int `json:"acceptSharedImageNumLimit"`
}

type AssignTagsRequest struct {
	InsertTags  []Tag         `json:"insertTags"`
	Resources   []TagResource `json:"resources"`
	RelationTag bool          `json:"relationTag"`
}

type TagResource struct {
	ResourceUUID string `json:"id"`
	ResourceID   string `json:"resourceId"`
	Tags         []Tag  `json:"tags"`
}

type BBCFlavorID string

// S结尾的表示智能卡，是使用普通的子网，需要安全组ID ; PDD的机型也是用普通子网
const (
	// IO优化型
	BBC_I4_01S BBCFlavorID = "BBC-I4-01S"
	BBC_I2_01  BBCFlavorID = "BBC-I2-01"
	BBC_I4_01  BBCFlavorID = "BBC-I4-01"
	// 大数据型
	BBC_S4_01S   BBCFlavorID = "BBC-S4-01S"
	BBC_S3_KY01S BBCFlavorID = "BBC-S3-KY01S"
	BBC_S4_01    BBCFlavorID = "BBC-S4-01"
	// GPU 型
	GPU_N2_8P40 BBCFlavorID = "GPU-N2-8P40"
	// 通用型
	BBC_G4_01S BBCFlavorID = "BBC-G4-01S"
	BBC_G4_02S BBCFlavorID = "BBC-G4-02S"
	BBC_G4_03S BBCFlavorID = "BBC-G4-03S"
	BBC_G4_04S BBCFlavorID = "BBC-G4-04S"
	BBC_G4_05S BBCFlavorID = "BBC-G4-05S"
	BBC_M4_01S BBCFlavorID = "BBC-M4-01S"
	BBC_SGX_01 BBCFlavorID = "BBC-SGX-01"
	BBC_G4_02  BBCFlavorID = "BBC-G4-02"
	BBC_G4_03  BBCFlavorID = "BBC-G4-03"
)

type Raid string

const (
	Raid5  Raid = "Raid5"
	Raid1  Raid = "Raid1"
	Raid0  Raid = "Raid0"
	Raid10 Raid = "Raid10"
)

type BBCConfig struct {
	ServiceType     ServiceType `json:"serviceType"`
	Type            string      `json:"type"`
	Flavor          BBCFlavorID `json:"flavor"`
	Name            string      `json:"name"`
	AdminPass       string      `json:"adminPass"`
	SecurityGroupID string      `json:"securityGroupId"`

	Raid        Raid   `json:"raid"`
	SYSDiskSize int    `json:"sysDiskSize"`
	UserData    string `json:"userData"`
	DiskInfo    string `json:"diskInfo"`

	ImageID   string               `json:"imageId"`
	ImageType logicimage.ImageType `json:"imageType"`
	OSName    string               `json:"osName"`
	OSType    string               `json:"osType"`

	DeploySetID string `json:"deploySetId"`
	HostName    string `json:"hostName"`
	EnableNuma  bool   `json:"enableNuma"`

	VPCID         string
	AvailableZone internalvpc.AvailableZone `json:"logicalZone"`
	SubnetID      string                    `json:"subnetUuid"`

	Tags []Tag `json:"tags"`

	ProductType    ProductType `json:"productType"`
	PurchaseNum    int         `json:"purchaseNum"`
	PurchaseLength int         `json:"purchaseLength"`
}

type GetBidEventsRequest struct {
	InstanceUUIDs []string `json:"instanceUuids"`
}

type GetBidEventsResponse struct {
	BidEvents []BidEvent `json:"events"`
}

type BidEvent struct {
	InstanceID   string         `json:"instanceId"`
	InstanceUUID string         `json:"instanceUuid"`
	EventTime    time.Time      `json:"eventTime"`
	Action       BidEventAction `json:"action"`
}

type BidEventAction string

const (
	// GetBidEvents 接口限制一次查询最多 100 事件
	GetBidEventsLimit = 100

	BidEventActionDelete BidEventAction = "delete"
)
