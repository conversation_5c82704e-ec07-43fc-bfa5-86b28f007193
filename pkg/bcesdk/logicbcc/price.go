package logicbcc

import (
	"bytes"
	"context"
	"encoding/json"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
)

// QueryBCCPrice - 查询 BCC (带 CDS/EIP) 价格
func (c *Client) QueryBCCPrice(ctx context.Context, request *QueryBCCPriceRequest, option *bce.SignOption) (*QueryBCCPriceResponse, error) {
	params := map[string]string{}

	postContent, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("/api/logical/bcc/v1/instance/price", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	result := new(QueryBCCPriceResponse)
	err = json.Unmarshal(bodyContent, result)
	if err != nil {
		return nil, err
	}

	return result, nil
}
