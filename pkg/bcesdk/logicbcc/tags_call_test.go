package logicbcc

import (
	"context"
	"testing"
	"time"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
)

func init() {
	region := "gz"
	accessKeyID := "e0c68be8495540f280b3e9ef03ec25d2"
	secretAccessKey := "b599d5f4f30e41bcb0e1482c9de65bd6"

	bccclient = NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      region,
		Endpoint:    Endpoints[region],
	})
	bccclient.SetDebug(true)
}

func testClient_AssignTags(t *testing.T) {
	assignReq := &AssignTagsRequest{
		InsertTags: []Tag{
			{
				TagKey:   "user",
				TagValue: "yuhongwei",
			},
		},
		Resources: []TagResource{
			{
				ResourceUUID: "4510506d-ee30-4db3-ab94-6117217f0ba3",
				ResourceID:   "i-R0qy7HsK",
				Tags: []Tag{
					{
						TagKey:   "source",
						TagValue: "CCE",
					},
					{
						TagKey:   "user",
						TagValue: "yuhongwei",
					},
				},
			},
		},
	}

	err := bccclient.AssignTags(context.TODO(), assignReq, NewSignOption())
	if err != nil {
		t.Errorf("AssignTags failed: %v", err)
	}
	t.Logf("AssignTags succeeded")
}
