package logicbcc

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
)

var priceClient *Client

func init() {
	//accessKeyID     = "5fded23b03594981872fbfadaad70ef6"
	//secretAccessKey = "6b109261727a4fee8fc5db4bf5ec6c51"

	accessKeyID := "e0c68be8495540f280b3e9ef03ec25d2"
	secretAccessKey := "b599d5f4f30e41bcb0e1482c9de65bd6"

	priceClient = NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Endpoint:    Endpoints["gz"],
		Checksum:    true,
		Timeout:     30 * time.Second,
	})
	priceClient.SetDebug(true)
}

func testQueryBCCPrice(t *testing.T) {
	req := &QueryBCCPriceRequest{
		PurchaseNum: 2,
		PrepayTime:  1,
		BCC: &BCCPriceArgs{
			ProductType:         ProductTypePostpay,
			InstanceType:        "9",
			CPU:                 8,
			Memory:              32,
			EphemeralSizeGB:     0,
			FPGACard:            "",
			FPGACount:           0,
			ContainsFPGA:        false,
			GPUCard:             "nTeslaV100-16",
			GPUCount:            1,
			KunLunCard:          "",
			KunLunCount:         0,
			RootDiskSizeInGB:    50,
			RootDiskStorageType: StorageTypeHP1,
			SpecID:              "lgn2",
		},
		CDS: &CDSPriceArgs{
			ProductType: ProductTypePostpay,
			Disks: []Disk{
				{
					SizeInGB:    10,
					StorageType: StorageTypeSTD1,
					SnapshotID:  "",
				},
				{
					SizeInGB:    50,
					StorageType: StorageTypeHP1,
					SnapshotID:  "",
				},
			},
		},
		EIP: &EIPPriceArgs{
			ProductType:      ProductTypePostpay,
			EIPBillingMethod: EIPBillingMethodNetTraffic,
			BandwidthInMbps:  1,
		},
	}

	resp, err := priceClient.QueryBCCPrice(context.TODO(), req, NewSignOption())

	if err != nil {
		t.Errorf("QueryBCCPrice failed: %v", err)
		return
	}

	if str, err := json.MarshalIndent(resp, "", "  "); err == nil {
		t.Logf("QueryBCCPrice success: %v", string(str))
	}
}
