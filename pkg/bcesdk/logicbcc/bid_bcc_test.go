package logicbcc

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
)

func testClient_GetBidEvents(t *testing.T) {
	accessKeyID := "********************************"
	secretAccessKey := "6b109261727a4fee8fc5db4bf5ec6c51"

	//accessKeyID := "********************************"
	//secretAccessKey := "b599d5f4f30e41bcb0e1482c9de65bd6"

	bbcclient = NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Endpoint:    Endpoints["sandbox"],
	})
	bbcclient.SetDebug(true)

	getReq := &GetBidEventsRequest{
		InstanceUUIDs: []string{
			"abf0e79e-1c50-4961-9c7f-b6ce92620268",
			"af6e2c54-55cb-4bee-8d63-cb63e5678add",
		},
	}

	response, err := bbcclient.GetBidEvents(context.TODO(), getReq, NewSignOption())
	if err != nil {
		t.Errorf("GetBidEvents failed: %v", err)
		return
	}
	if responseByte, err := json.Marshal(response); err == nil {
		t.Logf("GetBidEvents succeeded: %s", string(responseByte))
	}
}
