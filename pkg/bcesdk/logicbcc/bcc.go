package logicbcc

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/logger"
)

// CreateServers - 创建虚机
//
// PARAMS:
//   - ctx: The context to trace request
//   - CreateServersArgs: 创建虚机请求
//
// RETURNS:
//
//	*CreateServersResponse: 订单号以及虚机ID列表
//	error: nil if succeed, error if fail
func (c *Client) CreateServers(ctx context.Context, args *CreateServersArgs, clientToken string, option *bce.SignOption) (*CreateServersResponse, error) {
	if args == nil {
		return nil, fmt.Errorf("args is nil")
	}

	params := map[string]string{
		"clientToken": clientToken,
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	logger.Infof(ctx, "postContent: %s", string(postContent))

	req, err := bce.NewRequest("POST", c.GetURL("api/logical/bcc/v1/instance/create", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	result := new(CreateServersResponse)
	err = json.Unmarshal(bodyContent, result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

// ListServersByIDs - 通过虚机ID获取虚机列表
//
// PARAMS:
//   - ctx: The context to trace request
//   - serverUUIDs: 虚机ID列表
//
// RETURNS:
//
//	[]SimpleServerVO: 虚机列表
//	error: nil if succeed, error if fail
func (c *Client) ListServersByIDs(ctx context.Context, instanceIDs []string, option *bce.SignOption) (*ListServersResponse, error) {
	params := map[string]string{}

	args := &struct {
		IDs  []string `json:"ids"`
		From string   `json:"from"`
	}{
		IDs:  instanceIDs,
		From: "",
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("api/logical/bcc/v1/instance/listServersByUuids", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	result := new(ListServersResponse)
	err = json.Unmarshal(bodyContent, result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

// GetServerByID - 获取BCC虚机详情
//
// PARAMS:
//   - ctx: The context to trace request
//   - serverId: BCC虚机ID
//
// RETURNS:
//
//	*Server: BCC虚机详情
//	error: nil if succeed, error if fail
func (c *Client) GetServerByID(ctx context.Context, serverID string, option *bce.SignOption) (*Server, error) {
	if serverID == "" {
		return nil, fmt.Errorf("GetServerByID failed: serverID is nil")
	}

	params := map[string]string{}

	url := fmt.Sprintf("api/logical/bcc/v1/instance/%s", serverID)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	server := new(Server)
	err = json.Unmarshal(bodyContent, server)
	if err != nil {
		return nil, err
	}

	return server, nil
}

// GetServers - 获取BCC虚机列表
//
// PARAMS:
//   - ctx: The context to trace request
//   - queryParamMap: 请求参数Map，keywordType、keyword、orderBy、order、pageNo、pageSize、instanceType
//
// RETURNS:
//
//	*ServerListResponse: 包含BCC虚机列表
//	error: nil if succeed, error if fail
func (c *Client) GetServers(ctx context.Context, args *GetServersArgs, option *bce.SignOption) (*GetServersResponse, error) {
	if args == nil {
		args = &GetServersArgs{}
	}

	params := map[string]string{}

	if args.KeywordType != "" {
		params["keywordType"] = string(args.KeywordType)
	}

	if args.Keyword != "" {
		params["keyword"] = args.Keyword
	}

	if args.OrderBy != "" {
		params["orderBy"] = args.OrderBy
	}

	if args.Order != "" {
		params["order"] = args.Order
	}

	if args.PageNo != "" {
		params["pageNo"] = args.PageNo
	}

	if args.PageSize != "" {
		params["pageSize"] = args.PageSize
	}

	if args.InstanceType != "" {
		params["instanceType"] = string(args.InstanceType)
	}

	req, err := bce.NewRequest("GET", c.GetURL("api/logical/bcc/v1/instance", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	result := new(GetServersResponse)
	err = json.Unmarshal(bodyContent, result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

// DeleteServers - 删除虚机
//
// PARAMS:
//   - ctx: The context to trace request
//   - ServerDeleteRequest: 删除虚机请求参数，含虚机ID列表
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) DeleteServers(ctx context.Context, args *DeleteServersArgs, option *bce.SignOption) error {
	if args == nil {
		return fmt.Errorf("DeleteServers failed: args is nil")
	}

	params := map[string]string{}

	// 默认为 0, 不删除关联资源
	deleteResource := 0
	deleteCDSSnapshot := 0
	if args.DeleteResource {
		deleteResource = 1
	}
	if args.DeleteCDSSnapshot {
		deleteCDSSnapshot = 1
	}

	reqBody := &struct {
		ServerIDs             []string `json:"serverIds"`
		RelatedReleaseFlag    int      `json:"relatedReleaseFlag"`
		DeleteCdsSnapshotFlag int      `json:"deleteCdsSnapshotFlag"`
	}{
		ServerIDs:             args.ServerIDs,
		RelatedReleaseFlag:    deleteResource,
		DeleteCdsSnapshotFlag: deleteCDSSnapshot,
	}

	postContent, err := json.Marshal(reqBody)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("POST", c.GetURL("api/logical/bcc/v1/instance/delete", params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}

// RenameServers 重命名 BCC
func (c *Client) RenameServers(ctx context.Context, args *RenameServersArgs, option *bce.SignOption) error {
	if args == nil {
		return fmt.Errorf("RenameServers failed: args is nil")
	}

	params := map[string]string{}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("PUT", c.GetURL("/api/logical/bcc/v1/instance/rename", params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}

// ReinstallServers 重装 BCC
func (c *Client) ReinstallServers(ctx context.Context, args *ReinstallServersArgs, option *bce.SignOption) error {
	if args == nil {
		return fmt.Errorf("ReinstallServers failed: args is nil")
	}

	params := map[string]string{}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("PUT", c.GetURL("/api/logical/bcc/v1/instance/rebuild", params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}

// BatchUpdateApplication 批量更新 Application
func (c *Client) BatchUpdateApplication(ctx context.Context, args *BatchUpdateApplicationArgs, option *bce.SignOption) error {
	if args == nil {
		return fmt.Errorf("BatchUpdateApplication failed: args is nil")
	}

	params := map[string]string{}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("POST", c.GetURL("/api/logical/bcc/v1/instance/batchUpdateApplication", params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	return err
}

func (c *Client) GetFlavors(ctx context.Context, args *GetFlavorsArgs, option *bce.SignOption) (*GetFlavorsResponse, error) {
	if args == nil {
		return nil, fmt.Errorf("GetFlavors failed: args is nil")
	}

	params := map[string]string{}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("/api/logical/bcc/v1/instance/flavor", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	result := new(GetFlavorsResponse)
	err = json.Unmarshal(bodyContent, result)
	if err != nil {
		return nil, err
	}

	return result, nil
}
