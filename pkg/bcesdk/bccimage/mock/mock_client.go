// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bccimage (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	gomock "github.com/golang/mock/gomock"
	bccimage "icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bccimage"
	bce "icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
	reflect "reflect"
)

// MockInterface is a mock of Interface interface
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// GetImage mocks base method
func (m *MockInterface) GetImage(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*bccimage.Image, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetImage", arg0, arg1, arg2)
	ret0, _ := ret[0].(*bccimage.Image)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetImage indicates an expected call of GetImage
func (mr *MockInterfaceMockRecorder) GetImage(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetImage", reflect.TypeOf((*MockInterface)(nil).GetImage), arg0, arg1, arg2)
}

// GetImages mocks base method
func (m *MockInterface) GetImages(arg0 context.Context, arg1 *bccimage.ImagesRequest, arg2 *bce.SignOption) (*bccimage.ImagesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetImages", arg0, arg1, arg2)
	ret0, _ := ret[0].(*bccimage.ImagesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetImages indicates an expected call of GetImages
func (mr *MockInterfaceMockRecorder) GetImages(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetImages", reflect.TypeOf((*MockInterface)(nil).GetImages), arg0, arg1, arg2)
}
