package bccimage

import (
	"context"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bccimage Interface

// Interface open API of BCC Image
type Interface interface {
	GetImages(ctx context.Context, args *ImagesRequest, option *bce.SignOption) (*ImagesResponse, error)
	GetImage(ctx context.Context, imageID string, option *bce.SignOption) (*Image, error)
}

// ImagesRequest 查询镜像列表请求参数
type ImagesRequest struct {
	Version   string    `json:"version"`
	Marker    string    `json:"marker,omitempty"`
	MaxKeys   int       `json:"maxKeys,omitempty"`
	ImageType ImageType `json:"imageType,omitempty"`
}

// ImagesResponse 查询镜像列表返回
type ImagesResponse struct {
	Marker      string  `json:"marker"`
	IsTruncated bool    `json:"isTruncated"`
	NextMarker  string  `json:"nextMarker"`
	MaxKeys     int64   `json:"maxKeys"`
	Images      []Image `json:"images"`
}

// ImageResponse 查询指定镜像返回
type ImageResponse struct {
	Image Image `json:"image"`
}

// Image 镜像详细信息，open API 不包含 image UUID
type Image struct {
	ImageID        string         `json:"id"`
	ImageName      string         `json:"name"`
	ImageType      ImageType      `json:"type"`
	OSType         OSType         `json:"osType"`
	OSVersion      string         `json:"osVersion"`
	OSArch         string         `json:"osArch"`
	OSName         OSName         `json:"osName"`
	OSBuild        string         `json:"osBuild"`
	CreateTime     string         `json:"createTime"`
	Status         ImageStatus    `json:"status"`
	Desc           string         `json:"desc"`
	SpecialVersion SpecialVersion `json:"specialVersion"`
}

// ImageType 镜像类型
type ImageType string

const (
	// ImageTypeAll 所有镜像类型
	ImageTypeAll ImageType = "All"
	// ImageTypeSystem "系统镜像/公共镜像"
	ImageTypeSystem ImageType = "System"
	// ImageTypeCustom "自定义镜像"
	ImageTypeCustom ImageType = "Custom"
	// ImageTypeIntegration "服务集成镜像"
	ImageTypeIntegration ImageType = "Integration"
	// ImageTypeSharing 共享镜像
	ImageTypeSharing ImageType = "Sharing"
	// ImageTypeGPUSystem gpu公有
	ImageTypeGPUSystem ImageType = "GpuBccSystem"
	// ImageTypeGPUCustom gpu 自定义
	ImageTypeGPUCustom ImageType = "GpuBccCustom"
	// ImageTypeBBCSystem BBC 公有
	ImageTypeBBCSystem ImageType = "BbcSystem"
	// ImageTypeBBCCustom BBC 自定义
	ImageTypeBBCCustom ImageType = "BbcCustom"
	// ImageTypeService 服务镜像，bcc不提供这个类型，但是我们校验需要一个类型值，这里跟调用方约定传这个值，后端不做校验，但是调用方自己保证镜像的可用性。
	ImageTypeService ImageType = "service"
)

// OSType 操作系统类型
type OSType string

const (
	// OSTypeLinux linux
	OSTypeLinux OSType = "linux"
	// OSTypeWindows windows
	OSTypeWindows OSType = "windows"
)

// OSName 操作系统名字
type OSName string

const (
	// OSNameCentOS centos
	OSNameCentOS OSName = "CentOS"
	// OSNameUbuntu ubuntu
	OSNameUbuntu OSName = "Ubuntu"
	// OSNameWindows windows
	OSNameWindows OSName = "Windows Server"
	// OSNameDebian debian
	OSNameDebian OSName = "Debian"
	// OSNameOpensuse opensuse
	OSNameOpensuse OSName = "opensuse"
)

// ImageStatus 镜像状态
type ImageStatus string

const (
	// ImageStatusAvailable 镜像可用
	ImageStatusAvailable ImageStatus = "Available"
	// ImageStatusNotAvailable 镜像不可用
	ImageStatusNotAvailable ImageStatus = "NotAvailable"
	// ImageStatusError 镜像错误
	ImageStatusError ImageStatus = "Error"
	// ImageStatusCreatedFailed 镜像创建失败
	ImageStatusCreatedFailed ImageStatus = "CreatedFailed"
	// ImageStatusCreating 镜像创建中
	ImageStatusCreating ImageStatus = "Creating"
)

type SpecialVersion string

const (
	// 内部上云 镜像
	SpecialVersionInternal SpecialVersion = "internal_cloud"
)
