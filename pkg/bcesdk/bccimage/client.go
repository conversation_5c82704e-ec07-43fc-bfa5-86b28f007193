package bccimage

import (
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/util"
)

// Endpoints BCC Image endpoints
var Endpoints = map[string]string{
	"bj":  "bcc.bj.baidubce.com",
	"gz":  "bcc.gz.baidubce.com",
	"su":  "",
	"hkg": "",
	"fwh": "",
	"bd":  "",
}

// Client BCC Image open API
type Client struct {
	*bce.Client
}

// NewClient return client
func NewClient(config *bce.Config) *Client {
	bceClient := bce.NewClient(config)
	return &Client{bceClient}
}

// GetURL generates the full URL of http request for Baidu Cloud  API.
func (c *Client) GetURL(version string, params map[string]string) string {
	host := c.Endpoint
	if host == "" {
		host = Endpoints[c.GetRegion()]
	}
	uriPath := version
	return c.Client.GetURL(host, uriPath, params)
}

// NewSignOption new bce SignOption
func NewSignOption() *bce.SignOption {
	option := &bce.SignOption{}
	option.AddHeader("x-bce-request-id", util.GetRequestID())
	option.AddHeadersToSign("host", "x-bce-date")

	return option
}
