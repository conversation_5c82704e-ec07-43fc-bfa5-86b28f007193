package bccimage

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
)

var (
	//region          = "sandbox"
	//accessKeyID     = "********************************"
	//secretAccessKey = "6b109261727a4fee8fc5db4bf5ec6c51"

	region          = "bj"
	accessKeyID     = "********************************"
	secretAccessKey = "b599d5f4f30e41bcb0e1482c9de65bd6"

	imageClient = NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      region,
		Endpoint:    Endpoints[region],
	})
)

func init() {
	imageClient.SetDebug(true)
}

func testGetImages(t *testing.T) {
	args := &ImagesRequest{
		ImageType: ImageTypeSystem,
	}

	resp, err := imageClient.GetImages(context.Background(), args, NewSignOption())
	if err != nil {
		t.Errorf("GetImages failed: %v", err)
	}

	if respByte, err := json.Marshal(resp); err == nil {
		t.Logf("GetImages succeeded: %s", string(respByte))
	}
}

func testGetImage(t *testing.T) {
	resp, err := imageClient.GetImage(context.Background(), "m-ix2tRLmn", NewSignOption())
	if err != nil {
		t.Errorf("GetImage failed: %v", err)
	}

	if respByte, err := json.Marshal(resp); err == nil {
		t.Logf("GetImage succeeded: %s", string(respByte))
	}
}
