package bccimage

import (
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
)

// GetImages - 查询指定类型的镜像
// PARAMS:
//   - ctx: The context to trace request
//   - args: 镜像查询请求参数
//
// RETURNS:
//
//	*ImagesResponse: image list
//	error: nil if succeed, error if fail
func (c *Client) GetImages(ctx context.Context, args *ImagesRequest, option *bce.SignOption) (*ImagesResponse, error) {
	params := map[string]string{}
	var version = "v2"
	if args.Version != "" {
		version = args.Version
	}
	if args.Marker != "" {
		params["marker"] = args.Marker
	}
	if args.MaxKeys != 0 {
		params["maxKeys"] = fmt.Sprintf("%d", args.MaxKeys)
	}
	if args.ImageType != "" {
		params["imageType"] = string(args.ImageType)
	}

	req, err := bce.NewRequest("GET", c.<PERSON>URL(version+"/image", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var imageList *ImagesResponse
	err = json.Unmarshal(bodyContent, &imageList)
	if err != nil {
		return nil, err
	}

	return imageList, nil
}

// GetImage - 通过 ImageID 获取 Image 详细信息
// PARAMS:
//   - ctx: The context to trace request
//   - imageID: image ID
//
// RETURNS:
//
//	*Image: image details
//	error: nil if succeed, error if fail
func (c *Client) GetImage(ctx context.Context, imageID string, option *bce.SignOption) (*Image, error) {
	if imageID == "" {
		return nil, fmt.Errorf("image ID is empty")
	}

	params := map[string]string{}

	req, err := bce.NewRequest("GET", c.GetURL("v2/image/"+imageID, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var imageResp *ImageResponse
	err = json.Unmarshal(bodyContent, &imageResp)
	if err != nil {
		return nil, err
	}

	return &imageResp.Image, nil
}
