// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/11/25 16:30:00, by <EMAIL>, create
*/
/*
实际调用 BLB InternalAPI SDK 测试
*/

package internalblb

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/sts"
)

// 192.168.1.83
func testCreateBackendServer(t *testing.T) {
	ctx := context.TODO()

	// 初始化 sts client
	stsclient := sts.NewClient(ctx, &bce.Config{
		Endpoint: "sts.gz.iam.sdns.baidu.com:8586/v1",
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   "gz",
	}, &bce.Config{
		Endpoint: "iam.gz.bce-internal.baidu.com/v3",
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   "gz",
	}, "BceServiceRole_SERVICE_CCE", "cce", "c8QBqhOKxmGu0Sl2p13guMil3U2grbYt")

	stsclient.SetDebug(true)

	// 初始化 blb client
	client := NewClient(&bce.Config{
		Checksum: true,
		Timeout:  30 * time.Second,
		Endpoint: "blb.gz.bce-internal.baidu.com",
	})

	client.SetDebug(true)

	if err := client.CreateBackendServer(context.TODO(), "lb-e5255f06", &CreateBackendServerArgs{
		BackendServerList: []*BackendServerAbstract{
			&BackendServerAbstract{
				InstanceUUID: "c99d2688-0d5b-4796-b0b3-5780c002bf0b",
				Weight:       100,
			},
		},
	}, stsclient.NewSignOption(ctx, "2e1be1eb99e946c3a543ec5a4eaa7d39")); err != nil {
		t.Errorf("CreateBackendServer failed: %v", err)
	}
}

func testXHSCreateBackendServer(t *testing.T) {
	ctx := context.TODO()

	// 初始化 sts client
	stsclient := sts.NewClient(ctx, &bce.Config{
		Endpoint: "sts.su.iam.sdns.baidu.com:8586/v1",
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   "su",
	}, &bce.Config{
		Endpoint: "iam.su.bce-internal.baidu.com/v3",
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   "su",
	}, "BceServiceRole_SERVICE_CCE", "cce", "c8QBqhOKxmGu0Sl2p13guMil3U2grbYt")

	stsclient.SetDebug(true)

	// 初始化 blb client
	client := NewClient(&bce.Config{
		Checksum: true,
		Timeout:  30 * time.Second,
		Endpoint: "blb.su.bce-internal.baidu.com",
	})

	client.SetDebug(true)

	// 10.217.0.224
	if err := client.CreateBackendServer(context.TODO(), "lb-5ccab5c4", &CreateBackendServerArgs{
		BackendServerList: []*BackendServerAbstract{
			&BackendServerAbstract{
				InstanceUUID: "cd2c6619-554c-4e5c-9f67-ca36731d491c",
				Weight:       100,
			},
			&BackendServerAbstract{
				InstanceUUID: "aa3f852b-6c9e-4ad2-bcdf-f596bf107de7",
				Weight:       100,
			},
			&BackendServerAbstract{
				InstanceUUID: "3e34566f-b21d-4083-8e50-421f203e733e",
				Weight:       100,
			},
		},
	}, stsclient.NewSignOption(ctx, "f8c10cb167474b78ac78e26d259f97df")); err != nil {
		t.Errorf("CreateBackendServer failed: %v", err)
	}
}

func testListBackendServer(t *testing.T) {
	client := NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      region,
		Endpoint:    endpoint,
	})

	client.SetDebug(true)

	resp, err := client.ListBackendServer(context.TODO(), "lb-e3f986dd", NewSignOption())
	if err != nil {
		t.Errorf("ListBackendServer failed: %v", err)
	}

	if str, err := json.Marshal(resp); err == nil {
		t.Errorf("ListBackendServer success: %v", string(str))
	}
}

func testBatchDeleteBackendServer(t *testing.T) {
	client := NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      region,
		Endpoint:    endpoint,
	})

	client.SetDebug(true)

	if err := client.BatchDeleteBackendServer(context.TODO(), "lb-e3f986dd", []string{"7b1b3ab0-6e3e-4fe3-a325-cbe7fcb6306b"}, NewSignOption()); err != nil {
		t.Errorf("ListBackendServer failed: %v", err)
	}
}
