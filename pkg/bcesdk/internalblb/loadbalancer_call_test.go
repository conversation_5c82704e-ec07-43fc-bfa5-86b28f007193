// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/11/25 16:30:00, by <EMAIL>, create
*/
/*
实际调用 BLB InternalAPI SDK 测试
*/

package internalblb

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
)

var gzTestConfig = bce.Config{
	// 李丽丽账号
	Credentials: bce.NewCredentials("6e249e45efa842118f92e4a68f31bb6a", "af4932b1e1de49f7a246f258ca530394"),
	Checksum:    true,
	Timeout:     30 * time.Second,
	Region:      "gz",
	Endpoint:    "blb.gz.bce-internal.baidu.com",
}

var (
	accessKeyID     = "6e249e45efa842118f92e4a68f31bb6a"
	secretAccessKey = "af4932b1e1de49f7a246f258ca530394"
	region          = "gz"
	endpoint        = "blb.gz.bce-internal.baidu.com"
)

//	{
//	    "blbList":[
//	        {
//	            "id":"436570644541694b794c4e3232696d32734f687671673d3d",
//	            "shortId":"lb-a70b6207",
//	            "name":"chenhuan",
//	            "vip":"*************",
//	            "ovip":"*************",
//	            "vni":62,
//	            "vetpIp":""
//	        }
//	    ]
//	}
func testCreateLoadBalancer(t *testing.T) {
	client := NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      region,
		Endpoint:    endpoint,
	})

	resp, err := client.CreateLoadBalancer(context.TODO(), &CreateLoadBalancerArgs{
		Count:       1,
		VPCID:       "0efe29aa-1659-42c5-81a4-6e6e94fbe144", // VPC cce-gztest
		VPCSubnetID: "d77244cd-8d4a-4650-846a-3eac7b85cfd1", // VPC Subnet sbn-mxyxgutspkrw
		Name:        "chenhuan-internal-api-blb",
		AllocateVIP: true,
	}, NewSignOption())
	if err != nil {
		t.Errorf("CreateLoadBalancer failed: %v", err)
	}

	// {
	// 	"blbList":[
	// 		{
	// 			"id":"77355038496d724156563556545a34594f49373556513d3d",
	// 			"shortId":"lb-d66dd5a9",
	// 			"name":"chenhuan-internal-api-blb",
	// 			"vip":"**************",
	// 			"ovip":"**************",
	// 			"vni":61447,
	// 			"vetpIp":""
	// 		}
	// 	]
	// }

	// {
	// 	"blbList":[
	// 		{
	// 			"id":"6876396a307643346a6b4b3250593153442b774a74413d3d",
	// 			"shortId":"lb-7744f8f1",
	// 			"name":"chenhuan-internal-api-blb",
	// 			"vip":"************",
	// 			"ovip":"**************",
	// 			"vni":61447,
	// 			"vetpIp":""
	// 		}
	// 	]
	// }

	if str, err := json.Marshal(resp); err == nil {
		t.Errorf("CreateLoadBalancer success: %v", string(str))
	}
}

func testListLoadBalancer(t *testing.T) {
	client := NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      region,
		Endpoint:    endpoint,
	})

	resp, err := client.ListLoadBalancer(context.TODO(), &ListLoadBalancersArgs{
		// VPCID:  "0efe29aa-1659-42c5-81a4-6e6e94fbe144",
	}, NewSignOption())
	if err != nil {
		t.Errorf("ListLoadBalancer failed: %v", err)
	}

	if str, err := json.Marshal(resp); err == nil {
		t.Errorf("ListLoadBalancer success: %v", string(str))
	}
}

func testListLoadBalancerByName(t *testing.T) {
	client := NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      region,
		Endpoint:    endpoint,
	})

	resp, err := client.ListLoadBalancerByName(context.TODO(), "chenhuan-internal-api-blb", NewSignOption())
	if err != nil {
		t.Errorf("ListLoadBalancerByName failed: %v", err)
	}

	if str, err := json.Marshal(resp); err == nil {
		t.Errorf("ListLoadBalancerByName success: %v", string(str))
	}
}

func testDescribeLoadBalancerByID(t *testing.T) {
	client := NewClient(&gzTestConfig)

	resp, err := client.DescribeLoadBalancerByID(context.TODO(), "lb-7744f8f1", NewSignOption())
	if err != nil {
		t.Errorf("GetLoadBalancerByID failed: %v", err)
	}

	if str, err := json.Marshal(resp); err == nil {
		t.Errorf("GetLoadBalancerByID success: %v", string(str))
	}
}

func testGetLoadBalancer(t *testing.T) {
	client := NewClient(&gzTestConfig)

	// resp, err := client.GetLoadBalancer(context.TODO(), "lb-7744f8f1", NewSignOption())
	resp, err := client.GetLoadBalancer(context.TODO(), "6876396a307643346a6b4b3250593153442b774a74413d3d", NewSignOption())
	if err != nil {
		t.Errorf("GetLoadBalancerByID failed: %v", err)
	}

	if str, err := json.Marshal(resp); err == nil {
		t.Errorf("GetLoadBalancerByID success: %v", string(str))
	}
}

func testDeleteLoadBalancer(t *testing.T) {
	client := NewClient(&gzTestConfig)
	err := client.DeleteLoadBalancer(context.TODO(), "6876396a307643346a6b4b3250593153442b774a74413d3d", NewSignOption())
	if err != nil {
		t.Errorf("GetLoadBalancerByID failed: %v", err)
	}
}
