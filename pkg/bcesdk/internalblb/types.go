// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/11/25 16:30:00, by <EMAIL>, create
*/
/*
本 Package 包含 BLB InternalAPI SDK 的 Interface 定义和实现
参考文档: http://gollum.baidu.com/BLB#BLB-API
*/

package internalblb

import (
	"context"

	"k8s.io/apimachinery/pkg/util/intstr"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/internalblb Interface

// Interface 定义 BLB InternalAPI SDK Interface
type Interface interface {
	SetDebug(bool)

	// LoadBalancer 相关方法
	CreateLoadBalancer(ctx context.Context, args *CreateLoadBalancerArgs, option *bce.SignOption) (*CreateLoadBalancerResponse, error)
	ListLoadBalancer(ctx context.Context, args *ListLoadBalancersArgs, option *bce.SignOption) (*ListLoadBalancersResponse, error)
	ListLoadBalancerByName(ctx context.Context, name string, option *bce.SignOption) (*ListLoadBalancersResponse, error)

	// 推荐使用该方法: GetLoadBalancer 直接调用 BLB 的 Query BLB 接口
	// DescribeLoadBalancerByID 调用 BLB 的 List BLB 接口, 然后再进行过滤
	// 当时可能是没有 Query BLB 的接口, 所以才采用了 List 的方法来实现, 后面废弃该方法
	GetLoadBalancer(ctx context.Context, id string, option *bce.SignOption) (*LoadBalancer, error)
	DescribeLoadBalancerByID(ctx context.Context, id string, option *bce.SignOption) (*LoadBalancer, error)

	DeleteLoadBalancer(ctx context.Context, id string, option *bce.SignOption) error

	// Listener 相关方法
	CreateListener(ctx context.Context, lbShortID string, args *CreateListenerArgs, option *bce.SignOption) error
	ListListener(ctx context.Context, lbShortID string, args *ListListenerArgs, option *bce.SignOption) (*ListListenerResponse, error)
	DescribeListener(ctx context.Context, lbShortID string, port int, option *bce.SignOption) (*Listener, error)
	DeleteListener(ctx context.Context, lbShortID string, port int32, option *bce.SignOption) error

	// BackendServer 相关方法
	CreateBackendServer(ctx context.Context, lbShortID string, args *CreateBackendServerArgs, option *bce.SignOption) error
	ListBackendServer(ctx context.Context, lbShortID string, option *bce.SignOption) (*ListBackendServerResponse, error)
	BatchDeleteBackendServer(ctx context.Context, lbShortID string, instanceUUIDList []string, option *bce.SignOption) error
}

// CreateLoadBalancerArgs args for create app loadbalancer
type CreateLoadBalancerArgs struct {
	Count       int    `json:"count"`
	VPCID       string `json:"vpcId"`
	VPCSubnetID string `json:"subnetId"`
	Desc        string `json:"desc"`
	Name        string `json:"name"`
	Internal    bool   `json:"internal,omitempty"`
	AllocateVIP bool   `json:"allocateVip,omitempty"`

	// Source 如果设置为 cce 后, ListLoadBalancer 会无法获取, 默认去掉
	// Source Source `json:"source,omitempty"`
	// AZZone      string `json:"az_zone"`
	// NginxInVPC  bool   `json:"nginxInVpc,omitempty"`
}

// CreateLoadBalancerResponse 创建 BLB 返回
type CreateLoadBalancerResponse struct {
	BLBList []LoadBalancerAbstract `json:"blbList"`
}

// LoadBalancerAbstract 创建 BLB 返回列表
type LoadBalancerAbstract struct {
	ID      string `json:"id"`
	ShortID string `json:"shortId"`
	Name    string `json:"name"`
	VIP     string `json:"vip"`
	OVIP    string `json:"ovip"`
	VNI     int    `json:"vni"`
	VtepIP  string `json:"vetpIp"`
}

// Source for app blb internal production
type Source string

const (
	// CCE for cce production
	CCE Source = "cce"
	// Default for External user
	Default Source = ""
)

// IsIncludeService list appblb if include internal service
type IsIncludeService string

const (
	// NotIncludeService not include internal service
	NotIncludeService IsIncludeService = "0"
	// IncludeService to include internal service
	IncludeService IsIncludeService = "1"
)

// ExactMatch list appblb if exac match
type ExactMatch string

const (
	// ExactMatchTrue 精准匹配
	ExactMatchTrue ExactMatch = "1"
	// ExactMatchFalse 模糊匹配
	ExactMatchFalse ExactMatch = "0"
)

// BLBType for load balancer type
type BLBType string

const (
	// BLBTypeNormal 普通 BLB 类型
	BLBTypeNormal BLBType = "normal"

	// BLBTypeApplication 应用型 BLB 类型
	BLBTypeApplication BLBType = "application"
)

// ListLoadBalancersArgs args for describe app loadbalancer
type ListLoadBalancersArgs struct {
	ID             string           `json:"id,omitempty"`
	Name           string           `json:"name,omitempty"`
	IP             string           `json:"ip,omitempty"`
	VIP            string           `json:"vip,omitempty"`
	OVIP           string           `json:"ovip,omitempty"`
	EIP            string           `json:"EIP,omitempty"`
	BCCID          string           `json:"bccId,omitempty"`
	VPCID          string           `json:"vpcId,omitempty"`
	Type           BLBType          `json:"type,omitempty"`
	IncludeService IsIncludeService `json:"includeService,omitempty"`
	ExactMatch     ExactMatch       `json:"exactMatch,omitempty"`
}

// ListLoadBalancersResponse response for describe app loadbalancer
type ListLoadBalancersResponse struct {
	Marker      string          `json:"marker"`
	IsTruncated bool            `json:"isTruncated"`
	NextMarker  string          `json:"nextMarker"`
	MaxKeys     int             `json:"maxKeys"`
	BLBList     []*LoadBalancer `json:"blbList"`
}

// LoadBalancer for
type LoadBalancer struct {
	ID         string    `json:"id"`
	ShortID    string    `json:"shortId"`
	IsVPC      bool      `json:"is_vpc"`
	Status     BLBStatus `json:"status"`
	Name       string    `json:"name"`
	CreateTime string    `json:"createTime"`
	VIP        string    `json:"vip"`
	OVIP       string    `json:"ovip"`
	VPCID      string    `json:"vpcId"`
	Desc       string    `json:"desc"`
	SubnetID   string    `json:"subnetId"`
	Internal   bool      `json:"internal"`
	VNI        int       `json:"vni"`
	VtepIP     string    `json:"vtepIp"`
	AZZone     string    `json:"az_zone"`
}

// BLBStatus for BLB status
type BLBStatus string

const (
	// BLBAvailable = BLB available
	BLBAvailable BLBStatus = "available"
)

// CreateListenerArgs create app blb listener args
type CreateListenerArgs struct {
	Type        ListenerType  `json:"type,omitempty"`
	Port        int           `json:"port,omitempty"`
	BackendPort int           `json:"backendPort,omitempty"`
	Scheduler   SchedulerType `json:"scheduler,omitempty"`
}

// ListenerType means listener type
type ListenerType string

const (
	// ListenerTypeHTTP listener type = "HTTP"
	ListenerTypeHTTP ListenerType = "HTTP"

	// ListenerTypeHTTPS listener type = "HTTPS"
	ListenerTypeHTTPS ListenerType = "HTTPS"

	// ListenerTypeTCP listener type = "TCP"
	ListenerTypeTCP ListenerType = "TCP"

	// ListenerTypeUDP listener type = "UDP"
	ListenerTypeUDP ListenerType = "UDP"
)

// SchedulerType menas scheduler type
type SchedulerType string

const (
	// SchedulerTypeRoundRobin scheduler type = "RoundRobin"
	SchedulerTypeRoundRobin SchedulerType = "RoundRobin"

	// SchedulerTypeLeastConnection scheduler type = "LeastConnection"
	SchedulerTypeLeastConnection SchedulerType = "LeastConnection"

	// SchedulerTypeHash scheduler type = "Hash"
	SchedulerTypeHash SchedulerType = "Hash"
)

// ListListenerArgs list listener in app blb
type ListListenerArgs struct {
	Type ListenerType `json:"type,omitempty"`
	Port int32        `json:"port,omitempty"`
}

// ListListenerResponse response of list appblb listener
type ListListenerResponse struct {
	Marker       string      `json:"marker"`
	IsTruncated  bool        `json:"isTruncated"`
	MaxKeys      int         `json:"maxKeys"`
	NextMarker   string      `json:"nextMarker"`
	ListenerList []*Listener `json:"listenerList"`
}

// Listener application loadbalancer listener
type Listener struct {
	Type                 ListenerType       `json:"type"`
	Scheduler            SchedulerType      `json:"scheduler"`
	KeepSession          bool               `json:"keepSession"`
	XForwardedFor        bool               `json:"xForwardedFor"`
	XForwardedProto      bool               `json:"xForwardedProto"`
	KeepSessionParam     string             `json:"keepSessionParam"`
	Status               string             `json:"status"`
	DualAuth             bool               `json:"dualAuth"`
	ClientCertID         []string           `json:"clientCertId"`
	CertIDs              []string           `json:"certIds"`
	SecurityProtocols    string             `json:"securityProtocols"`
	AppliedCiphers       string             `json:"appliedCiphers"`
	GetClientIP          bool               `json:"getClientIp"`
	AcceptInvalidRequest bool               `json:"acceptInvalidRequest"`
	Waf                  bool               `json:"waf"`
	Port                 intstr.IntOrString `json:"port"`
	BackendPort          intstr.IntOrString `json:"backendPort"`
	RedirectPort         intstr.IntOrString `json:"redirectPort"`
	MaxConn              intstr.IntOrString `json:"maxConn"`
	ServerTimeout        intstr.IntOrString `json:"serverTimeout"`
}

// CreateBackendServerArgs 创建 BLB BackendServer 参数
type CreateBackendServerArgs struct {
	BackendServerList []*BackendServerAbstract `json:"backendServerList"`
}

// BackendServerAbstract 参数
type BackendServerAbstract struct {
	InstanceUUID string `json:"instanceId"` // BCC 长 ID
	PrivateIP    string `json:"privateIp,omitempty"`
	Weight       int    `json:"weight"` // 0-100
}

// ListBackendServerResponse ListBackendServer 返回参数
type ListBackendServerResponse struct {
	Marker            string           `json:"marker"`
	IsTruncated       bool             `json:"isTruncated"`
	MaxKeys           int              `json:"maxKeys"`
	NextMarker        string           `json:"nextMarker"`
	BackendServerList []*BackendServer `json:"backendServerList"`
}

// BackendServer application loadbalancer backendServer
type BackendServer struct {
	BLBUUID            string             `json:"blbId"`
	ShortID            string             `json:"shortId"`
	InstanceUUID       string             `json:"instanceId"`
	InstanceIP         string             `json:"instanceIp"`
	PrivateIP          string             `json:"privateIp"`
	Weight             intstr.IntOrString `json:"weight"`
	PhysicalInstanceIP string             `json:"physicalInstanceIp"`
	PortList           []*Port            `json:"portList"`
}

// Port application loadbalancer port
type Port struct {
	ListenerPort        intstr.IntOrString      `json:"listenerPort"`
	BackendPort         intstr.IntOrString      `json:"backendPort"`
	PortType            BackendServerPortType   `json:"portType"`
	HealthCheckPortType HealthCheckPortType     `json:"healthCheckPortType"`
	Status              BackendServerPortStatus `json:"status"`
}

// BackendServerPortType BackendServer 端口类型
type BackendServerPortType string

const (
	// BackendServerPortTypeTCP 端口类型为 TCP
	BackendServerPortTypeTCP BackendServerPortType = "TCP"

	// BackendServerPortTypeHTTP 端口类型为 HTTP
	BackendServerPortTypeHTTP BackendServerPortType = "HTTP"
)

// HealthCheckPortType BackendServer 监控检查端口类型
type HealthCheckPortType string

const (
	// HealthCheckPortTypeTCP BackendServer 监控检查端口类型 TCP
	HealthCheckPortTypeTCP HealthCheckPortType = "TCP"

	// HealthCheckPortTypeHTTP BackendServer 监控检查端口类型 HTTP
	HealthCheckPortTypeHTTP HealthCheckPortType = "HTTP"
)

// BackendServerPortStatus 端口状态
type BackendServerPortStatus string

const (
	// BackendServerPortStatusAlive 端口状态 Alive
	BackendServerPortStatusAlive BackendServerPortStatus = "Alive"

	// BackendServerPortStatusDead 端口状态 Dead
	BackendServerPortStatusDead BackendServerPortStatus = "Dead"

	// BackendServerPortStatusUnknown 端口状态 Unknown
	BackendServerPortStatusUnknown BackendServerPortStatus = "Unknown"
)
