// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/11/25 16:30:00, by <EMAIL>, create
*/
/*
本 Package 包含 BLB InternalAPI SDK 的 Interface 定义和实现
参考文档: http://gollum.baidu.com/BLB#BLB-API
*/

package internalblb

import (
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/util"
)

// Endpoints contains all endpoints of Baidu Cloud BCC.
var Endpoints = map[string]string{
	"bj":      "blb.bj.bce-internal.baidu.com",
	"sandbox": "logical-blb.internal-qasandbox.baidu-int.com",
}

// Client is the bos client implemention for Baidu Cloud BOS API.
type Client struct {
	*bce.Client
}

// NewClient return application loadbalancer client
func NewClient(config *bce.Config) *Client {
	return &Client{
		Client: bce.NewClient(config),
	}
}

// SetDebug 开启或关闭 Debug 模式
func (c *Client) SetDebug(debug bool) {
	c.Client.SetDebug(debug)
}

// GetURL generates the full URL of http request for Baidu Cloud BOS API.
func (c *Client) GetURL(version string, params map[string]string) string {
	host := c.Endpoint
	if host == "" {
		host = Endpoints[c.GetRegion()]
	}
	uriPath := version
	return c.Client.GetURL(host, uriPath, params)
}

// NewSignOption return BLB specified sign option
func NewSignOption() *bce.SignOption {
	option := &bce.SignOption{}
	option.AddHeader("x-bce-request-id", util.GetRequestID())
	option.AddHeadersToSign("host", "x-bce-date")

	return option
}
