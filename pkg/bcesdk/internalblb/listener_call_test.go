// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/11/25 16:30:00, by <EMAIL>, create
*/
/*
实际调用 BLB InternalAPI SDK 测试
*/

package internalblb

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
)

func testCreateListener(t *testing.T) {
	client := NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      region,
		Endpoint:    endpoint,
	})

	client.SetDebug(true)

	if err := client.CreateListener(context.TODO(), "lb-a70b6207", &CreateListenerArgs{
		Type:        ListenerTypeTCP,
		Port:        6443,
		BackendPort: 6443,
		Scheduler:   SchedulerTypeRoundRobin,
	}, NewSignOption()); err != nil {
		t.Errorf("CreateListener failed: %v", err)
	}
}

func testListListener(t *testing.T) {
	client := NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      region,
		Endpoint:    endpoint,
	})

	client.SetDebug(true)

	resp, err := client.ListListener(context.TODO(), "lb-e3f986dd", &ListListenerArgs{
		Type: ListenerTypeTCP,
		Port: 6443,
	}, NewSignOption())
	if err != nil {
		t.Errorf("ListListener failed: %v", err)
	}

	if str, err := json.Marshal(resp); err == nil {
		t.Errorf("ListListener success: %v", string(str))
	}
}

func testDescribeListener(t *testing.T) {
	client := NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      region,
		Endpoint:    endpoint,
	})

	client.SetDebug(true)

	listener, err := client.DescribeListener(context.TODO(), "lb-a70b6207", 6444, NewSignOption())
	if err != nil {
		t.Errorf("QueryListener failed: %v", err)
	}

	if str, err := json.Marshal(listener); err == nil {
		t.Errorf("DescribeListener success: %v", string(str))
	}
}

func testDeleteListener(t *testing.T) {
	client := NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      region,
		Endpoint:    endpoint,
	})

	client.SetDebug(true)

	err := client.DeleteListener(context.TODO(), "lb-e3f986dd", 6443, NewSignOption())
	if err != nil {
		t.Errorf("DeleteListener failed: %v", err)
	}
}
