package ccegateway

import (
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/baidubce/bce-sdk-go/http"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/util/clock"

	"github.com/baidubce/bce-sdk-go/auth"
	"github.com/baidubce/bce-sdk-go/util"
	sdklog "github.com/baidubce/bce-sdk-go/util/log"
)

var _ BCESigner = (*bceSigner)(nil)

type BCESigner interface {
	auth.Signer

	SetVolumeSource(tokenVolume string)
	SetSecretSource(secretGetter func(namespace, name string) (*corev1.Secret, error))
	SetTokenSource(tokenGetter func(*auth.BceCredentials, *auth.SignOptions) ([]byte, []byte, error))
	GetHostAndPort() (string, int)
}

type bceSigner struct {
	Region    string
	ClusterID string
	Token     string
	ExpiredAt int64

	clock clock.Clock

	tokenVolume  string
	secretGetter func(namespace, name string) (*corev1.Secret, error)
	tokenGetter  func(cred *auth.BceCredentials, opt *auth.SignOptions) ([]byte, []byte, error)
}

func NewBCESigner(region, clusterID string) *bceSigner {
	return &bceSigner{
		Region:    region,
		ClusterID: clusterID,
		clock:     clock.RealClock{},
	}
}

func (cg *bceSigner) SetVolumeSource(tokenVolume string) {
	cg.tokenVolume = tokenVolume
}

func (cg *bceSigner) SetSecretSource(secretGetter func(namespace, name string) (*corev1.Secret, error)) {
	cg.secretGetter = secretGetter
}

func (cg *bceSigner) SetTokenSource(tokenGetter func(*auth.BceCredentials, *auth.SignOptions) ([]byte, []byte, error)) {
	cg.tokenGetter = tokenGetter
}

func (cg bceSigner) GetHostAndPort() (string, int) {
	host, port := "cce-gateway.bj.baidubce.com", 80 // default host and port

	if cg.Region != "" {
		host = "cce-gateway." + cg.Region + ".baidubce.com"
	}

	if env := os.Getenv(EndpointOverrideEnv); env != "" {
		// use the endpoint explicitly set in env if exists
		hostPort := strings.Split(env, ":")
		host = hostPort[0]
		if len(hostPort) == 2 {
			if portNum, err := strconv.Atoi(hostPort[1]); err == nil {
				port = portNum
			}
		}
	}
	return host, port
}

func (cg *bceSigner) Sign(req *http.Request, cred *auth.BceCredentials, opt *auth.SignOptions) {
	if err := cg.ensureToken(cred, opt); err != nil {
		sdklog.Errorf("ensureToken failed: %v", err)
		return
	}

	req.SetHeader(TokenHeaderKey, cg.Token)
	req.SetHeader(ClusterIDHeaderKey, cg.ClusterID)
	// add default content-type if not set
	if req.Header("Content-Type") == "" {
		req.SetHeader("Content-Type", "application/json")
	}

	// add x-bce-request-id if not set
	if req.Header(http.BCE_REQUEST_ID) == "" {
		req.SetHeader(http.BCE_REQUEST_ID, util.NewRequestId())
	}

	gatewayHost, _ := cg.GetHostAndPort()
	// make Sign idempotent as it may be invoked many times on retry
	if req.Host() != gatewayHost {
		req.SetHeader(RemoteHostHeaderKey, req.Host())
		req.SetHeader(http.HOST, gatewayHost)
		req.SetHost(gatewayHost)
	}
}

func (cg *bceSigner) ensureToken(cred *auth.BceCredentials, opt *auth.SignOptions) error {
	if cg == nil {
		return fmt.Errorf("bceSigner for cce-gateway is nil")
	}
	if cg.Token != "" && cg.clock.Now().Unix() < cg.ExpiredAt {
		// Our token is still valid, just use it.
		return nil
	}

	var tokenBytes, expiredAtBytes []byte
	var succeeded bool
	var lastErr error

	// Iterate all valid sources until token is successfully fetched.

	if !succeeded && cg.tokenVolume != "" {
		err := func() error {
			var err error
			tokenFile := filepath.Join(cg.tokenVolume, TokenKey)
			tokenBytes, err = ioutil.ReadFile(tokenFile)
			if err != nil {
				return fmt.Errorf("fail to read %s: %w", tokenFile, err)
			}
			expiredAtFile := filepath.Join(cg.tokenVolume, ExpiredAtKey)
			expiredAtBytes, err = ioutil.ReadFile(expiredAtFile)
			if err != nil {
				return fmt.Errorf("fail to read %s: %w", expiredAtFile, err)
			}
			return nil
		}()
		if err != nil {
			sdklog.Errorf("fetch token from volume: %v", err)
			lastErr = err
		} else {
			succeeded = true
		}
	}

	if !succeeded && cg.secretGetter != nil {
		err := func() error {
			tokenSecret, err := cg.secretGetter(TokenSecretNamespace, TokenSecretName)
			if err != nil {
				return fmt.Errorf("fail to get secret %s/%s: %w",
					TokenSecretNamespace, TokenSecretName, err)
			}
			if tokenSecret == nil {
				return fmt.Errorf("token secret is nil")
			}
			var ok bool
			tokenBytes, ok = tokenSecret.Data[TokenKey]
			if !ok {
				return fmt.Errorf("fail to find token key=%s in secret", TokenKey)
			}
			expiredAtBytes, ok = tokenSecret.Data[ExpiredAtKey]
			if !ok {
				return fmt.Errorf("fail to find expiredAt key=%s in secret", ExpiredAtKey)
			}
			return nil
		}()
		if err != nil {
			sdklog.Errorf("fetch token from secret: %v", err)
			lastErr = err
		} else {
			succeeded = true
		}
	}

	if !succeeded && cg.tokenGetter != nil {
		var err error
		tokenBytes, expiredAtBytes, err = cg.tokenGetter(cred, opt)
		if err != nil {
			sdklog.Errorf("fetch token from getter: %v", err)
			lastErr = fmt.Errorf("fail to invoke tokenGetter: %w", err)
		} else {
			succeeded = true
		}
	}

	if !succeeded {
		if lastErr != nil {
			return fmt.Errorf("ensureToken failed with lastErr=%v", lastErr)
		}
		return fmt.Errorf("ensureToken failed: no valid source available")
	}

	cg.ExpiredAt, lastErr = strconv.ParseInt(string(expiredAtBytes), 10, 64)
	if lastErr != nil {
		return fmt.Errorf("fail to parse expiredAt=%s: %w", string(expiredAtBytes), lastErr)
	}
	cg.Token = string(tokenBytes)

	return nil
}
