package ccegateway

import (
	"context"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
)

// Endpoint contains all valid endpoints of cce gateway.
var Endpoint = map[string]string{
	"gztest": "cce-gateway-pre.gz.baidubce.com",
	"qa00":   "gwgp-ckmlfnnafm3.i.gateway-test.baidu-int.com",
}

var _ Client = &client{}

type Client interface {
	SetDebug(bool)
	// EnsureTokenSecret ensures token in cce-gateway and returns the secret yaml of token.
	EnsureTokenSecret(ctx context.Context, clusterID string, opt *bce.SignOption) (string, error)
}

type client struct {
	*bce.Client
}

// Config contains all options for bci.Client.
type Config struct {
	*bce.Config
}

func NewConfig(config *bce.Config) *Config {
	return &Config{config}
}

func NewClient(config *Config) *client {
	bceClient := bce.NewClient(config.Config)
	return &client{bceClient}
}

// GetURL generates the full URL of http request for cce gateway.
func (c *client) GetURL(objectKey string, params map[string]string) string {
	host := c.Endpoint

	if host == "" {
		host = Endpoint[c.GetRegion()]
	}

	if host == "" {
		host = "cce-gateway." + c.GetRegion() + ".baidubce.com"
	}

	uriPath := objectKey

	return c.Client.GetURL(host, uriPath, params)
}
