package ccegateway

import (
	"context"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strconv"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	"gotest.tools/assert"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
)

// signOptionCmpOpt compares if CustomSignFunc in two sign options are identical
var signOptionCmpOpt = cmp.Comparer(func(x, y *bce.SignOption) bool {
	if x == nil && y == nil {
		return true
	}
	if x == nil || y == nil {
		return false
	}
	if x.CustomSignFunc == nil && y.CustomSignFunc == nil {
		return true
	}
	if x.CustomSignFunc == nil || y.CustomSignFunc == nil {
		return false
	}
	req1, err := bce.NewRequest("GET", "example.com", nil)
	if err != nil {
		panic(err)
	}
	req2 := func() *bce.Request {
		tmp := *req1
		return &tmp
	}()
	x.CustomSignFunc(context.TODO(), req1)
	y.CustomSignFunc(context.TODO(), req2)
	if !cmp.Equal(req1.Header, req2.Header) {
		return false
	}
	if !cmp.Equal(req1.Host, req2.Host) {
		return false
	}
	return true
})

func TestGetHostAndPort(t *testing.T) {
	type fields struct {
		Region string
	}
	tests := []struct {
		name     string
		setup    func()
		teardown func()
		fields   fields
		wantHost string
		wantPort int
	}{
		// All test cases.
		{
			name:     "no env and online region is present",
			setup:    func() {},
			teardown: func() {},
			fields: fields{
				Region: "su",
			},
			wantHost: "cce-gateway.su.baidubce.com",
			wantPort: 80,
		},
		{
			name:     "no env and qa region is present",
			setup:    func() {},
			teardown: func() {},
			fields: fields{
				Region: "qa00",
			},
			wantHost: Endpoint["qa00"],
			wantPort: 80,
		},
		{
			name:     "no env and region is empty",
			setup:    func() {},
			teardown: func() {},
			fields: fields{
				Region: "",
			},
			wantHost: "cce-gateway.bj.baidubce.com",
			wantPort: 80,
		},
		{
			name: "endpoint env without port is present",
			setup: func() {
				os.Setenv(EndpointOverrideEnv, "cce-gateway.bce-api.baidu-int.com")
			},
			teardown: func() {
				os.Unsetenv(EndpointOverrideEnv)
			},
			fields: fields{
				Region: "su",
			},
			wantHost: "cce-gateway.bce-api.baidu-int.com",
			wantPort: 80,
		},
		{
			name: "endpoint env with port is present",
			setup: func() {
				os.Setenv(EndpointOverrideEnv, "cce-gateway.bce-api.baidu-int.com:8988")
			},
			teardown: func() {
				os.Unsetenv(EndpointOverrideEnv)
			},
			fields: fields{
				Region: "su",
			},
			wantHost: "cce-gateway.bce-api.baidu-int.com",
			wantPort: 8988,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			defer tt.teardown()
			h := &helper{
				Region: tt.fields.Region,
			}
			host, port := h.GetHostAndPort()
			if host != tt.wantHost {
				t.Errorf("helper.GetHostAndPort() got = %v, want %v", host, tt.wantHost)
			}
			if port != tt.wantPort {
				t.Errorf("helper.GetHostAndPort() got1 = %v, want %v", port, tt.wantPort)
			}
		})
	}
}

func TestNewSignOptionFromVolume(t *testing.T) {
	setupTestDir := func(token, expiredAt string) string {
		testDir, err := ioutil.TempDir("", "ccegateway_test")
		if err != nil {
			panic(err)
		}
		expiredAtFile := filepath.Join(testDir, ExpiredAtKey)
		tokenFile := filepath.Join(testDir, TokenKey)

		if expiredAt != "" {
			if err := ioutil.WriteFile(expiredAtFile, []byte(expiredAt), 0644); err != nil {
				panic(err)
			}
		}
		if token != "" {
			if err := ioutil.WriteFile(tokenFile, []byte(token), 0644); err != nil {
				panic(err)
			}
		}
		return testDir
	}

	type fields struct {
		Region    string
		ClusterID string
		Token     string
		ExpiredAt int64
	}
	type args struct {
		ctx       context.Context
		region    string
		clusterID string
		dir       string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *bce.SignOption
		wantErr bool
	}{
		// All test cases.
		{
			name: "valid token and expiredAt in file",
			fields: fields{
				Region:    "bj",
				ClusterID: "c-test1234",
			},
			args: args{
				ctx: context.TODO(),
				dir: setupTestDir("f04645f1-3522-e67a-44e2-527d6f205a4b", "1578021488"),
			},
			want: &bce.SignOption{
				CustomSignFunc: func(ctx context.Context, req *bce.Request) {
					req.Header.Set(TokenHeaderKey, "f04645f1-3522-e67a-44e2-527d6f205a4b")
					req.Header.Set(ClusterIDHeaderKey, "c-test1234")
					req.Header.Set(RemoteHostHeaderKey, "example.com")
					if req.Header.Get("Content-Type") == "" {
						req.Header.Set("Content-Type", "application/json")
					}
					req.Host = "cce-gateway.bj.baidubce.com"
				},
			},
		},
		{
			name: "invalid expiredAt in file",
			fields: fields{
				Region:    "bj",
				ClusterID: "c-test1234",
			},
			args: args{
				ctx: context.TODO(),
				dir: setupTestDir("f04645f1-3522-e67a-44e2-527d6f205a4b", "abcc"),
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "file not found",
			fields: fields{
				Region:    "bj",
				ClusterID: "c-test1234",
			},
			args: args{
				ctx: context.TODO(),
				dir: setupTestDir("", ""),
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.args.dir != "" {
				defer os.RemoveAll(tt.args.dir)
			}
			h := &helper{
				Region:    tt.fields.Region,
				ClusterID: tt.fields.ClusterID,
				Token:     tt.fields.Token,
				ExpiredAt: tt.fields.ExpiredAt,
			}
			got, err := h.NewSignOptionFromVolume(tt.args.ctx, tt.args.dir)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewSignOptionFromVolume() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !cmp.Equal(got, tt.want, signOptionCmpOpt) {
				t.Errorf("NewSignOptionFromVolume() = %v, want %v, diff is %s",
					got, tt.want, cmp.Diff(got, tt.want, signOptionCmpOpt))
			}
		})
	}

	t.Run("cache expire test", func(t *testing.T) {
		testDir, err := ioutil.TempDir("", "ccegateway_test")
		if err != nil {
			panic(err)
		}
		defer os.RemoveAll(testDir)
		expiredAtFile := filepath.Join(testDir, ExpiredAtKey)
		tokenFile := filepath.Join(testDir, TokenKey)
		token1 := "f04645f1-3522-e67a-44e2-527d6f205a4b"
		token2 := "b1dcfe35-9ae1-4db0-b80d-a4bdf73a3798"
		expireTime := time.Now().Add(3 * time.Second)
		if err := ioutil.WriteFile(expiredAtFile,
			// expire after 3s
			[]byte(strconv.FormatInt(expireTime.Unix(), 10)), 0644); err != nil {
			panic(err)
		}
		if err := ioutil.WriteFile(tokenFile, []byte(token1), 0644); err != nil {
			panic(err)
		}
		go func() {
			ctx, cancel := context.WithDeadline(context.TODO(), expireTime)
			defer cancel()
			<-ctx.Done()
			if err := ioutil.WriteFile(expiredAtFile,
				// expire after 3s
				[]byte(strconv.FormatInt(expireTime.Add(3*time.Second).Unix(), 10)), 0644); err != nil {
				panic(err)
			}
			if err := ioutil.WriteFile(tokenFile, []byte(token2), 0644); err != nil {
				panic(err)
			}
		}()

		h := NewHelper("bj", "c-test1234")

		req, err := bce.NewRequest("GET", "http://example.com", nil)
		if err != nil {
			panic(err)
		}

		for {
			ctx := context.TODO()
			opt, err := h.NewSignOptionFromVolume(ctx, testDir)
			if err != nil {
				panic(err)
			}
			if opt == nil || opt.CustomSignFunc == nil {
				t.Errorf("CustomSignFunc is empty")
				return
			}
			opt.CustomSignFunc(ctx, req)
			wantToken := token1
			if time.Now().After(expireTime) {
				wantToken = token2
			}
			if got := req.Header.Get(TokenHeaderKey); got != wantToken {
				t.Errorf("NewSignOptionFromVolume() token = %v, want %v", got, wantToken)
				return
			}
			if wantToken == token2 {
				return
			}
			// interval of getSignOption
			<-time.After(2 * time.Second)
		}
	})
}

func TestHelperNewSignOptionFromSecret(t *testing.T) {
	type args struct {
		region    string
		clusterID string
		token     string
		expiredAt int64

		ctx          context.Context
		secretGetter func(namespace, name string) (*corev1.Secret, error)

		want    *bce.SignOption
		wantErr bool
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
		{
			name: "success case",
			args: func() args {
				token := "d3ae0740-86b8-4c7a-af35-aef28469cb62"
				expiredAt := time.Now().Add(time.Minute).Unix()
				clusterID := "c-test1234"
				region := "bj"
				return args{
					region:    region,
					clusterID: clusterID,

					ctx: context.TODO(),
					secretGetter: func(ns, name string) (*corev1.Secret, error) {
						if ns != TokenSecretNamespace || name != TokenSecretName {
							return nil, fmt.Errorf("unexpected secret namespace=%s name=%s", ns, name)
						}
						return &corev1.Secret{
							TypeMeta: metav1.TypeMeta{
								Kind:       "Secret",
								APIVersion: "v1",
							},
							ObjectMeta: metav1.ObjectMeta{
								Namespace: TokenSecretNamespace,
								Name:      TokenSecretName,
							},
							Data: map[string][]byte{
								TokenKey:     []byte(token),
								ExpiredAtKey: []byte(strconv.FormatInt(expiredAt, 10)),
							},
						}, nil
					},

					want: &bce.SignOption{
						CustomSignFunc: func(ctx context.Context, req *bce.Request) {
							req.Header.Set(TokenHeaderKey, token)
							req.Header.Set(ClusterIDHeaderKey, clusterID)
							req.Header.Set(RemoteHostHeaderKey, "example.com")
							if req.Header.Get("Content-Type") == "" {
								req.Header.Set("Content-Type", "application/json")
							}
							req.Host = "cce-gateway." + region + ".baidubce.com"
						},
					},
				}
			}(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &helper{
				Region:    tt.args.region,
				ClusterID: tt.args.clusterID,
				Token:     tt.args.token,
				ExpiredAt: tt.args.expiredAt,
			}
			got, err := h.NewSignOptionFromSecret(tt.args.ctx, tt.args.secretGetter)
			if (err != nil) != tt.args.wantErr {
				t.Errorf("helper.NewSignOptionFromSecret() error = %v, wantErr %v", err, tt.args.wantErr)
				return
			}
			if err == nil {
				assert.DeepEqual(t, got, tt.args.want, signOptionCmpOpt)
			}
		})
	}
}
