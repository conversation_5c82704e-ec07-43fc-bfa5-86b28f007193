package logicalbcc

import (
	"context"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
)

// Endpoints logicalbcc 相关 Endpoints
var Endpoints = map[string]string{
	"bj":      "",
	"gz":      "bccproxy.gz.bce-internal.baidu.com:18080/v1",
	"su":      "",
	"hkg":     "",
	"fwh":     "",
	"bd":      "",
	"sandbox": "http://************:18080/v1",
}

var _ Interface = &Client{}

// Client is the logical BCC client implementation for Interface
type Client struct {
	*bce.Client
}

// NewClient 初始化 logicalbcc.Client
func NewClient(ctx context.Context, config *bce.Config) Interface {
	return &Client{
		Client: bce.NewClient(config),
	}
}

// GetURL generates the full URL of http request for BaiDu Cloud API
func (c *Client) GetURL(version string, params map[string]string) string {
	host := c.Endpoint
	if host == "" {
		host = Endpoints[c.GetRegion()]
	}

	uriPath := version
	return c.Client.GetURL(host, uriPath, params)
}

// SetDebug open or close Debug Mode
func (c *Client) SetDebug(debug bool) {
	c.Client.SetDebug(debug)
}
