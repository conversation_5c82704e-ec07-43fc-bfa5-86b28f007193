package logicalbcc

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
	bcerrror "icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/error"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/iam"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/sts"
)

var (
	region   = "sandbox"
	timeout  = 30 * time.Second
	checksum = true

	serviceName = "cce"
	password    = "nHrpnEDTIOonHC1QTOLGDOSjKbqfwKKy"
	accountID   = "00dc1b52d8354d9193536e4dd2c41ae6"

	stsclient = sts.NewClient(context.TODO(), &bce.Config{
		Endpoint: sts.Endpoints[region],
		Checksum: checksum,
		Timeout:  timeout,
		Region:   region,
	}, &bce.Config{
		Endpoint: iam.Endpoints[region],
		Checksum: checksum,
		Timeout:  timeout,
		Region:   region,
	}, sts.RoleName, serviceName, password)

	bccclient = NewClient(context.TODO(), &bce.Config{
		Endpoint: Endpoints[region],
		Checksum: checksum,
		Timeout:  timeout,
		Region:   region,
	})
)

// getEndpoint 用于测试，获取 logical BCC endpoint
func getEndpoint(cred *sts.Credential, region, serviceType string) (string, error) {
	if cred == nil {
		return "", fmt.Errorf("getEndpoint failed: cred is nil")
	}

	for _, service := range cred.Token.Catalog {
		if service.Type == serviceType {
			// 没有 endpoint
			if service.Endpoints == nil || len(service.Endpoints) < 0 {
				return "", fmt.Errorf("getEndpoint failed: len(service.Endpoints) < 0")
			}

			// 寻找对应 region 的 endpoint
			for _, endpoint := range service.Endpoints {
				if endpoint.Region == region {
					return endpoint.URL, nil
				}
			}

			// 找不到对应的Region，返回第一个
			return service.Endpoints[0].URL, nil
		}
	}

	return "", fmt.Errorf("no endpoint found")
}

func setDebug() {
	bccclient.SetDebug(true)
	stsclient.SetDebug(true)
}

func testCreateServers(t *testing.T) {
	// setDebug()
	bccclient.SetDebug(true)

	orderID := "b58a9b5dbbaa4ce28ddc20dc7bc0b3c7"

	serverList := []ServerConfig{
		{
			Name:             "yhw-test-logical-bcc-sdk-go",
			AdminPass:        "test123!T",
			ImageUUID:        "3ea1fb3e-40f9-4a00-bb53-7a5d214573f0",
			SubnetUUID:       "56f0a472-52f8-4052-a48e-8e1bd4920db9",
			CreateFloatingIP: true,
			Count:            1,
			ServerFlavor: ServerFlavor{
				CPU:       4,
				Memory:    8192,
				Disk:      100,
				Ephemeral: 0,
			},
			SecurityGroups: []SecurityGroup{
				{
					Name: "74d8d916-994b-4b05-8ad2-0a7e4f6a0346",
				},
			},
		},
	}
	createServerArgs := &CreateServerArgs{
		Source:        ServerSourceCCE,
		Action:        ServerActionCreate,
		OrderID:       orderID,
		ServerList:    serverList,
		AvailableZone: "AZONE-nmg02",
	}

	resp, err := bccclient.CreateServers(context.TODO(), createServerArgs, "cce-pbp13g1z-y5gg11nc-cce-pbp13g1z-y5gg11nc", stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.Errorf("CreateServers failed: %v", err)
	}

	if respBytes, err := json.Marshal(resp); err == nil {
		t.Logf("CreateServers succeeded: %s", string(respBytes))
	}

	getResp, err := bccclient.GetOrder(context.TODO(), orderID, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		if bceErr, ok := err.(*bcerrror.Error); ok {
			if bceErr.Message == orderID+"Not found" {
				t.Logf("ShowTransaction succeeded: not created yet, %v", bceErr)
				return
			}
		}
		t.Errorf("ShowTransaction failed: %v", err)
		return
	}

	if respBytes, err := json.Marshal(getResp); err == nil {
		t.Logf("ShowTransaction succeeded: %v", string(respBytes))
	}
}

func testGetOrder(t *testing.T) {
	// setDebug()

	orderID := "b58a9b5dbbaa4ce28ddc20dc7bc0b3c7"
	// orderID = "b58a9b5dbbaa4ce28ddc20dc7"

	resp, err := bccclient.GetOrder(context.TODO(), orderID, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		if bceErr, ok := err.(*bcerrror.Error); ok {
			if bceErr.Message == orderID+"Not found" {
				t.Logf("ShowTransaction succeeded: not created yet, %v", bceErr)
				return
			}
		}
		t.Errorf("ShowTransaction failed: %v", err)
		return
	}

	if respBytes, err := json.Marshal(resp); err == nil {
		t.Logf("ShowTransaction succeeded: %v", string(respBytes))
	}
}

func testGetServer(t *testing.T) {
	// setDebug()

	serverUUID := "965c6ce2-a675-44fb-bf3d-c0ed5c78cf08"

	resp, err := bccclient.GetServer(context.TODO(), serverUUID, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.Errorf("GetServer failed: %v", err)
	}

	if respBytes, err := json.Marshal(resp); err == nil {
		t.Logf("GetServer succeeded: %s", string(respBytes))
	}
}

func testDeleteServers(t *testing.T) {
	// setDebug()

	serverUUID := "6f96b32c-8e73-41a5-b7b8-8699ccbd8bb0"

	serverUUIDs := []string{
		serverUUID,
	}

	if err := bccclient.DeleteServers(context.TODO(), serverUUIDs, stsclient.NewSignOption(context.TODO(), accountID)); err != nil {
		t.Errorf("DeleteServers failed: %v", err)
	} else {
		t.Logf("DeleteServers succeeded: %v", serverUUIDs)
	}

	resp, err := bccclient.GetServer(context.TODO(), serverUUID, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.Errorf("GetServer failed: %v", err)
	}

	if respBytes, err := json.Marshal(resp); err == nil {
		t.Logf("GetServer succeeded: %s", string(respBytes))
	}
}
