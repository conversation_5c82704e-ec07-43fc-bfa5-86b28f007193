package bbc

import (
	"context"
	utils2 "icode.baidu.com/baidu/cprom/cloud-stack/cprom-common/utils"
	"testing"
	"time"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
)

var bbcimageclient *Client

func init() {
	//accessKeyID     = "5fded23b03594981872fbfadaad70ef6"
	//secretAccessKey = "6b109261727a4fee8fc5db4bf5ec6c51"

	accessKeyID := "e0c68be8495540f280b3e9ef03ec25d2"
	secretAccessKey := "b599d5f4f30e41bcb0e1482c9de65bd6"

	bbcimageclient = NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Endpoint:    Endpoints["su"],
	})
	bbcimageclient.SetDebug(true)
}

func testGetFlavorImages(t *testing.T) {
	images, err := bbcimageclient.GetFlavorImages(context.TODO(), NewSignOption())
	if err != nil {
		t.Errorf("GetFlavorImages failed: %v", err)
		return
	}
	t.Logf("GetFlavorImages succeeded: %s", utils2.ToJSON(images))
}
