package bbc

import (
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/util"
)

// Endpoints contains endpoints of OpenBBC.
var Endpoints = map[string]string{
	"bj":      "bbc.bj.baidubce.com",
	"gz":      "bbc.gz.baidubce.com",
	"su":      "bbc.su.baidubce.com",
	"hkg":     "bbc.hkg.baidubce.com",
	"fwh":     "bbc.fwh.baidubce.com",
	"bd":      "bbc.bd.baidubce.com",
	"sandbox": "bbc.bj.qasandbox.baidu-int.com",
}

var _ Interface = &Client{}

// Client is the implementation for OpenBBC API.
type Client struct {
	*bce.Client
}

func NewClient(config *bce.Config) *Client {
	bceClient := bce.NewClient(config)
	return &Client{bceClient}
}

// GetURL generates the full URL of http request for OpenBBC API.
func (c *Client) GetURL(objectKey string, params map[string]string) string {
	host := c.Endpoint

	if host == "" {
		host = Endpoints[c.GetRegion()]
	}

	uriPath := objectKey

	return c.Client.GetURL(host, uriPath, params)
}

// NewSignOption return OpenBBC specified sign option
func NewSignOption() *bce.SignOption {
	option := &bce.SignOption{}
	option.AddHeader("x-bce-request-id", util.GetRequestID())
	option.AddHeadersToSign("host", "x-bce-date")

	return option
}
