/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  vm_instance
 * @Version: 1.0.0
 * @Date: 2020/10/23 5:45 下午
 */
package bec

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
)

const (
	VMInstanceStatusStarting   VMInstanceStatus = "STARTING"
	VMInstanceStatusRunning    VMInstanceStatus = "RUNNING"
	VMInstanceStatusPending    VMInstanceStatus = "PENDING"
	VMInstanceStatusException  VMInstanceStatus = "EXCEPTION"
	VMInstanceStatusFailed     VMInstanceStatus = "FAILED"
	VMInstanceStatusUnknown    VMInstanceStatus = "UNKNOWN"
	VMInstanceStatusTerminated VMInstanceStatus = "TERMINATED"
	VMInstanceStatusWaiting    VMInstanceStatus = "WAITING"
	VMInstanceStatusStop       VMInstanceStatus = "STOP"
	VMInstanceStatusWBinding   VMInstanceStatus = "BINDING"
	VMInstanceStatusStopping   VMInstanceStatus = "STOPPING"
)

type ListVMInstanceParams struct {
	PageNo      int
	PageSize    int
	KeywordType string
	Keyword     string
}

type ListVMInstanceResult struct {
	PageNo     int           `json:"pageNo"`
	PageSize   int           `json:"pageSize"`
	TotalCount int           `json:"totalCount"`
	Result     []*VMInstance `json:"result"`
}

type VMImageDetail struct {
	ID                  string `json:"id"`
	ImageID             string `json:"imageId"`
	Name                string `json:"name"`
	ImageType           string `json:"imageType"`
	SnapshotID          string `json:"snapshotId"`
	CPU                 int    `json:"cpu"`
	Memory              int    `json:"memory"`
	OSType              string `json:"osType"`
	OSVersion           string `json:"osVersion"`
	OSName              string `json:"osName"`
	OSBuild             string `json:"osBuild"`
	OSLang              string `json:"osLang"`
	DiskSize            int    `json:"diskSize"`
	CreateTime          string `json:"createTime"`
	Status              string `json:"status"`
	MinMem              int    `json:"minMem"`
	MinCpu              int    `json:"minCpu"`
	MinDiskGb           int    `json:"minDiskGb"`
	Desc                string `json:"desc"`
	OSArch              string `json:"osArch"`
	EphemeralSize       int    `json:"ephemeralSize"`
	ImageDescription    string `json:"imageDescription"`
	ShareToUserNumLimit int    `json:"shareToUserNumLimit"`
	SharedToUserNum     int    `json:"sharedToUserNum"`
	FPGAType            string `json:"fpgaType"`
	NameFri             string `json:"name_fri"`
}

type VMInstanceStatus string
type VMInstance struct {
	VMID              string            `json:"vmId"`
	VMName            string            `json:"vmName"`
	Status            VMInstanceStatus  `json:"status"`
	CPU               int               `json:"cpu"'`
	MEM               int               `json:"mem"`
	GPU               int               `json:"gpu"`
	Region            Region            `json:"region"`
	ServiceProvider   ServiceProvider   `json:"serviceProvider"`
	City              City              `json:"city"`
	NeedPublicIp      bool              `json:"needPublicIp"`
	Bandwidth         string            `json:"bandwidth"`
	InternalIP        string            `json:"internalIp"`
	PublicIP          string            `json:"publicIp"`
	IPV6PublicIP      string            `json:"ipv6PublicIp"`
	OSImage           *VMImageDetail    `json:"osImage"`
	ServiceID         string            `json:"serviceId"`
	CreateTime        string            `json:"createTime"`
	RootDiskSize      int               `json:"rootDiskSize"`
	DataStorage       int               `json:"dataStorage"`
	CCEClusterID      string            `json:"cceCluster"`
	DataVolumeList    []*VMVolumeConfig `json:"dataVolumeList"`
	MultiplePublicIPs []VMPublicIP      `json:"multiplePublicIp"`
}

type VolumeConfig struct {
	DiskType DiskType `json:"volumeType"`
	SizeInGB int      `json:"sizeInGB"`
	Name     string   `json:"name"`
	PVCName  string   `json:"pvcName"`
}

type VMPublicIP struct {
	IP              string          `json:"ip"`
	IPV6            string          `json:"ipv6"`
	ServiceProvider ServiceProvider `json:"serviceProvider"`
}

func (c *client) ListVMInstances(ctx context.Context, args *ListVMInstanceParams, option *bce.SignOption) (*ListVMInstanceResult, error) {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	if args != nil {
		if args.PageNo > 0 {
			params["pageNo"] = strconv.Itoa(args.PageNo)
		}
		if args.PageSize > 0 {
			params["pageSize"] = strconv.Itoa(args.PageSize)
		}
		if args.Keyword != "" {
			params["keyword"] = args.Keyword
		}
		if args.KeywordType != "" {
			params["keywordType"] = args.KeywordType
		}
	}

	url := "/v1/vm/instance"
	req, err := bce.NewRequest("GET", c.GetURL(c.Endpoint, url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result ListVMInstanceResult
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

func (c *client) GetVMInstance(ctx context.Context, vmID string, option *bce.SignOption) (*VMInstance, error) {
	if vmID == "" {
		return nil, fmt.Errorf("empty vm id")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	url := fmt.Sprintf("/v1/vm/instance/%s", vmID)
	req, err := bce.NewRequest("GET", c.GetURL(c.Endpoint, url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result VMInstance
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

func (c *client) DeleteVMInstance(ctx context.Context, vmID string, option *bce.SignOption) error {
	if vmID == "" {
		return fmt.Errorf("empty vm id")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	url := fmt.Sprintf("/v1/vm/instance/%s", vmID)
	req, err := bce.NewRequest("DELETE", c.GetURL(c.Endpoint, url, params), nil)
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

type ReinstallVMInstanceOSParams struct {
	RootPassword string `json:"adminPass"`
	ImageID      string `json:"imageId"`
}

func (c *client) ReinstallVMInstanceOS(ctx context.Context, vmID string, args *ReinstallVMInstanceOSParams, option *bce.SignOption) error {
	if vmID == "" {
		return fmt.Errorf("empty vm id")
	}

	if args == nil {
		return fmt.Errorf("nil reinstall params")
	}

	if args.ImageID == "" {
		return fmt.Errorf("empty image id")
	}

	if args.RootPassword == "" {
		return fmt.Errorf("empty root password")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	putContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("/v1/vm/instance/%s/system/reinstall", vmID)
	req, err := bce.NewRequest("PUT", c.GetURL(c.Endpoint, url, params), bytes.NewBuffer(putContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

func (c *client) LabelAsCCEClusterNode(ctx context.Context, vmID, clusterID string, option *bce.SignOption) error {
	if vmID == "" {
		return fmt.Errorf("empty vm id")
	}

	if clusterID == "" {
		return fmt.Errorf("empty cluster id")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	putContent, err := json.Marshal(map[string]string{
		"cceCluster": clusterID,
	})
	if err != nil {
		return err
	}

	url := fmt.Sprintf("/v1/vm/instance/%s/labels/cceCluster", vmID)
	req, err := bce.NewRequest("PUT", c.GetURL(c.Endpoint, url, params), bytes.NewBuffer(putContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

func (c *client) RemoveLabelOfCCEClusterNode(ctx context.Context, vmID string, option *bce.SignOption) error {
	if vmID == "" {
		return fmt.Errorf("empty vm id")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	url := fmt.Sprintf("/v1/vm/instance/%s/labels/cceCluster", vmID)
	req, err := bce.NewRequest("DELETE", c.GetURL(c.Endpoint, url, params), nil)
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}
