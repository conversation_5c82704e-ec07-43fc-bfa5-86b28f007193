/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  types
 * @Version: 1.0.0
 * @Date: 2020/10/23 11:50 上午
 */
package bec

import (
	"context"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
)

const (
	BEC_DOMAIN = "bec.baidubce.com"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock -source=types.go

type Interface interface {
	CreateVMService(ctx context.Context, args *CreateVMServiceParams, option *bce.SignOption) (*CreateVMServiceResult, error)
	GetVMService(ctx context.Context, serviceID string, option *bce.SignOption) (*VMService, error)
	ListVMServices(ctx context.Context, args *ListVMServiceParams, option *bce.SignOption) (*ListVMServiceResult, error)
	DeleteVMService(ctx context.Context, serviceID string, option *bce.SignOption) error

	ListVMInstances(ctx context.Context, args *ListVMInstanceParams, option *bce.SignOption) (*ListVMInstanceResult, error)
	GetVMInstance(ctx context.Context, vmID string, option *bce.SignOption) (*VMInstance, error)
	DeleteVMInstance(ctx context.Context, vmID string, option *bce.SignOption) error
	ReinstallVMInstanceOS(ctx context.Context, vmID string, args *ReinstallVMInstanceOSParams, option *bce.SignOption) error
	LabelAsCCEClusterNode(ctx context.Context, vmID, clusterID string, option *bce.SignOption) error
	RemoveLabelOfCCEClusterNode(ctx context.Context, vmID string, option *bce.SignOption) error

	CreateBLB(ctx context.Context, args *CreateBLBParams, option *bce.SignOption) (string, error)
	ListBLBs(ctx context.Context, args *ListBLBParams, option *bce.SignOption) (*ListBLBResult, error)
	GetBLB(ctx context.Context, blbID string, option *bce.SignOption) (*BLB, error)
	CreateBLBListener(ctx context.Context, blbID string, args *CreateBLBListenerParams, option *bce.SignOption) error
	CreateBLBBackend(ctx context.Context, blbID string, args *CreateBLBBackendParams, option *bce.SignOption) error
	GetBLBBackends(ctx context.Context, blbID string, option *bce.SignOption) (*GetBLBBackendsResult, error)
	DeleteBLB(ctx context.Context, blbID string, option *bce.SignOption) error
	DeleteBLBBackends(ctx context.Context, blbID string, args *DeleteBLBBackendsParam, option *bce.SignOption) error
	ListAvailableDeploymentForBLB(ctx context.Context, blbID string, args *ListAvailableDeploymentForBLBParams, option *bce.SignOption) (*ListAvailableDeploymentForBLBResult, error)
}

type client struct {
	*bce.Client
}

func NewClient(config *bce.Config) *client {
	bceClient := bce.NewClient(config)

	client := &client{
		Client: bceClient,
	}
	if client.Endpoint == "" {
		client.Endpoint = "http://" + BEC_DOMAIN
	}
	return client
}
