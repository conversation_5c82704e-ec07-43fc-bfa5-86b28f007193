/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  vm_service
 * @Version: 1.0.0
 * @Date: 2020/10/23 3:33 下午
 */
package bec

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
)

const (
	PaymentMethodPostPsy PaymentMethod = "postpay"
)

const (
	ServiceProviderChinaUnicom  ServiceProvider = "CHINA_UNICOM"
	ServiceProviderChinaMobile  ServiceProvider = "CHINA_MOBILE"
	ServiceProviderChinaTelecom ServiceProvider = "CHINA_TELECOM"
	ServiceProviderTripleLine   ServiceProvider = "TRIPLE_LINE"
)

type PaymentMethod string

type CreateVMServiceParams struct {
	ServiceName     string                   `json:"serviceName"`
	PaymentMethod   PaymentMethod            `json:"paymentMethod"`
	NeedPublicIp    bool                     `json:"needPublicIp"`
	Bandwidth       int                      `json:"bandwidth"`
	DeployInstances []VMInstanceRegionConfig `json:"deployInstances"`

	CPU              int              `json:"cpu"`
	Memory           int              `json:"memory"`
	ImageID          string           `json:"imageId"`
	RootDiskSizeInGb int              `json:"rootDiskSizeInGb"`
	AdminPass        string           `json:"adminPass"`
	DataVolumeList   []VMVolumeConfig `json:"dataVolumeList"`
	DisableIntranet  bool             `json:"disableIntranet"`
	DisableCloudInit bool             `json:"disableCloudInit"`
}

type Region string
type City string
type ServiceProvider string
type VMInstanceRegionConfig struct {
	Region          Region          `json:"region"`
	City            City            `json:"city"`
	ServiceProvider ServiceProvider `json:"serviceProvider"`
	Replicas        int             `json:"replicas"`
}

type DiskType string
type VMVolumeConfig struct {
	Name       string   `json:"name"`
	SizeInGB   int      `json:"sizeInGB"`
	VolumeType DiskType `json:"volumeType"`
	PVCName    string   `json:"pvcName,omitempty"`
}

type CreateVMServiceResult struct {
	Result  bool              `json:"result"`
	Action  string            `json:"action"`
	Details map[string]string `json:"details"`
}

func (c *client) CreateVMService(ctx context.Context, args *CreateVMServiceParams, option *bce.SignOption) (*CreateVMServiceResult, error) {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	url := "/v1/vm/service"
	req, err := bce.NewRequest("POST", c.GetURL(c.Endpoint, url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result CreateVMServiceResult
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

type VMServiceStatus string

const (
	VMServiceStatusRunning     VMServiceStatus = "RUNNING"
	VMServiceStatusStop        VMServiceStatus = "STOP"
	VMServiceStatusPending     VMServiceStatus = "PENDING"
	VMServiceStatusTerminating VMServiceStatus = "TERMINATING"
	VMServiceStatusFailed      VMServiceStatus = "FAILED"
	VMServiceStatusSucceed     VMServiceStatus = "SUCCEEDED"
	VMServiceStatusUnknown     VMServiceStatus = "UNKNOWN"
	VMServiceStatusTerminated  VMServiceStatus = "TERMINATED"
	VMServiceStatusWaiting     VMServiceStatus = "WAITING"
)

type VMService struct {
	ServiceID        string                   `json:"serviceId"`
	ServiceName      string                   `json:"serviceName"`
	Status           VMServiceStatus          `json:"status"`
	TotalCPU         float64                  `json:"totalCpu"`
	TotalMEM         float64                  `json:"totalMem"`
	TotalRootDisk    int                      `json:"totalRootDisk"`
	TotalDisk        int                      `json:"totalDisk"`
	Regions          int                      `json:"regions"`
	DeployInstances  []VMInstanceRegionConfig `json:"deployInstances"`
	TotalInstances   int                      `json:"totalInstances"`
	RunningInstances int                      `json:"runningInstances"`
	OSImage          *VMImageDetail           `json:"osImage"`
	CreateTime       string                   `json:"createTime"`
	Bandwidth        string                   `json:"bandwidth"`
}

func (c *client) GetVMService(ctx context.Context, serviceID string, option *bce.SignOption) (*VMService, error) {
	if serviceID == "" {
		return nil, fmt.Errorf("empty vm service id")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	url := fmt.Sprintf("/v1/vm/service/%s", serviceID)
	req, err := bce.NewRequest("GET", c.GetURL(c.Endpoint, url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result VMService
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

type ListVMServiceParams struct {
	PageNo      int
	PageSize    int
	KeywordType string
	Keyword     string
	Order       string
	OrderBy     string
}

type ListVMServiceResult struct {
	Order      string       `json:"order"`
	OrderBy    string       `json:"orderBy"`
	PageNo     int          `json:"pageNo"`
	PageSize   int          `json:"pageSize"`
	TotalCount int          `json:"totalCount"`
	Result     []*VMService `json:"result"`
}

func (c *client) ListVMServices(ctx context.Context, args *ListVMServiceParams, option *bce.SignOption) (*ListVMServiceResult, error) {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	if args != nil {
		if args.PageNo > 0 {
			params["pageNo"] = strconv.Itoa(args.PageNo)
		}
		if args.PageSize > 0 {
			params["pageSize"] = strconv.Itoa(args.PageSize)
		}
		if args.Keyword != "" {
			params["keyword"] = args.Keyword
		}
		if args.KeywordType != "" {
			params["keywordType"] = args.KeywordType
		}
		if args.Order != "" {
			params["order"] = args.Order
		}
		if args.OrderBy != "" {
			params["orderBy"] = args.OrderBy
		}
	}

	url := "/v1/vm/service"
	req, err := bce.NewRequest("GET", c.GetURL(c.Endpoint, url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result ListVMServiceResult
	err = json.Unmarshal(bodyContent, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

func (c *client) DeleteVMService(ctx context.Context, serviceID string, option *bce.SignOption) error {
	if serviceID == "" {
		return fmt.Errorf("empty service id")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	url := fmt.Sprintf("/v1/vm/service/%s", serviceID)
	req, err := bce.NewRequest("DELETE", c.GetURL(c.Endpoint, url, params), nil)
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}
