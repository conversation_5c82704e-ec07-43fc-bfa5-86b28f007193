// Code generated by MockGen. DO NOT EDIT.
// Source: types.go

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
	bec "icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bec"
	reflect "reflect"
)

// MockInterface is a mock of Interface interface
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// CreateVMService mocks base method
func (m *MockInterface) CreateVMService(ctx context.Context, args *bec.CreateVMServiceParams, option *bce.SignOption) (*bec.CreateVMServiceResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateVMService", ctx, args, option)
	ret0, _ := ret[0].(*bec.CreateVMServiceResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateVMService indicates an expected call of CreateVMService
func (mr *MockInterfaceMockRecorder) CreateVMService(ctx, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateVMService", reflect.TypeOf((*MockInterface)(nil).CreateVMService), ctx, args, option)
}

// GetVMService mocks base method
func (m *MockInterface) GetVMService(ctx context.Context, serviceID string, option *bce.SignOption) (*bec.VMService, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVMService", ctx, serviceID, option)
	ret0, _ := ret[0].(*bec.VMService)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVMService indicates an expected call of GetVMService
func (mr *MockInterfaceMockRecorder) GetVMService(ctx, serviceID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVMService", reflect.TypeOf((*MockInterface)(nil).GetVMService), ctx, serviceID, option)
}

// ListVMServices mocks base method
func (m *MockInterface) ListVMServices(ctx context.Context, args *bec.ListVMServiceParams, option *bce.SignOption) (*bec.ListVMServiceResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListVMServices", ctx, args, option)
	ret0, _ := ret[0].(*bec.ListVMServiceResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListVMServices indicates an expected call of ListVMServices
func (mr *MockInterfaceMockRecorder) ListVMServices(ctx, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListVMServices", reflect.TypeOf((*MockInterface)(nil).ListVMServices), ctx, args, option)
}

// DeleteVMService mocks base method
func (m *MockInterface) DeleteVMService(ctx context.Context, serviceID string, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteVMService", ctx, serviceID, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteVMService indicates an expected call of DeleteVMService
func (mr *MockInterfaceMockRecorder) DeleteVMService(ctx, serviceID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteVMService", reflect.TypeOf((*MockInterface)(nil).DeleteVMService), ctx, serviceID, option)
}

// ListVMInstances mocks base method
func (m *MockInterface) ListVMInstances(ctx context.Context, args *bec.ListVMInstanceParams, option *bce.SignOption) (*bec.ListVMInstanceResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListVMInstances", ctx, args, option)
	ret0, _ := ret[0].(*bec.ListVMInstanceResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListVMInstances indicates an expected call of ListVMInstances
func (mr *MockInterfaceMockRecorder) ListVMInstances(ctx, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListVMInstances", reflect.TypeOf((*MockInterface)(nil).ListVMInstances), ctx, args, option)
}

// GetVMInstance mocks base method
func (m *MockInterface) GetVMInstance(ctx context.Context, vmID string, option *bce.SignOption) (*bec.VMInstance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVMInstance", ctx, vmID, option)
	ret0, _ := ret[0].(*bec.VMInstance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVMInstance indicates an expected call of GetVMInstance
func (mr *MockInterfaceMockRecorder) GetVMInstance(ctx, vmID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVMInstance", reflect.TypeOf((*MockInterface)(nil).GetVMInstance), ctx, vmID, option)
}

// DeleteVMInstance mocks base method
func (m *MockInterface) DeleteVMInstance(ctx context.Context, vmID string, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteVMInstance", ctx, vmID, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteVMInstance indicates an expected call of DeleteVMInstance
func (mr *MockInterfaceMockRecorder) DeleteVMInstance(ctx, vmID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteVMInstance", reflect.TypeOf((*MockInterface)(nil).DeleteVMInstance), ctx, vmID, option)
}

// ReinstallVMInstanceOS mocks base method
func (m *MockInterface) ReinstallVMInstanceOS(ctx context.Context, vmID string, args *bec.ReinstallVMInstanceOSParams, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReinstallVMInstanceOS", ctx, vmID, args, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReinstallVMInstanceOS indicates an expected call of ReinstallVMInstanceOS
func (mr *MockInterfaceMockRecorder) ReinstallVMInstanceOS(ctx, vmID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReinstallVMInstanceOS", reflect.TypeOf((*MockInterface)(nil).ReinstallVMInstanceOS), ctx, vmID, args, option)
}

// LabelAsCCEClusterNode mocks base method
func (m *MockInterface) LabelAsCCEClusterNode(ctx context.Context, vmID, clusterID string, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LabelAsCCEClusterNode", ctx, vmID, clusterID, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// LabelAsCCEClusterNode indicates an expected call of LabelAsCCEClusterNode
func (mr *MockInterfaceMockRecorder) LabelAsCCEClusterNode(ctx, vmID, clusterID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LabelAsCCEClusterNode", reflect.TypeOf((*MockInterface)(nil).LabelAsCCEClusterNode), ctx, vmID, clusterID, option)
}

// RemoveLabelOfCCEClusterNode mocks base method
func (m *MockInterface) RemoveLabelOfCCEClusterNode(ctx context.Context, vmID string, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveLabelOfCCEClusterNode", ctx, vmID, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveLabelOfCCEClusterNode indicates an expected call of RemoveLabelOfCCEClusterNode
func (mr *MockInterfaceMockRecorder) RemoveLabelOfCCEClusterNode(ctx, vmID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveLabelOfCCEClusterNode", reflect.TypeOf((*MockInterface)(nil).RemoveLabelOfCCEClusterNode), ctx, vmID, option)
}

// CreateBLB mocks base method
func (m *MockInterface) CreateBLB(ctx context.Context, args *bec.CreateBLBParams, option *bce.SignOption) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBLB", ctx, args, option)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateBLB indicates an expected call of CreateBLB
func (mr *MockInterfaceMockRecorder) CreateBLB(ctx, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBLB", reflect.TypeOf((*MockInterface)(nil).CreateBLB), ctx, args, option)
}

// ListBLBs mocks base method
func (m *MockInterface) ListBLBs(ctx context.Context, args *bec.ListBLBParams, option *bce.SignOption) (*bec.ListBLBResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListBLBs", ctx, args, option)
	ret0, _ := ret[0].(*bec.ListBLBResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListBLBs indicates an expected call of ListBLBs
func (mr *MockInterfaceMockRecorder) ListBLBs(ctx, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListBLBs", reflect.TypeOf((*MockInterface)(nil).ListBLBs), ctx, args, option)
}

// GetBLB mocks base method
func (m *MockInterface) GetBLB(ctx context.Context, blbID string, option *bce.SignOption) (*bec.BLB, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBLB", ctx, blbID, option)
	ret0, _ := ret[0].(*bec.BLB)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBLB indicates an expected call of GetBLB
func (mr *MockInterfaceMockRecorder) GetBLB(ctx, blbID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBLB", reflect.TypeOf((*MockInterface)(nil).GetBLB), ctx, blbID, option)
}

// CreateBLBListener mocks base method
func (m *MockInterface) CreateBLBListener(ctx context.Context, blbID string, args *bec.CreateBLBListenerParams, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBLBListener", ctx, blbID, args, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateBLBListener indicates an expected call of CreateBLBListener
func (mr *MockInterfaceMockRecorder) CreateBLBListener(ctx, blbID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBLBListener", reflect.TypeOf((*MockInterface)(nil).CreateBLBListener), ctx, blbID, args, option)
}

// CreateBLBBackend mocks base method
func (m *MockInterface) CreateBLBBackend(ctx context.Context, blbID string, args *bec.CreateBLBBackendParams, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBLBBackend", ctx, blbID, args, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateBLBBackend indicates an expected call of CreateBLBBackend
func (mr *MockInterfaceMockRecorder) CreateBLBBackend(ctx, blbID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBLBBackend", reflect.TypeOf((*MockInterface)(nil).CreateBLBBackend), ctx, blbID, args, option)
}

// GetBLBBackends mocks base method
func (m *MockInterface) GetBLBBackends(ctx context.Context, blbID string, option *bce.SignOption) (*bec.GetBLBBackendsResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBLBBackends", ctx, blbID, option)
	ret0, _ := ret[0].(*bec.GetBLBBackendsResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBLBBackends indicates an expected call of GetBLBBackends
func (mr *MockInterfaceMockRecorder) GetBLBBackends(ctx, blbID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBLBBackends", reflect.TypeOf((*MockInterface)(nil).GetBLBBackends), ctx, blbID, option)
}

// DeleteBLB mocks base method
func (m *MockInterface) DeleteBLB(ctx context.Context, blbID string, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteBLB", ctx, blbID, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteBLB indicates an expected call of DeleteBLB
func (mr *MockInterfaceMockRecorder) DeleteBLB(ctx, blbID, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBLB", reflect.TypeOf((*MockInterface)(nil).DeleteBLB), ctx, blbID, option)
}

// DeleteBLBBackends mocks base method
func (m *MockInterface) DeleteBLBBackends(ctx context.Context, blbID string, args *bec.DeleteBLBBackendsParam, option *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteBLBBackends", ctx, blbID, args, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteBLBBackends indicates an expected call of DeleteBLBBackends
func (mr *MockInterfaceMockRecorder) DeleteBLBBackends(ctx, blbID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBLBBackends", reflect.TypeOf((*MockInterface)(nil).DeleteBLBBackends), ctx, blbID, args, option)
}

// ListAvailableDeploymentForBLB mocks base method
func (m *MockInterface) ListAvailableDeploymentForBLB(ctx context.Context, blbID string, args *bec.ListAvailableDeploymentForBLBParams, option *bce.SignOption) (*bec.ListAvailableDeploymentForBLBResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAvailableDeploymentForBLB", ctx, blbID, args, option)
	ret0, _ := ret[0].(*bec.ListAvailableDeploymentForBLBResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAvailableDeploymentForBLB indicates an expected call of ListAvailableDeploymentForBLB
func (mr *MockInterfaceMockRecorder) ListAvailableDeploymentForBLB(ctx, blbID, args, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAvailableDeploymentForBLB", reflect.TypeOf((*MockInterface)(nil).ListAvailableDeploymentForBLB), ctx, blbID, args, option)
}
