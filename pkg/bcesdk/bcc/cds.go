package bcc

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
)

type Volume struct {
	ID             string             `json:"id"`
	Name           string             `json:"name"`
	DiskSizeInGB   int                `json:"diskSizeInGB"`
	PaymentTiming  string             `json:"paymentTiming"`
	CreateTime     string             `json:"createTime"`
	ExpireTime     string             `json:"expireTime"`
	Status         VolumeStatus       `json:"status"`
	VolumeType     VolumeType         `json:"type"`
	StorageType    StorageType        `json:"storageType"`
	Desc           string             `json:"desc"`
	Attachments    []VolumeAttachment `json:"attachments"`
	ZoneName       string             `json:"zoneName"`
	IsSystemVolume bool               `json:"isSystemVolume"`
}

type VolumeStatus string

const (
	VOLUMESTATUS_CREATE             VolumeStatus = "Creating"
	VOLUMESTATUS_AVALIABLE          VolumeStatus = "Available"
	VOLUMESTATUS_ATTACHING          VolumeStatus = "Attaching"
	VOLUMESTATUS_NOTAVALIABLE       VolumeStatus = "NotAvailable"
	VOLUMESTATUS_INUSE              VolumeStatus = "InUse"
	VOLUMESTATUS_DETACHING          VolumeStatus = "Detaching"
	VOLUMESTATUS_DELETING           VolumeStatus = "Deleting"
	VOLUMESTATUS_DELETED            VolumeStatus = "Deleted"
	VOLUMESTATUS_SCALING            VolumeStatus = "Scaling"
	VOLUMESTATUS_EXPIRED            VolumeStatus = "Expired"
	VOLUMESTATUS_ERROR              VolumeStatus = "Error"
	VOLUMESTATUS_SNAPSHOTPROCESSING VolumeStatus = "SnapshotProcessing"
	VOLUMESTATUS_IMAGEPROCESSING    VolumeStatus = "ImageProcessing"
)

// VolumeType - Volume 类型
type VolumeType string

const (
	VOLUME_TYPE_CDS       VolumeType = "Cds"
	VOLUME_TYPE_SYSTEM    VolumeType = "System"
	VOLUME_TYPE_EPHEMERAL VolumeType = "Ephemeral"
)

// StorageType - 存储的类型
type StorageType string

const (
	// StorageTypeSTD1 "上一代云磁盘, sata 盘"
	// std1 上一代磁盘被废弃掉
	StorageTypeSTD1 StorageType = "std1"

	// StorageTypeHP1 "高性能云磁盘, ssd 盘"
	StorageTypeHP1 StorageType = "hp1"

	// StorageTypeCloudHP1 "SSD 云磁盘, premium ssd 盘"
	StorageTypeCloudHP1 StorageType = "cloud_hp1"

	// StorageTypeHDD "普通型"
	StorageTypeHDD StorageType = "hdd"

	// StorageTypeLocal "本地盘"
	StorageTypeLocal StorageType = "local"

	// StorageTypeDCCSATA "sata盘, 创建 DCC 子网实例专用"
	StorageTypeDCCSATA StorageType = "sata"

	// StorageTypeDCCSSD "ssd盘, 创建 DCC 子网实例专用"
	StorageTypeDCCSSD StorageType = "ssd"

	// StorageTypeEnhancedSSD 增强型ssd
	StorageTypeEnhancedSSD = "enhanced_ssd_pl1"
)

// VolumeAttachment define attach info
type VolumeAttachment struct {
	VolumeID   string `json:"volumeId"`
	InstanceID string `json:"instanceId"`
	// mount path
	Device string `json:"device"`
	Serial string `json:"serial"`
}

// CdsPreMountInfo define premount
type CdsPreMountInfo struct {
	MountPath string           `json:"mountPath"`
	CdsConfig []DiskSizeConfig `json:"cdsConfig"`
}

// DiskSizeConfig define distsize config
type DiskSizeConfig struct {
	Size         string `json:"size"`
	SnapshotID   string `json:"snapshotID"`
	SnapshotName string `json:"snapshotName"`
	VolumeType   string `json:"volumeType"`
	StorageType  string `json:"storageType"`
	LogicalZone  string `json:"logicalZone"`
}

type SnapshotStatus string

const (
	Creating      SnapshotStatus = "Creating"
	CreatedFailed SnapshotStatus = "CreatedFailed"
	Available     SnapshotStatus = "Available"
	NotAvailable  SnapshotStatus = "NotAvailable"
)

type Snapshot struct {
	ID           string         `json:"id"`
	Name         string         `json:"name"`
	SizeInGB     int            `json:"sizeInGB"`
	CreateTime   string         `json:"createTime"`
	Status       SnapshotStatus `json:"status"`
	CreateMethod string         `json:"createMethod"`
	VolumeID     string         `json:"volumeId"`
	Desc         string         `json:"desc"`
}

// DeleteVolume Delete a volume
func (c *Client) DeleteVolume(ctx context.Context, volumeId string, option *bce.SignOption) error {
	if volumeId == "" {
		return fmt.Errorf("DeleteVolume need a id")
	}
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	req, err := bce.NewRequest("DELETE", c.GetURL("v2/volume"+"/"+volumeId, params), nil)
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}

// API document: https://cloud.baidu.com/doc/BCC/s/Ujwvyo1ta
const maxVolumeNameLength = 65

type CreateVolumeArgs struct {
	PurchaseCount int         `json:"purchaseCount,omitempty"`
	CdsSizeInGB   int         `json:"cdsSizeInGB"`
	StorageType   StorageType `json:"storageType"`
	Billing       *Billing    `json:"billing"`
	SnapshotID    string      `json:"snapshotId,omitempty"`
	ZoneName      string      `json:"zoneName,omitempty"`
	Name          string      `json:"Name,omitempty"`
}

type CreateVolumeResponse struct {
	VolumeIDs []string `json:"volumeIds,omitempty"`
}

func (args *CreateVolumeArgs) validate() error {
	if args == nil {
		return fmt.Errorf("CreateVolumeArgs need args")
	}
	if args.StorageType == "" {
		return fmt.Errorf("CreateVolumeArgs need StorageType")
	}
	if args.Billing == nil {
		return fmt.Errorf("CreateVolumeArgs need Billing")
	}
	if args.CdsSizeInGB == 0 {
		return fmt.Errorf("CreateVolumeArgs need CdsSizeInGB")
	}
	if len(args.Name) > maxVolumeNameLength {
		return fmt.Errorf("length of volume Name should <= %d", maxVolumeNameLength)
	}
	return nil
}

// CreateVolumes create a volume
func (c *Client) CreateVolumes(ctx context.Context, args *CreateVolumeArgs, option *bce.SignOption) ([]string, error) {
	err := args.validate()
	if err != nil {
		return nil, err
	}
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}
	req, err := bce.NewRequest("POST", c.GetURL("v2/volume", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()

	if err != nil {
		return nil, err
	}
	var blbsResp *CreateVolumeResponse
	err = json.Unmarshal(bodyContent, &blbsResp)

	if err != nil {
		return nil, err
	}
	return blbsResp.VolumeIDs, nil

}

type ListVolumesResponse struct {
	Volumes     []Volume `json:"volumes"`
	Marker      string   `json:"marker"`
	IsTruncated bool     `json:"isTruncated"`
	NextMarker  string   `json:"nextMarker"`
	MaxKeys     int      `json:"maxKeys"`
}

// GetVolumeList get all volumes
func (c *Client) ListVolumes(ctx context.Context, instanceID, zoneName string, option *bce.SignOption) (*ListVolumesResponse, error) {

	params := make(map[string]string)
	if instanceID != "" {
		params["instanceId"] = instanceID
	}
	if zoneName != "" {
		params["zoneName"] = zoneName
	}
	req, err := bce.NewRequest("GET", c.GetURL("v2/volume", params), nil)
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)
	bodyContent, err := resp.GetBodyContent()

	if err != nil {
		return nil, err
	}
	var blbsResp *ListVolumesResponse
	err = json.Unmarshal(bodyContent, &blbsResp)

	if err != nil {
		return nil, err
	}
	return blbsResp, nil
}

type DescribeVolumeResponse struct {
	Volume *Volume `json:"volume"`
}

// DescribeVolume describe a volume
// More info see https://cloud.baidu.com/doc/BCC/API.html#.E6.9F.A5.E8.AF.A2.E7.A3.81.E7.9B.98.E8.AF.A6.E6.83.85
func (c *Client) DescribeVolume(ctx context.Context, id string, option *bce.SignOption) (*Volume, error) {
	if id == "" {
		return nil, fmt.Errorf("DescribeVolume need a id")
	}
	req, err := bce.NewRequest("GET", c.GetURL("v2/volume"+"/"+id, nil), nil)
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	var ins DescribeVolumeResponse
	err = json.Unmarshal(bodyContent, &ins)

	if err != nil {
		return nil, err
	}
	return ins.Volume, nil
}

// AttachCDSVolumeArgs describe attachcds args
type AttachCDSVolumeArgs struct {
	VolumeId   string `json:"-"`
	InstanceId string `json:"instanceId"`
}
type AttachCDSVolumeResponse struct {
	VolumeAttachment *VolumeAttachment `json:"volumeAttachment"`
}

func (args *AttachCDSVolumeArgs) validate() error {
	if args == nil {
		return fmt.Errorf("AttachCDSVolumeArgs need args")
	}
	if args.VolumeId == "" {
		return fmt.Errorf("AttachCDSVolumeArgs need VolumeId")
	}
	if args.InstanceId == "" {
		return fmt.Errorf("AttachCDSVolumeArgs need InstanceId")
	}
	return nil
}

// AttachCDSVolume attach a cds to vm
func (c *Client) AttachCDSVolume(ctx context.Context, args *AttachCDSVolumeArgs, option *bce.SignOption) (*VolumeAttachment, error) {
	err := args.validate()
	if err != nil {
		return nil, err
	}
	params := map[string]string{
		"attach":      "",
		"clientToken": c.GenerateClientToken(),
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}
	req, err := bce.NewRequest("PUT", c.GetURL("v2/volume"+"/"+args.VolumeId, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	var blbsResp AttachCDSVolumeResponse
	err = json.Unmarshal(bodyContent, &blbsResp)

	if err != nil {
		return nil, err
	}
	return blbsResp.VolumeAttachment, nil
}

// DetachCDSVolume detach a cds
// TODO: if a volume is detaching, need to wait
func (c *Client) DetachCDSVolume(ctx context.Context, args *AttachCDSVolumeArgs, option *bce.SignOption) error {
	err := args.validate()
	if err != nil {
		return err
	}
	params := map[string]string{
		"detach":      "",
		"clientToken": c.GenerateClientToken(),
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}
	req, err := bce.NewRequest("PUT", c.GetURL("v2/volume"+"/"+args.VolumeId, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}

// AttachCDSVolumeArgs describe attachcds args
type CreateSnapShotArgs struct {
	VolumeId     string `json:"volumeId"`
	SnapshotName string `json:"snapshotName"`
	Desc         string `json:"desc"`
}

type CreateSnapShotResponse struct {
	SnapshotId string `json:"snapshotId"`
}

func (c *Client) CreateSnapshot(ctx context.Context, args *CreateSnapShotArgs, option *bce.SignOption) (string, error) {
	params := map[string]string{
		"volumeId":     args.VolumeId,
		"snapshotName": args.SnapshotName,
		"desc":         args.Desc,
		"clientToken":  c.GenerateClientToken(),
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return "", err
	}
	req, err := bce.NewRequest("POST", c.GetURL("v2/snapshot", params), bytes.NewBuffer(postContent))
	if err != nil {
		return "", err
	}
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return "", err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return "", err
	}
	var ssresp CreateSnapShotResponse
	err = json.Unmarshal(bodyContent, &ssresp)

	if err != nil {
		return "", err
	}
	return ssresp.SnapshotId, nil
}

func (c *Client) DeleteSnapshot(ctx context.Context, snapShotId string, option *bce.SignOption) error {

	if snapShotId == "" {
		return fmt.Errorf("DeleteSnapShot need a id")
	}
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	req, err := bce.NewRequest("DELETE", c.GetURL("v2/snapshot/"+snapShotId, params), nil)
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}

type DescribeSnapshotResponse struct {
	Snapshot *Snapshot `json:"snapshot"`
}

func (c *Client) DescribeSnapshot(ctx context.Context, id string, option *bce.SignOption) (*Snapshot, error) {
	if id == "" {
		return nil, fmt.Errorf("DescribeSnapShot need a id")
	}
	req, err := bce.NewRequest("GET", c.GetURL("v2/snapshot"+"/"+id, nil), nil)
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	var dsr DescribeSnapshotResponse
	err = json.Unmarshal(bodyContent, &dsr)

	if err != nil {
		return nil, err
	}
	return dsr.Snapshot, nil
}

type GetSnapshotListResponse struct {
	Snapshots   []Snapshot `json:"snapshots"`
	Marker      string     `json:"marker"`
	IsTruncated bool       `json:"isTruncated"`
	NextMarker  string     `json:"nextMarker"`
	MaxKeys     int        `json:"maxKeys"`
}

// GetVolumeList get all volumes
func (c *Client) GetSnapshotList(ctx context.Context, volumeId string, option *bce.SignOption) ([]Snapshot, error) {

	params := make(map[string]string)
	if volumeId != "" {
		params["volumeId"] = volumeId
	}
	req, err := bce.NewRequest("GET", c.GetURL("v2/snapshot", params), nil)
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)
	bodyContent, err := resp.GetBodyContent()

	if err != nil {
		return nil, err
	}
	var sslResp GetSnapshotListResponse
	err = json.Unmarshal(bodyContent, &sslResp)

	if err != nil {
		return nil, err
	}
	return sslResp.Snapshots, nil
}

// DeleteCDS delete a cds
func (c *Client) DeleteCDS(ctx context.Context, volumeID string) error {
	if volumeID == "" {
		return fmt.Errorf("DeleteCDS need a volumeId")
	}
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	req, err := bce.NewRequest("DELETE", c.GetURL("v2/volume"+"/"+volumeID, params), nil)
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, nil)
	if err != nil {
		return err
	}
	return nil
}

// RollbackVolume rollback a volume
// TODO
func (c *Client) RollbackVolume() {

}

// PurchaseReservedVolume purchaseReserved a volume
// TODO
func (c *Client) PurchaseReservedVolume() {

}
