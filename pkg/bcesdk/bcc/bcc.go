package bcc

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	bccapi "github.com/baidubce/bce-sdk-go/services/bcc/api"
	"k8s.io/apimachinery/pkg/util/intstr"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/tag"
)

// Instance OpenBCC instance 详情
type Instance struct {
	Spec         string         `json:"spec"`
	InstanceID   string         `json:"id"`
	InstanceName string         `json:"name"`
	Hostname     string         `json:"hostname"`
	InstanceType InstanceType   `json:"instanceType"`
	Description  string         `json:"desc"`
	Status       InstanceStatus `json:"status"`

	CPUCount           int                `json:"cpuCount"`
	GPUType            GPUType            `json:"gpuCard"`
	FPGAType           FPGAType           `json:"fpgaCard"`
	CardCount          intstr.IntOrString `json:"cardCount"`
	MemoryCapacityInGB int                `json:"memoryCapacityInGB"`
	LocalDiskSizeInGB  int                `json:"localDiskSizeInGB"`

	VPCID                 string `json:"vpcId"`
	ZoneName              string `json:"zoneName"`
	SubnetID              string `json:"subnetId"`
	InternalIP            string `json:"internalIp"`
	PublicIP              string `json:"publicIp"`
	NetworkCapacityInMbps int    `json:"networkCapacityInMbps"`

	PaymentTiming PaymentTiming `json:"paymentTiming"`
	AutoRenew     bool          `json:"autoRenew"`
	KeyPairID     string        `json:"keypairId"`
	KeyPairName   string        `json:"keypairName"`
	ImageID       string        `json:"imageId"`
	Tags          []tag.Tag     `json:"tags"`

	// PlacementPolicy 实例放置策略
	PlacementPolicy PlacementPolicy `json:"placementPolicy"`
	// DedicatedHostID 专属服务器 ID
	DedicatedHostID string `json:"dedicatedHostId"`
	// RackID 机架 ID，非公开，仅查询传入 isDeploySet=true 时展示
	RackID string `json:"rackId"`
	// HostID 宿主机 ID，非公开，仅查询传入 isDeploySet=true 时展示
	HostID string `json:"hostId"`
	// SwitchID 交换机 ID，非公开，仅查询传入 isDeploySet=true 时展示
	SwitchID string `json:"switchId"`

	CreateTime string `json:"createTime"`
	ExpireTime string `json:"expireTime"`
}

// InstanceType instance 类型
type InstanceType string

const (
	// InstanceTypeN1 普通型 BCC 实例: 通用型g1、计算型c1、密集计算型ic1、内存型m1
	InstanceTypeN1 InstanceType = "N1"

	// InstanceTypeN2 普通型II BCC 实例: 通用型g2、计算型c2、密集计算型ic2、内存型m2
	InstanceTypeN2 InstanceType = "N2"

	// InstanceTypeN3 普通型Ⅲ BCC 实例: 通用型g3、计算型c3、密集计算型ic3、内存型m3
	InstanceTypeN3 InstanceType = "N3"

	// InstanceTypeN4 网络增强型 BCC 实例: 通用网络增强型g3ne、计算网络增强型c3ne、内存网络增强型m3ne
	InstanceTypeN4 InstanceType = "N4"

	// InstanceTypeN5 普通型Ⅳ BCC实例: 通用型g4、密集计算型ic4、计算型c4、内存型m4
	InstanceTypeN5 InstanceType = "N5"

	InstanceTypeN6 InstanceType = "N6"

	// InstanceTypeC1 计算优化型实例: 高主频计算型hcc1、高主频通用型hcg1
	InstanceTypeC1 InstanceType = "C1"

	// InstanceTypeC2 计算优化 Ⅱ 型实例: 高主频计算型hcc2、高主频通用型hcg2
	InstanceTypeC2 InstanceType = "C2"

	// InstanceTypeS1 存储优化型实例: 本地SSD型l1
	InstanceTypeS1 InstanceType = "S1"

	InstanceTypeS2 InstanceType = "S2"

	// InstanceTypeG1 GPU 型实例
	InstanceTypeG1 InstanceType = "G1"

	// InstanceTypeF1 FPGA 型实例
	InstanceTypeF1 InstanceType = "F1"

	InstanceTypeVG1 InstanceType = "VG1"

	InstanceTypeA1 InstanceType = "A1"

	InstanceTypeARM4 InstanceType = "ARM4"

	InstanceTypeD1S InstanceType = "D1S"

	InstanceTypeL3 InstanceType = "L3"

	InstanceTypeAEP InstanceType = "AEP"

	InstanceTypeB1 InstanceType = "B1"

	InstanceTypeH1 InstanceType = "H1"

	InstanceTypeR1 InstanceType = "R1"

	InstanceTypeSHAHRES1 InstanceType = "SHAHRES1"

	// TODO: 以下为 CCE 自行定义
	// InstanceTypeDCC DCC 类型
	InstanceTypeDCC InstanceType = "DCC"

	// InstanceTypeBBC BBC 类型
	InstanceTypeBBC InstanceType = "BBC"

	// InstanceTypeBBCGPU BBC GPU 类型
	InstanceTypeBBCGPU InstanceType = "BBC_GPU"

	// Serverless集群master类型
	InstanceTypeServerlessMaster InstanceType = "ServerlessMaster"

	// Edge集群BEC类型
	InstanceTypeBEC InstanceType = "BEC"
)

// InstanceStatus Instance 运行状态
type InstanceStatus string

const (
	InstanceStatusRunning            InstanceStatus = "Running"
	InstanceStatusStarting           InstanceStatus = "Starting"
	InstanceStatusStopping           InstanceStatus = "Stopping"
	InstanceStatusStopped            InstanceStatus = "Stopped"
	InstanceStatusDeleted            InstanceStatus = "Deleted"
	InstanceStatusScaling            InstanceStatus = "Scaling"
	InstanceStatusExpired            InstanceStatus = "Expired"
	InstanceStatusError              InstanceStatus = "Error"
	InstanceStatusSnapshotProcessing InstanceStatus = "SnapshotProcessing"
	InstanceStatusImageProcessing    InstanceStatus = "ImageProcessing"
)

// GPUType GPU 类型
type GPUType string

const (
	// GPUTypeV100_32 NVIDIA Tesla V100-32G
	GPUTypeV100_32 GPUType = "V100-32"
	// GPUTypeV100_16 NVIDIA Tesla V100-16G
	GPUTypeV100_16 GPUType = "V100-16"
	// GPUTypeP40 P40 NVIDIA Tesla P40
	GPUTypeP40 GPUType = "P40"
	// GPUTypeP4 P4 NVIDIA Tesla P4
	GPUTypeP4 GPUType = "P4"
	// GPUTypeK40 K40 NVIDIA Tesla K40
	GPUTypeK40 GPUType = "K40"
	// GPUTypeDLCard NVIDIA 深度学习开发卡
	GPUTypeDLCard GPUType = "DLCard"
)

// FPGAType FPGA 类型
type FPGAType string

const (
	FPGATypeKU115 FPGAType = "KU115"
)

// PaymentTiming 付费时间选择
type PaymentTiming string

const (
	// PaymentTimingPrepaid 预付费
	PaymentTimingPrepaid PaymentTiming = "Prepaid"
	// PaymentTimingPostpaid 后付费
	PaymentTimingPostpaid PaymentTiming = "Postpaid"
	// PaymentTimingBid
	PaymentTimingBid PaymentTiming = "bid"
)

// PlacementPolicy 实例放置策略
type PlacementPolicy string

const (
	PlacementPolicyDefault       PlacementPolicy = "default"
	PlacementPolicyDedicatedHost PlacementPolicy = "dedicatedHost"
)

type ListInstancesResponse struct {
	Marker      string     `json:"marker"`
	IsTruncated bool       `json:"isTruncated"`
	NextMarker  string     `json:"nextMarker"`
	MaxKeys     int        `json:"maxKeys"`
	Instances   []Instance `json:"instances"`
}

type GetInstanceResponse struct {
	Ins Instance `json:"instance"`
}

func (c *Client) CreateInstance(ctx context.Context, args *bccapi.CreateInstanceArgs, option *bce.SignOption) (*bccapi.CreateInstanceResult, error) {
	if args == nil {
		return nil, fmt.Errorf("args is nil")
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("v2/instance", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	result := new(bccapi.CreateInstanceResult)
	err = json.Unmarshal(bodyContent, result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (c Client) CreateInstanceBySpec(ctx context.Context, args *CreateInstanceBySpecArgsShell, clientToken string, option *bce.SignOption) (*bccapi.CreateInstanceBySpecResult, error) {
	if args == nil {
		return nil, fmt.Errorf("args is nil")
	}

	params := map[string]string{
		"clientToken": clientToken,
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("v2/instanceBySpec", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	result := new(bccapi.CreateInstanceBySpecResult)
	err = json.Unmarshal(bodyContent, result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (c *Client) CreateBidInstance(ctx context.Context, args *bccapi.CreateInstanceArgs, clientToken string, option *bce.SignOption) (*bccapi.CreateInstanceResult, error) {
	if args == nil {
		return nil, fmt.Errorf("args is nil")
	}

	params := map[string]string{
		"clientToken": clientToken,
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("v2/instance/bid", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	result := new(bccapi.CreateInstanceResult)
	err = json.Unmarshal(bodyContent, result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

// ListInstances gets all Instances.
func (c *Client) ListInstances(ctx context.Context, option *bce.SignOption) ([]Instance, error) {
	req, err := bce.NewRequest("GET", c.GetURL("v2/instance", nil), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)

	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()

	if err != nil {
		return nil, err
	}

	var insList *ListInstancesResponse
	err = json.Unmarshal(bodyContent, &insList)

	if err != nil {
		return nil, err
	}

	return insList.Instances, nil
}

// DescribeInstance describe a instance
func (c *Client) DescribeInstance(ctx context.Context, instanceID string, option *bce.SignOption) (*Instance, error) {
	return c.DescribeInstanceWithDeploySet(ctx, instanceID, false, option)
}

func (c *Client) DescribeInstanceWithDeploySet(ctx context.Context, instanceID string, isDeploySet bool, option *bce.SignOption) (*Instance, error) {
	params := map[string]string{}
	if isDeploySet {
		params = map[string]string{"isDeploySet": "true"}
	}
	req, err := bce.NewRequest("GET", c.GetURL("v2/instance"+"/"+instanceID, params), nil)

	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)

	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()

	if err != nil {
		return nil, err
	}

	var ins GetInstanceResponse
	err = json.Unmarshal(bodyContent, &ins)

	if err != nil {
		return nil, err
	}

	return &ins.Ins, nil
}

func (c *Client) ListInstancesByIP(ctx context.Context, internalIP string, option *bce.SignOption) (*ListInstancesResponse, error) {
	params := make(map[string]string)
	params["internalIp"] = internalIP

	req, err := bce.NewRequest("GET", c.GetURL("v2/instance", params), nil)

	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)

	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()

	if err != nil {
		return nil, err
	}

	var insList ListInstancesResponse
	err = json.Unmarshal(bodyContent, &insList)

	if err != nil {
		return nil, err
	}

	return &insList, nil
}

func (c *Client) DeleteInstance(ctx context.Context, instanceID string, option *bce.SignOption) error {
	if instanceID == "" {
		return fmt.Errorf("instanceID is empty")
	}

	url := fmt.Sprintf("v2/instance/%s", instanceID)

	req, err := bce.NewRequest("DELETE", c.GetURL(url, nil), nil)
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	if err != nil {
		return err
	}

	return nil
}

// DeleteInstanceWithArgs - 删除实例
func (c *Client) DeleteInstanceWithArgs(ctx context.Context, instanceID string, args *DeleteInstanceArgs, option *bce.SignOption) error {
	if instanceID == "" {
		return fmt.Errorf("instanceID is empty")
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("v2/instance/%s", instanceID)

	req, err := bce.NewRequest("POST", c.GetURL(url, nil), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	if err != nil {
		return err
	}

	return nil
}

func (c *Client) BatchDeleteInstance(ctx context.Context, args *BatchDeleteInstanceArgs, option *bce.SignOption) error {
	if args == nil {
		return fmt.Errorf("args is nil")
	}

	if len(args.InstanceIDs) == 0 {
		return fmt.Errorf("instanceIds is empty")
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("v2/instance/batchDelete")

	req, err := bce.NewRequest("POST", c.GetURL(url, nil), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	if err != nil {
		return err
	}

	return nil
}
