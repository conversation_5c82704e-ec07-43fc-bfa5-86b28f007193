package bcc

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
)

var sgclient Interface

func init() {
	//accessKeyID     := "5fded23b03594981872fbfadaad70ef6"
	//secretAccessKey := "6b109261727a4fee8fc5db4bf5ec6c51"

	accessKeyID := "e0c68be8495540f280b3e9ef03ec25d2"
	secretAccessKey := "b599d5f4f30e41bcb0e1482c9de65bd6"

	sgclient = NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Endpoint:    Endpoint["bj"],
	})
	sgclient.SetDebug(true)
}

func testGetSecurityGroups(t *testing.T) {
	req := &GetSecurityGroupsRequest{
		VPCID:      "vpc-197ed081gw8e",
		InstanceID: "",
		Marker:     "",
		MaxKeys:    1000,
	}
	_, err := sgclient.GetSecurityGroups(context.Background(), req, NewSignOption())
	if err != nil {
		t.Errorf("GetSecurityGroups failed: %v", err)
	}
}

func testCreateSecurityGroup(t *testing.T) {
	req := &CreateSecurityGroupRequest{
		VPCID:       "vpc-yra96qvnc9xm",
		Name:        "test-name",
		Description: "test create security group",
		SGRules: []SecurityGroupRule{
			{
				Remark:        "test rule",
				Direction:     "ingress",
				EtherType:     "IPv4",
				PortRange:     "1-65535",
				Protocol:      "all",
				SourceGroupID: "",
				SourceIP:      "10.0.0.0/8",
				DestGroupID:   "",
				DestIP:        "",
			},
		},
	}

	resp, err := sgclient.CreateSecurityGroup(context.Background(), req, NewSignOption())
	if err != nil {
		t.Errorf("CreateSecurityGroup failed: %v", err)
	}

	if respByte, err := json.Marshal(resp); err == nil {
		t.Logf("CreateSecurityGroup succeeded: %s", string(respByte))
	}
}

func testDeleteSecurityGroup(t *testing.T) {
	securityGroupID := "g-r4yy2det70z7"

	err := sgclient.DeleteSecurityGroup(context.Background(), securityGroupID, NewSignOption())
	if err != nil {
		t.Errorf("DeleteSecurityGroup %s failed: %v", securityGroupID, err)
		return
	}
	t.Logf("DeleteSecurityGroup %s succeeded", securityGroupID)
}

func testCreateSecurityGroupRule(t *testing.T) {
	req := &CreateSecurityGroupRuleRequest{
		SecurityGroupID: "g-miywhjer9a1b",
		SGRule: SecurityGroupRule{
			Remark:        "test add rule",
			Direction:     "ingress",
			EtherType:     "IPv4",
			PortRange:     "1-65535",
			Protocol:      "all",
			SourceGroupID: "",
			SourceIP:      "10.0.0.0/8",
			DestGroupID:   "",
			DestIP:        "",
		},
	}

	err := sgclient.CreateSecurityGroupRule(context.Background(), req, NewSignOption())
	if err != nil {
		t.Errorf("CreateSecurityGroupRule %s failed: %v", req.SecurityGroupID, err)
		return
	}
	t.Logf("CreateSecurityGroupRule %s succeeded", req.SecurityGroupID)
}

func testBindSecurityGroup(t *testing.T) {
	instanceID := "67423efa-ea60-4673-a07a-9c11a0ff7d15"
	sgID := "g-1y3a6t1y6irg"
	err := sgclient.BindSecurityGroup(context.Background(), instanceID, sgID, NewSignOption())
	if err != nil {
		t.Errorf("BindSecurityGroup failed: %v", err)
		return
	}
	t.Logf("BindSecurityGroup succeeded")
}

func testUnbindSecurityGroup(t *testing.T) {
	instanceID := "67423efa-ea60-4673-a07a-9c11a0ff7d15"
	sgID := "g-1y3a6t1y6irg"
	err := sgclient.UnbindSecurityGroup(context.Background(), instanceID, sgID, NewSignOption())
	if err != nil {
		t.Errorf("BindSecurityGroup failed: %v", err)
		return
	}
	t.Logf("BindSecurityGroup succeeded")
}
