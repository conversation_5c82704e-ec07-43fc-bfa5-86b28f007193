package bcc

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	uuid "github.com/satori/go.uuid"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
)

const (
	defaultMaxKeys = 1000 // 请求中默认每页包含的最大数量
)

// GetSecurityGroups 根据 VPC/InstanceID 查询安全组
func (c *Client) GetSecurityGroups(ctx context.Context, request *GetSecurityGroupsRequest, option *bce.SignOption) (*GetSecurityGroupsResponse, error) {
	if request == nil {
		return nil, fmt.Errorf("request is nil")
	}

	if request.MaxKeys <= 0 {
		request.MaxKeys = defaultMaxKeys
	}

	params := map[string]string{
		"maxKeys": strconv.FormatInt(request.MaxKeys, 10),
	}

	if request.Marker != "" {
		params["marker"] = request.Marker
	}

	if request.InstanceID != "" {
		params["instanceId"] = request.InstanceID
	}

	if request.VPCID != "" {
		params["vpcId"] = request.VPCID
	}

	req, err := bce.NewRequest("GET", c.GetURL("v2/securityGroup", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	getResp := new(GetSecurityGroupsResponse)
	err = json.Unmarshal(removeInvalidChar(bodyContent), getResp)
	if err != nil {
		return nil, err
	}

	return getResp, nil
}

// CreateSecurityGroup 创建安全组
func (c *Client) CreateSecurityGroup(ctx context.Context, request *CreateSecurityGroupRequest, option *bce.SignOption) (*CreateSecurityGroupResponse, error) {
	if request == nil {
		return nil, fmt.Errorf("request is nil")
	}

	if request.VPCID == "" {
		return nil, fmt.Errorf("request.VPCID is empty")
	}

	if request.Name == "" {
		return nil, fmt.Errorf("request.Name is empty")
	}

	if len(request.SGRules) <= 0 {
		return nil, fmt.Errorf("request.SGRules length not greater than 0")
	}

	str, _ := uuid.NewV4()
	params := map[string]string{
		"clientToken": str.String(),
	}

	postContent, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("v2/securityGroup", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	createResp := new(CreateSecurityGroupResponse)
	err = json.Unmarshal(bodyContent, createResp)
	if err != nil {
		return nil, err
	}

	return createResp, nil
}

// DeleteSecurityGroup 删除安全组
func (c *Client) DeleteSecurityGroup(ctx context.Context, securityGroupID string, option *bce.SignOption) error {
	if securityGroupID == "" {
		return fmt.Errorf("securityGroupID is empty")
	}

	params := map[string]string{}

	req, err := bce.NewRequest("DELETE", c.GetURL("v2/securityGroup/"+securityGroupID, params), nil)
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

func (c *Client) CreateSecurityGroupRule(ctx context.Context, request *CreateSecurityGroupRuleRequest, option *bce.SignOption) error {
	if request == nil {
		return fmt.Errorf("request is nil")
	}

	if request.SecurityGroupID == "" {
		return fmt.Errorf("request.SecurityGroupID is empty")
	}

	str, _ := uuid.NewV4()
	params := map[string]string{
		"authorizeRule": "",
		"clientToken":   str.String(),
	}

	postContent, err := json.Marshal(request)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("PUT", c.GetURL("v2/securityGroup/"+request.SecurityGroupID, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

func (c *Client) BindSecurityGroup(ctx context.Context, instanceID, securityGroupID string, option *bce.SignOption) error {
	if instanceID == "" || securityGroupID == "" {
		return fmt.Errorf("instanceID or securityGroupID is empty")
	}

	params := map[string]string{
		"bind": "",
	}

	body := struct {
		SecurityGroupId string `json:"securityGroupId"`
	}{
		SecurityGroupId: securityGroupID,
	}
	postContent, err := json.Marshal(body)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("PUT", c.GetURL("v2/instance/"+instanceID, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

func (c *Client) UnbindSecurityGroup(ctx context.Context, instanceID, securityGroupID string, option *bce.SignOption) error {
	if instanceID == "" || securityGroupID == "" {
		return fmt.Errorf("instanceID or securityGroupID is empty")
	}

	params := map[string]string{
		"unbind": "",
	}

	body := struct {
		SecurityGroupId string `json:"securityGroupId"`
	}{
		SecurityGroupId: securityGroupID,
	}
	postContent, err := json.Marshal(body)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("PUT", c.GetURL("v2/instance/"+instanceID, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}
