package bcc

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	bccapi "github.com/baidubce/bce-sdk-go/services/bcc/api"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/sts"
)

var bccclient *Client

func init() {
	//accessKeyID     := "5fded23b03594981872fbfadaad70ef6"
	//secretAccessKey := "6b109261727a4fee8fc5db4bf5ec6c51"

	// JpaaS 账号
	// accessKeyID := "e0c68be8495540f280b3e9ef03ec25d2"
	// secretAccessKey := "b599d5f4f30e41bcb0e1482c9de65bd6"

	accessKeyID := "5133eb40ed884ba1b2e1679a05bfc036"
	secretAccessKey := "17cb19f68d40427996466932f8081690"

	// accessKeyID := "42a03f5800a311eca2c4b7065ee2ff9b"
	// secretAccessKey := "ce4e296ae2be4afc83e1a9b2328efea7"

	bccclient = NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      "bj",
		Endpoint:    Endpoint["bj"],
	})
	bccclient.SetDebug(true)
}

func testGetInstance(t *testing.T) {
	bccclient.SetDebug(true)

	resp, err := bccclient.DescribeInstance(context.Background(), "i-IrEYrJv4", NewSignOption())
	if err != nil {
		t.Errorf("DescribeInstance failed: %v", err)
	}

	if respByte, err := json.Marshal(resp); err == nil {
		t.Logf("DescribeInstance succeeded: %s", string(respByte))
	}
}

func testCreateInstanceBySpec(t *testing.T) {
	bccclient.SetDebug(true)

	ctx := context.TODO()

	encrpyPassword, err := bccapi.Aes128EncryptUseSecreteKey("17cb19f68d404279", "xhsjhkj4%w@oip")
	if err != nil {
		t.Errorf("Aes128EncryptUseSecreteKey failed: %s", err)
	}

	createInstanceArgs := &CreateInstanceBySpecArgsShell{
		CreateInstanceBySpecArgs: bccapi.CreateInstanceBySpecArgs{
			Spec:     "bcc.ic5.c2m2",
			ZoneName: "zoneD",
			ImageId:  "m-wQ1Ppoh5",
			Billing: bccapi.Billing{
				PaymentTiming: bccapi.PaymentTimingPostPaid,
			},

			RootDiskSizeInGb:    100,
			RootDiskStorageType: bccapi.StorageTypeSSD,

			CreateCdsList: []bccapi.CreateCdsModel{
				{
					CdsSizeInGB: 100,
					StorageType: bccapi.StorageTypeSSD,
				},
			},

			PurchaseCount: 1,

			Name:     "chenhuan-openapi",
			Hostname: "chenhuan-openapi",

			AdminPass: encrpyPassword,

			SubnetId:        "sbn-3m8ykzax8s8z",
			SecurityGroupId: "g-850nnihi65yv",

			ClientToken: "client-token-uniq",

			// eip
			InternetChargeType:    "TRAFFIC_POSTPAID_BY_HOUR",
			NetWorkCapacityInMbps: 100,
		},
		EnterpriseSecurityGroupID: "esg-qbii6qijaxc9",
	}

	resp, err := bccclient.CreateInstanceBySpec(ctx, createInstanceArgs, "cce-pbp13g1z-y5gg11nc-cce-dddd-dddff", NewSignOption())
	if err != nil {
		t.Errorf("CreateInstance failed: %v", err)
	}

	if respByte, err := json.Marshal(resp); err == nil {
		t.Logf("CreateInstance succeeded: %s", string(respByte))
	}
}

func testCreateInstance(t *testing.T) {
	bccclient.SetDebug(true)

	ctx := context.TODO()

	encrpyPassword, err := bccapi.Aes128EncryptUseSecreteKey("17cb19f68d404279", "xhsjhkj4%w@oip")
	if err != nil {
		t.Errorf("Aes128EncryptUseSecreteKey failed: %s", err)
	}

	createInstanceArgs := &bccapi.CreateInstanceArgs{
		ImageId: "m-AUk5rsP3",
		Billing: bccapi.Billing{
			PaymentTiming: bccapi.PaymentTimingPostPaid,
		},
		InstanceType: bccapi.InstanceTypeN3,

		CpuCount:           2,
		MemoryCapacityInGB: 8,

		RootDiskSizeInGb:    100,
		RootDiskStorageType: bccapi.StorageTypeSSD,

		CreateCdsList: []bccapi.CreateCdsModel{
			{
				CdsSizeInGB: 100,
				StorageType: bccapi.StorageTypeSSD,
			},
		},

		PurchaseCount: 1,

		Name:     "chenhuan-openapi",
		Hostname: "chenhuan-openapi",

		AdminPass: encrpyPassword,

		SubnetId:        "sbn-f0ewipic2zh4",
		SecurityGroupId: "g-850nnihi65yv",

		ClientToken: "client-token-uniq",
	}

	resp, err := bccclient.CreateInstance(ctx, createInstanceArgs, nil)
	if err != nil {
		t.Errorf("CreateInstance failed: %v", err)
	}

	if respByte, err := json.Marshal(resp); err == nil {
		t.Logf("CreateInstance succeeded: %s", string(respByte))
	}
}

// Not work
func testCreateInstanceBySignOptioT(t *testing.T) {
	ctx := context.TODO()

	// 初始化 sts client
	stsclient := sts.NewClient(ctx, &bce.Config{
		Endpoint: "sts.gz.iam.sdns.baidu.com:8586/v1",
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   "gz",
	}, &bce.Config{
		Endpoint: "iam.gz.bce-internal.baidu.com/v3",
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   "gz",
	}, "BceServiceRole_SERVICE_CCE", "cce", "c8QBqhOKxmGu0Sl2p13guMil3U2grbYt")

	signOption := stsclient.NewSignOption(ctx, "eca97e148cb74e9683d7b7240829d1ff")
	// 使用服务账号 AK
	signOption.AddHeader("encrypted-key", "c0d3690042364b47a8e5b91e3d834b83")

	client := NewClient(&bce.Config{
		Checksum: true,
		Timeout:  30 * time.Second,
		Endpoint: "bcc.gz.baidubce.com",
	})

	client.SetDebug(true)

	// 使用服务账号 SK
	encrpyPassword, err := bccapi.Aes128EncryptUseSecreteKey("fe2e4b7c70234a2c926a159112996820", "xhsjhkj4%w@oip")
	if err != nil {
		t.Errorf("Aes128EncryptUseSecreteKey failed: %s", err)
	}

	createInstanceArgs := &bccapi.CreateInstanceArgs{
		ImageId: "m-AUk5rsP3",
		Billing: bccapi.Billing{
			PaymentTiming: bccapi.PaymentTimingPostPaid,
		},
		InstanceType: bccapi.InstanceTypeN3,

		CpuCount:           2,
		MemoryCapacityInGB: 8,

		RootDiskSizeInGb:    100,
		RootDiskStorageType: bccapi.StorageTypeSSD,

		CreateCdsList: []bccapi.CreateCdsModel{
			{
				CdsSizeInGB: 100,
				StorageType: bccapi.StorageTypeSSD,
			},
		},

		PurchaseCount: 1,

		Name:     "chenhuan-openapi",
		Hostname: "chenhuan-openapi",

		AdminPass: encrpyPassword,

		SubnetId:        "sbn-f0ewipic2zh4",
		SecurityGroupId: "g-850nnihi65yv",

		ClientToken: "client-token-uniq",
	}

	resp, err := client.CreateInstance(ctx, createInstanceArgs, signOption)
	if err != nil {
		t.Errorf("CreateInstance failed: %v", err)
	}

	if respByte, err := json.Marshal(resp); err == nil {
		t.Logf("CreateInstance succeeded: %s", string(respByte))
	}
}

func testDeleteInstance(t *testing.T) {
	bccclient.SetDebug(true)

	ctx := context.TODO()

	err := bccclient.DeleteInstance(ctx, "i-GdqPoKff", nil)
	if err != nil {
		t.Errorf("DeleteInstance failed: %v", err)
	}
}

func testDeleteInstanceWithArgs(t *testing.T) {
	bccclient.SetDebug(true)

	ctx := context.TODO()

	err := bccclient.DeleteInstanceWithArgs(ctx, "i-28LiAxs3", &DeleteInstanceArgs{
		RelatedReleaseFlag:    true,
		DeleteCdsSnapshotFlag: true,
		DeleteRelatedEnisFlag: true,
		BccRecycleFlag:        false,
	}, nil)
	if err != nil {
		t.Errorf("DeleteInstance failed: %v", err)
	}
}

func testBatchDeleteInstance(t *testing.T) {
	bccclient.SetDebug(true)

	ctx := context.TODO()

	err := bccclient.BatchDeleteInstance(ctx, &BatchDeleteInstanceArgs{
		RelatedReleaseFlag:    true,
		DeleteCdsSnapshotFlag: true,
		DeleteRelatedEnisFlag: true,
		BccRecycleFlag:        false,
		InstanceIDs:           []string{"i-YdRvNXrS"},
	}, nil)

	if err != nil {
		t.Errorf("BatchDeleteInstance failed: %v", err)
	}
}

func testGetSecurityGroupOfUser(t *testing.T) {
	ctx := context.TODO()

	stsclient := sts.NewClient(ctx, &bce.Config{
		Endpoint: "sts.bj.iam.sdns.baidu.com:8586/v1",
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   "su",
	}, &bce.Config{
		Endpoint: "iam.bj.bce-internal.baidu.com/v3",
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   "su",
	}, "BceServiceRole_SERVICE_CCE", "cce", "c8QBqhOKxmGu0Sl2p13guMil3U2grbYt")

	client := NewClient(&bce.Config{
		Checksum: true,
		Timeout:  30 * time.Second,
		Endpoint: "bcc.bj.baidubce.com",
	})

	client.SetDebug(true)

	resp, err := client.GetSecurityGroups(ctx, &GetSecurityGroupsRequest{
		VPCID:   "vpc-uhwf3ckgvazu",
		MaxKeys: 10000,
	}, stsclient.NewSignOption(ctx, "6c6093c96f0241c087af184cc5729de8"))
	if err != nil {
		t.Errorf("GetSecurityGroups failed: %v", err)
	}

	if respByte, err := json.Marshal(resp); err == nil {
		t.Logf("GetSecurityGroups succeeded: %s", string(respByte))
	}
}
