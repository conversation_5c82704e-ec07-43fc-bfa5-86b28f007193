package tasks

import (
	"context"
	"errors"
	"net/http"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	grafanasdk "github.com/grafana-tools/sdk"
	"github.com/stretchr/testify/assert"
	cpromv1 "icode.baidu.com/baidu/cprom/cloud-stack/pkg/apis/cprom/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func TestConsoleGrafanaTask_syncDatasource(t *testing.T) {
	var (
		ast = assert.New(t)
		ctx = context.Background()
	)

	// ast.NotNil(NewConsoleGrafanaTask(nil, &ConsoleGrafanaConfig{
	// 	Enable:        true,
	// 	URL:           "http://localhost:3000",
	// 	AdminPassword: "admin",
	// }))

	tenants := []TenantInfo{
		{
			ID:        "name0",
			AccountID: 123,
			ProjectID: 456,
		},
	}
	tenantGetter := func(ctx context.Context, instanceID string) []TenantInfo {
		return tenants
	}

	task := &ConsoleGrafanaTask{
		config: &ConsoleGrafanaConfig{
			Enable:        true,
			URL:           "http://localhost:3000",
			AdminPassword: "admin",
		},
		tenantGetter: tenantGetter,
	}

	patch1 := gomonkey.ApplyFunc(grafanasdk.NewClient, func(apiURL string, apiKeyOrBasicAuth string, client *http.Client) (*grafanasdk.Client, error) {
		return &grafanasdk.Client{}, nil
	})
	defer patch1.Reset()

	patch2 := gomonkey.ApplyFunc((*grafanasdk.Client).GetAllDatasources, func(_ *grafanasdk.Client, ctx context.Context) ([]grafanasdk.Datasource, error) {
		return []grafanasdk.Datasource{
			{
				Name:      "name1",
				Type:      "prometheus",
				Access:    "proxy",
				URL:       "http://localhost:3000",
				IsDefault: false,
			},
		}, nil
	})
	defer patch2.Reset()

	var createErr = errors.New("create datasource error")
	patch3 := gomonkey.ApplyFunc((*grafanasdk.Client).CreateDatasource, func(_ *grafanasdk.Client, ctx context.Context, ds grafanasdk.Datasource) (grafanasdk.StatusMessage, error) {
		return grafanasdk.StatusMessage{}, createErr
	})
	defer patch3.Reset()

	var updateErr = errors.New("update datasource error")
	patch4 := gomonkey.ApplyFunc((*grafanasdk.Client).UpdateDatasource, func(_ *grafanasdk.Client, ctx context.Context, ds grafanasdk.Datasource) (grafanasdk.StatusMessage, error) {
		return grafanasdk.StatusMessage{}, updateErr
	})
	defer patch4.Reset()

	mi := &cpromv1.MonitorInstance{
		ObjectMeta: metav1.ObjectMeta{
			Name: "test",
		},
	}
	err := task.syncDatasource(ctx, mi)
	ast.Equal(err, createErr)

	tenants[0].ID = "name1"
	err = task.syncDatasource(ctx, mi)
	ast.Equal(err, updateErr)

}

func TestTenantInfo_GetTenant(t *testing.T) {
	tests := []struct {
		name     string
		tenant   TenantInfo
		expected string
	}{
		{
			name: "正常租户信息",
			tenant: TenantInfo{
				AccountID: 123,
				ProjectID: 456,
			},
			expected: "123:456",
		},
		{
			name: "零值租户信息",
			tenant: TenantInfo{
				AccountID: 0,
				ProjectID: 0,
			},
			expected: "0:0",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.tenant.GetTenant()
			assert.Equal(t, tt.expected, result)
		})
	}
}
