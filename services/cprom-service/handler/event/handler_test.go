package event

import (
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/usersetting"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/configuration"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/middleware"
	"icode.baidu.com/baidu/cprom/cloud-stack/services/cprom-service/handler"
)

type mockMiddleware struct {
	middleware.Interface
}

type mockUserSetting struct {
	usersetting.Interface
}

func TestNewRegister(t *testing.T) {
	tests := []struct {
		name   string
		config handler.Config
	}{
		{
			name: "success case",
			config: handler.Config{
				Mgr:         nil, // Manager should be created using ctrl.NewManager in actual usage
				Middlew:     &mockMiddleware{},
				Config:      &configuration.ServiceConfig{},
				UserSetting: &mockUserSetting{},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := NewRegister(tt.config)
			assert.NotNil(t, got)
			api, ok := got.(*EventAPI)
			assert.True(t, ok)
			assert.Equal(t, tt.config.Mgr, api.mgr)
			assert.Equal(t, tt.config.Middlew, api.middlew)
			assert.Equal(t, tt.config.Config, api.config)
			assert.Equal(t, tt.config.UserSetting, api.usersettingCli)
		})
	}
}

func TestEventAPI_RegisterAPI(t *testing.T) {
	tests := []struct {
		name        string
		eventAPI    *EventAPI
		setupRouter func() *gin.Engine
		wantRoutes  map[string]bool
	}{
		{
			name: "success case - register all routes",
			eventAPI: &EventAPI{
				mgr:            nil, // Manager should be created using ctrl.NewManager in actual usage
				middlew:        &mockMiddleware{},
				config:         &configuration.ServiceConfig{},
				usersettingCli: &mockUserSetting{},
			},
			setupRouter: func() *gin.Engine {
				return gin.Default()
			},
			wantRoutes: map[string]bool{
				"/api/v1/cprom/events/:ID": true,
				"/api/v1/cprom/events":     true,
				"/api/v1/cprom/claim":      true,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			router := tt.setupRouter()
			tt.eventAPI.RegisterAPI(router)
		})
	}
}
