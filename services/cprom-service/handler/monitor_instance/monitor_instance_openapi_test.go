package monitor_instance

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/configuration"
	"icode.baidu.com/baidu/cprom/cloud-stack/services/cprom-service/handler"
)

// 测试isValidRetention函数
func TestIsValidRetentionFunction(t *testing.T) {
	tests := []struct {
		name      string
		retention string
		expected  bool
	}{
		{
			name:      "valid 15 days",
			retention: "15d",
			expected:  true,
		},
		{
			name:      "valid 30 days",
			retention: "30d",
			expected:  true,
		},
		{
			name:      "valid 90 days",
			retention: "90d",
			expected:  true,
		},
		{
			name:      "valid 365 days",
			retention: "365d",
			expected:  true,
		},
		{
			name:      "invalid 7 days",
			retention: "7d",
			expected:  false,
		},
		{
			name:      "valid 180 days",
			retention: "180d",
			expected:  true,
		},
		{
			name:      "invalid format",
			retention: "30days",
			expected:  false,
		},
		{
			name:      "empty string",
			retention: "",
			expected:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isValidRetention(tt.retention)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestMonitorInstanceAPI_CreateV2_ValidationErrors(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		requestBody    MonitorInstanceRequestArgs
		expectedStatus int
		expectedError  string
	}{
		{
			name: "invalid retention period",
			requestBody: MonitorInstanceRequestArgs{
				InstanceName:    "test-instance",
				RetentionPeriod: "7d", // invalid retention
			},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "MalformedJSON",
		},
		{
			name: "invalid instance type",
			requestBody: MonitorInstanceRequestArgs{
				InstanceName:    "test-instance",
				InstanceType:    "INVALID", // invalid instance type
				RetentionPeriod: "30d",
			},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "InappropriateJSON",
		},
		// 注释掉这个测试，因为它需要 k8s client，会导致 panic
		// {
		// 	name: "valid retention period but will fail at ID generation",
		// 	requestBody: MonitorInstanceRequestArgs{
		// 		InstanceName:    "test-instance",
		// 		RetentionPeriod: "30d", // valid retention
		// 		InstanceType:    "BCM",  // valid instance type
		// 	},
		// 	expectedStatus: http.StatusInternalServerError, // Will fail at ID generation step
		// },
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			// Create request body
			reqBody, _ := json.Marshal(tt.requestBody)
			c.Request = httptest.NewRequest("POST", "/v2/instance", bytes.NewBuffer(reqBody))
			c.Request.Header.Set("Content-Type", "application/json")
			c.Set("accountID", "test-account")
			c.Set("userID", "test-user")
			c.Set(handler.RequestID, "test-request-id")

			// Create API instance
			api := &MonitorInstanceAPI{
				config: &configuration.ServiceConfig{},
			}

			// Execute
			api.CreateV2(c)

			// Assert
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedError != "" {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(t, err)
				assert.Contains(t, response["code"].(string), tt.expectedError)
			}
		})
	}
}

func TestMonitorInstanceAPI_isValidatePwd(t *testing.T) {
	api := &MonitorInstanceAPI{}

	tests := []struct {
		name     string
		password string
		expected bool
	}{
		{
			name:     "valid password",
			password: "Abc123!@",
			expected: true,
		},
		{
			name:     "valid password with all symbols",
			password: "Test123!@#$%^*()",
			expected: true,
		},
		{
			name:     "too short",
			password: "Abc12!",
			expected: false,
		},
		{
			name:     "too long",
			password: "Abc123!@#$%^*()1234567890123456789",
			expected: false,
		},
		{
			name:     "missing letter",
			password: "123456!@",
			expected: false,
		},
		{
			name:     "missing number",
			password: "Abcdef!@",
			expected: false,
		},
		{
			name:     "missing symbol",
			password: "Abc123456",
			expected: false,
		},
		{
			name:     "invalid symbol",
			password: "Abc123&",
			expected: false,
		},
		{
			name:     "empty password",
			password: "",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := api.isValidatePwd(tt.password)
			assert.Equal(t, tt.expected, result)
		})
	}
}
