package monitor_instance

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/configuration"
	"icode.baidu.com/baidu/cprom/cloud-stack/services/cprom-service/handler"
)

func TestMonitorInstanceAPI_Execute(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		requestBody    ExecuteBody
		mockSyncError  error
		expectedStatus int
		expectedResult string
	}{
		{
			name: "successful execution",
			requestBody: ExecuteBody{
				OrderID: "test-order-123",
			},
			mockSyncError:  nil,
			expectedStatus: http.StatusOK,
			expectedResult: "CREATED",
		},
		{
			name: "invalid request body",
			requestBody: ExecuteBody{
				OrderID: "",
			},
			mockSyncError:  nil,
			expectedStatus: http.StatusInternalServerError,
			expectedResult: "CREATING",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			// Create request body
			reqBody, _ := json.Marshal(tt.requestBody)
			c.Request = httptest.NewRequest("POST", "/execute", bytes.NewBuffer(reqBody))
			c.Request.Header.Set("Content-Type", "application/json")
			c.Set(handler.RequestID, "test-request-id")

			// Create API instance
			api := &MonitorInstanceAPI{
				config: &configuration.ServiceConfig{},
			}

			// Mock SynchronizeResStatus
			patches := gomonkey.ApplyMethod(api, "SynchronizeResStatus",
				func(_ *MonitorInstanceAPI, ctx context.Context, accountID string, orderID string) (string, error) {
					if tt.mockSyncError != nil {
						return "", tt.mockSyncError
					}
					return tt.expectedResult, nil
				})
			defer patches.Reset()

			// Execute
			api.Execute(c)

			// Assert
			assert.Equal(t, tt.expectedStatus, w.Code)

			var result handler.OrderFacadeResult
			err := json.Unmarshal(w.Body.Bytes(), &result)
			require.NoError(t, err)
			assert.Equal(t, tt.expectedResult, result.ExecutionStatus)
		})
	}
}

func TestMonitorInstanceAPI_Check(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		orderID        string
		mockSyncResult string
		mockSyncError  error
		expectedStatus int
	}{
		{
			name:           "successful check",
			orderID:        "test-order-123",
			mockSyncResult: "CREATED",
			mockSyncError:  nil,
			expectedStatus: http.StatusOK,
		},
		{
			name:           "sync error",
			orderID:        "test-order-456",
			mockSyncResult: "",
			mockSyncError:  assert.AnError,
			expectedStatus: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = httptest.NewRequest("GET", "/check?orderId="+tt.orderID, nil)
			c.Set(handler.RequestID, "test-request-id")

			// Create API instance
			api := &MonitorInstanceAPI{
				config: &configuration.ServiceConfig{},
			}

			// Mock SynchronizeResStatus
			patches := gomonkey.ApplyMethod(api, "SynchronizeResStatus",
				func(_ *MonitorInstanceAPI, ctx context.Context, accountID string, orderID string) (string, error) {
					return tt.mockSyncResult, tt.mockSyncError
				})
			defer patches.Reset()

			// Execute
			api.Check(c)

			// Assert
			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

func TestMonitorInstanceAPI_CreateManualOrder(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name              string
		instanceID        string
		mockCreateResult  string
		mockCreateCode    int
		mockCreateError   error
		expectedStatus    int
		expectedHasResult bool
	}{
		{
			name:              "successful creation from query param",
			instanceID:        "test-instance-123",
			mockCreateResult:  "CREATED",
			mockCreateCode:    0,
			mockCreateError:   nil,
			expectedStatus:    http.StatusOK,
			expectedHasResult: true,
		},
		{
			name:              "creation error",
			instanceID:        "test-instance-456",
			mockCreateResult:  "",
			mockCreateCode:    http.StatusInternalServerError,
			mockCreateError:   assert.AnError,
			expectedStatus:    http.StatusInternalServerError,
			expectedHasResult: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = httptest.NewRequest("POST", "/manual-order?instanceID="+tt.instanceID, nil)
			c.Set(handler.RequestID, "test-request-id")

			// Create API instance
			api := &MonitorInstanceAPI{
				config: &configuration.ServiceConfig{},
			}

			// Mock CreateOrderByInstance
			patches := gomonkey.ApplyMethod(api, "CreateOrderByInstance",
				func(_ *MonitorInstanceAPI, instanceID string, ctx context.Context) (string, int, error) {
					return tt.mockCreateResult, tt.mockCreateCode, tt.mockCreateError
				})
			defer patches.Reset()

			// Execute
			api.CreateManualOrder(c)

			// Assert
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedHasResult {
				var result handler.Result
				err := json.Unmarshal(w.Body.Bytes(), &result)
				require.NoError(t, err)
				assert.NotNil(t, result.Result)
			}
		})
	}
}

func TestMonitorInstanceAPI_SynchronizeResStatus(t *testing.T) {
	tests := []struct {
		name            string
		accountID       string
		orderID         string
		mockCheckStatus string
		mockCheckID     string
		mockCheckError  error
		mockUpdateError error
		expectedStatus  string
		expectedError   bool
	}{
		{
			name:            "successful sync with found instance",
			accountID:       "test-account",
			orderID:         "test-order-123",
			mockCheckStatus: "CREATED",
			mockCheckID:     "test-instance-123",
			mockCheckError:  nil,
			mockUpdateError: nil,
			expectedStatus:  "CREATED",
			expectedError:   false,
		},
		{
			name:            "instance not found",
			accountID:       "test-account",
			orderID:         "test-order-456",
			mockCheckStatus: "",
			mockCheckID:     "NOT FOUND",
			mockCheckError:  nil,
			mockUpdateError: nil,
			expectedStatus:  "CREATE_FAILED",
			expectedError:   false,
		},
		{
			name:            "check error",
			accountID:       "test-account",
			orderID:         "test-order-789",
			mockCheckStatus: "",
			mockCheckID:     "",
			mockCheckError:  assert.AnError,
			mockUpdateError: nil,
			expectedStatus:  "",
			expectedError:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create API instance
			api := &MonitorInstanceAPI{
				config: &configuration.ServiceConfig{},
			}

			// Mock CheckMIStatus
			patches1 := gomonkey.ApplyMethod(api, "CheckMIStatus",
				func(_ *MonitorInstanceAPI, ctx context.Context, accountID string, orderID string) (string, string, error) {
					return tt.mockCheckStatus, tt.mockCheckID, tt.mockCheckError
				})
			defer patches1.Reset()

			// Mock UpdateOrderStatus
			patches2 := gomonkey.ApplyMethod(api, "UpdateOrderStatus",
				func(_ *MonitorInstanceAPI, ctx context.Context, config *configuration.ServiceConfig, orderID string, resourceInfo ResourceInfo) error {
					return tt.mockUpdateError
				})
			defer patches2.Reset()

			// Execute
			ctx := context.Background()
			result, err := api.SynchronizeResStatus(ctx, tt.accountID, tt.orderID)

			// Assert
			if tt.expectedError {
				assert.Error(t, err)
				assert.Empty(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedStatus, result)
			}
		})
	}
}

func TestGenerateOrderContent(t *testing.T) {
	tests := []struct {
		name            string
		region          string
		retentionPeriod string
		expectedRegion  string
	}{
		{
			name:            "normal region",
			region:          "us-east-1",
			retentionPeriod: "30d",
			expectedRegion:  "us-east-1",
		},
		{
			name:            "gztest region mapping",
			region:          "gztest",
			retentionPeriod: "15d",
			expectedRegion:  "gz",
		},
		{
			name:            "bjtest region mapping",
			region:          "bjtest",
			retentionPeriod: "90d",
			expectedRegion:  "bj",
		},
		{
			name:            "bdtest region mapping",
			region:          "bdtest",
			retentionPeriod: "365d",
			expectedRegion:  "bd",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Execute
			result := GenerateOrderContent(tt.region, tt.retentionPeriod)

			// Assert
			assert.Equal(t, tt.expectedRegion, result.Region)
			assert.Equal(t, "NEW", result.OrderType)
			assert.Len(t, result.Items, 1)

			item := result.Items[0]
			assert.Equal(t, "CPROM", item.ServiceType)
			assert.Equal(t, "postpay", item.ProductType)
			assert.Equal(t, orderKey, item.Key)
			assert.Equal(t, 1, item.Count)
			assert.Equal(t, "day", item.SubProductType)
			assert.Len(t, item.Flavor, 1)

			flavor := item.Flavor[0]
			assert.Equal(t, "retention", flavor.Name)
			assert.Equal(t, tt.retentionPeriod, flavor.Value)
			assert.Equal(t, 1, flavor.Scale)
		})
	}
}

func TestGenerateOrderStatus(t *testing.T) {
	tests := []struct {
		name         string
		orderStatus  string
		instanceID   string
		key          string
		expectedStatus string
		hasResources bool
	}{
		{
			name:         "created status",
			orderStatus:  "CREATED",
			instanceID:   "test-instance-123",
			key:          "cprom",
			expectedStatus: "CREATED",
			hasResources: true,
		},
		{
			name:         "creating status",
			orderStatus:  "CREATING",
			instanceID:   "test-instance-456",
			key:          "cprom",
			expectedStatus: "CREATING",
			hasResources: true,
		},
		{
			name:         "create failed status",
			orderStatus:  "CREATE_FAILED",
			instanceID:   "test-instance-789",
			key:          "cprom",
			expectedStatus: "CREATE_FAILED",
			hasResources: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Execute
			result := GenerateOrderStatus(tt.orderStatus, tt.instanceID, tt.key)

			// Assert
			assert.Equal(t, tt.expectedStatus, result.Status)

			if tt.hasResources {
				assert.Len(t, result.Resources, 1)
				resource := result.Resources[0]
				assert.Equal(t, tt.key, resource.Key)
				assert.Equal(t, tt.instanceID, resource.ID)

				if tt.orderStatus == "CREATED" {
					assert.Equal(t, "RUNNING", resource.Status)
				} else {
					assert.Equal(t, "INIT", resource.Status)
				}
			} else {
				assert.Len(t, result.Resources, 0)
			}
		})
	}
}
