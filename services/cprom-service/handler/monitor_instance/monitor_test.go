package monitor_instance

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestAllowQuery(t *testing.T) {
	tests := []struct {
		name       string
		query      string
		instanceID string
		expected   bool
	}{
		{
			name:       "valid query with instance ID",
			query:      `up{app_kubernetes_io_instance="test-instance-123"}`,
			instanceID: "test-instance-123",
			expected:   true,
		},
		{
			name:       "invalid query without instance ID",
			query:      `up{job="prometheus"}`,
			instanceID: "test-instance-123",
			expected:   false,
		},
		{
			name:       "query with different instance ID",
			query:      `up{app_kubernetes_io_instance="other-instance"}`,
			instanceID: "test-instance-123",
			expected:   false,
		},
		{
			name:       "complex query with correct instance ID",
			query:      `rate(http_requests_total{app_kubernetes_io_instance="my-instance"}[5m])`,
			instanceID: "my-instance",
			expected:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := allowQuery(tt.query, tt.instanceID)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestRangeQueryParams_Validate(t *testing.T) {
	now := time.Now().Unix()
	hour := int64(time.Hour)
	day := hour * 24

	tests := []struct {
		name        string
		params      rangeQueryParams
		instanceID  string
		expectError bool
		expectStep  int64
	}{
		{
			name: "valid query within 7 days",
			params: rangeQueryParams{
				Query: `up{app_kubernetes_io_instance="test"}`,
				Start: float64(now - day),
				End:   float64(now),
				Step:  0, // auto-calculate
			},
			instanceID:  "test",
			expectError: false,
			expectStep:  180, // 1 day duration should get 180s step
		},
		{
			name: "query too old (more than 7 days)",
			params: rangeQueryParams{
				Query: `up{app_kubernetes_io_instance="test"}`,
				Start: float64(now - 8*day),
				End:   float64(now),
				Step:  0,
			},
			instanceID:  "test",
			expectError: true,
		},
		{
			name: "1 hour duration should get 15s step",
			params: rangeQueryParams{
				Query: `up{app_kubernetes_io_instance="test"}`,
				Start: float64(now - hour),
				End:   float64(now),
				Step:  0,
			},
			instanceID:  "test",
			expectError: false,
			expectStep:  15,
		},
		{
			name: "2 hour duration should get 30s step",
			params: rangeQueryParams{
				Query: `up{app_kubernetes_io_instance="test"}`,
				Start: float64(now - 2*hour),
				End:   float64(now),
				Step:  0,
			},
			instanceID:  "test",
			expectError: false,
			expectStep:  30,
		},
		{
			name: "4 hour duration should get 60s step",
			params: rangeQueryParams{
				Query: `up{app_kubernetes_io_instance="test"}`,
				Start: float64(now - 4*hour),
				End:   float64(now),
				Step:  0,
			},
			instanceID:  "test",
			expectError: false,
			expectStep:  60,
		},
		{
			name: "custom step should be preserved",
			params: rangeQueryParams{
				Query: `up{app_kubernetes_io_instance="test"}`,
				Start: float64(now - hour),
				End:   float64(now),
				Step:  300, // custom step
			},
			instanceID:  "test",
			expectError: false,
			expectStep:  300,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.params.validate(tt.instanceID)

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "only support query last 7 day data")
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectStep, tt.params.Step)
			}
		})
	}
}

func TestGetTotalMetricsFunction(t *testing.T) {
	tests := []struct {
		name           string
		mockResponse   string
		mockStatusCode int
		expectedError  bool
		expectedResult Data
	}{
		{
			name: "successful response with data",
			mockResponse: `{
				"status": "success",
				"data": {
					"resultType": "vector",
					"result": [
						{
							"metric": {"__name__": "vm_tenant_inserted_rows_total"},
							"value": [1234567890, "100"]
						}
					]
				}
			}`,
			mockStatusCode: http.StatusOK,
			expectedError:  false,
			expectedResult: Data{
				ResultType: "vector",
				Result: []Result{
					{
						Metric: map[string]string{"__name__": "vm_tenant_inserted_rows_total"},
						Value:  []interface{}{float64(1234567890), "100"},
					},
				},
			},
		},
		{
			name: "empty result",
			mockResponse: `{
				"status": "success",
				"data": {
					"resultType": "vector",
					"result": []
				}
			}`,
			mockStatusCode: http.StatusOK,
			expectedError:  false,
			expectedResult: Data{},
		},
		{
			name:           "invalid JSON response",
			mockResponse:   `invalid json`,
			mockStatusCode: http.StatusOK,
			expectedError:  true,
		},
		{
			name:           "HTTP error response",
			mockResponse:   `{"error": "bad request"}`,
			mockStatusCode: http.StatusBadRequest,
			expectedError:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock HTTP server
			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(tt.mockStatusCode)
				w.Write([]byte(tt.mockResponse))
			}))
			defer server.Close()

			// Execute
			ctx := context.Background()
			result, err := GetTotalMetrics(ctx, server.URL, "test_query", "test_token", "1234567890")

			// Assert
			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				if len(tt.expectedResult.Result) > 0 {
					assert.Equal(t, tt.expectedResult.ResultType, result.ResultType)
					assert.Len(t, result.Result, len(tt.expectedResult.Result))
				}
			}
		})
	}
}

func TestGetQueryMetrics(t *testing.T) {
	tests := []struct {
		name           string
		mockResponse   string
		mockStatusCode int
		expectedError  bool
		expectedResult string
	}{
		{
			name:           "successful response",
			mockResponse:   `{"status": "success", "data": {"result": []}}`,
			mockStatusCode: http.StatusOK,
			expectedError:  false,
			expectedResult: `{"status": "success", "data": {"result": []}}`,
		},
		{
			name:           "HTTP error response",
			mockResponse:   `{"error": "bad request"}`,
			mockStatusCode: http.StatusBadRequest,
			expectedError:  true,
		},
		{
			name:           "max samples exceeded error",
			mockResponse:   `{"error": "cannot select more than -search.maxSamplesPerQuery"}`,
			mockStatusCode: http.StatusOK,
			expectedError:  true,
			expectedResult: "",
		},
		{
			name:           "empty response",
			mockResponse:   "",
			mockStatusCode: http.StatusOK,
			expectedError:  false,
			expectedResult: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock HTTP server
			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				// Verify request parameters
				assert.Equal(t, "GET", r.Method)
				assert.Contains(t, r.URL.RawQuery, "query=test_query")
				assert.Contains(t, r.URL.RawQuery, "time=1234567890")
				assert.Equal(t, "test_token", r.Header.Get("Authorization"))

				w.WriteHeader(tt.mockStatusCode)
				w.Write([]byte(tt.mockResponse))
			}))
			defer server.Close()

			// Execute
			ctx := context.Background()
			result, err := GetQueryMetrics(ctx, server.URL, "test_query", "test_token", "1234567890")

			// Assert
			if tt.expectedError {
				assert.Error(t, err)
				if strings.Contains(tt.mockResponse, "maxSamplesPerQuery") {
					assert.Contains(t, err.Error(), "cannot select more than 1000000000 samples")
				} else if tt.mockStatusCode >= http.StatusBadRequest {
					assert.Contains(t, err.Error(), fmt.Sprintf("http status code %d", tt.mockStatusCode))
				}
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedResult, result)
			}
		})
	}
}
