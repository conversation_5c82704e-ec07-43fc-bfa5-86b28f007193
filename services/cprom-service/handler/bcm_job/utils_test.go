// nolint
package bcm_job

import (
	"context"
	"testing"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	cpromv1 "icode.baidu.com/baidu/cprom/cloud-stack/pkg/apis/cprom/v1"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/configuration"
)

func TestStuffDefaultFields(t *testing.T) {
	tests := []struct {
		name    string
		bcmj    *cpromv1.BCMJob
		cfg     *configuration.ServiceConfig
		wantErr bool
	}{
		{
			name: "nil labels",
			bcmj: &cpromv1.BCMJob{
				Spec: cpromv1.BCMJobSpec{
					AccountID:  "test-account",
					InstanceID: "test-instance",
					BCMJobID:   "test-job",
				},
			},
			cfg: &configuration.ServiceConfig{
				ResourceClusterRegion: "test-region",
				ResourceClusterID:     "test-cluster",
			},
			wantErr: false,
		},
		{
			name: "existing labels",
			bcmj: &cpromv1.BCMJob{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"existing": "label",
					},
				},
				Spec: cpromv1.BCMJobSpec{
					AccountID:  "test-account",
					InstanceID: "test-instance",
					BCMJobID:   "test-job",
				},
			},
			cfg: &configuration.ServiceConfig{
				ResourceClusterRegion: "test-region",
				ResourceClusterID:     "test-cluster",
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := stuffDefaultFields(tt.bcmj, tt.cfg); (err != nil) != tt.wantErr {
				t.Errorf("stuffDefaultFields() error = %v, wantErr %v", err, tt.wantErr)
			}

			if tt.bcmj.Labels == nil {
				t.Error("labels should not be nil")
			}

			if tt.bcmj.Labels[cpromv1.BCEAccountIDLabel] != tt.bcmj.Spec.AccountID {
				t.Errorf("BCEAccountIDLabel not set correctly")
			}

			if tt.bcmj.Labels[cpromv1.InstanceIDLabel] != tt.bcmj.Spec.InstanceID {
				t.Errorf("InstanceIDLabel not set correctly")
			}

			if tt.bcmj.Labels[cpromv1.EnableLabel] != "true" {
				t.Errorf("EnableLabel not set correctly")
			}

			if tt.bcmj.Labels[cpromv1.BCMJobIDLabel] != tt.bcmj.Spec.BCMJobID {
				t.Errorf("BCMJobIDLabel not set correctly")
			}

			if tt.bcmj.Spec.Region != tt.cfg.ResourceClusterRegion {
				t.Errorf("Region not set correctly")
			}

			if tt.bcmj.Spec.ClusterID != tt.cfg.ResourceClusterID {
				t.Errorf("ClusterID not set correctly")
			}
		})
	}
}

func TestValidate(t *testing.T) {
	tests := []struct {
		name string
		bcmj *cpromv1.BCMJob
		want bool
	}{
		{
			name: "all fields present",
			bcmj: &cpromv1.BCMJob{
				Spec: cpromv1.BCMJobSpec{
					AccountID:  "test-account",
					BCMJobID:   "test-job",
					Region:     "test-region",
					InstanceID: "test-instance",
					ClusterID:  "test-cluster",
				},
			},
			want: true,
		},
		{
			name: "missing account id",
			bcmj: &cpromv1.BCMJob{
				Spec: cpromv1.BCMJobSpec{
					BCMJobID:   "test-job",
					Region:     "test-region",
					InstanceID: "test-instance",
					ClusterID:  "test-cluster",
				},
			},
			want: false,
		},
		{
			name: "missing bcm job id",
			bcmj: &cpromv1.BCMJob{
				Spec: cpromv1.BCMJobSpec{
					AccountID:  "test-account",
					Region:     "test-region",
					InstanceID: "test-instance",
					ClusterID:  "test-cluster",
				},
			},
			want: false,
		},
		{
			name: "missing region",
			bcmj: &cpromv1.BCMJob{
				Spec: cpromv1.BCMJobSpec{
					AccountID:  "test-account",
					BCMJobID:   "test-job",
					InstanceID: "test-instance",
					ClusterID:  "test-cluster",
				},
			},
			want: false,
		},
		{
			name: "missing instance id",
			bcmj: &cpromv1.BCMJob{
				Spec: cpromv1.BCMJobSpec{
					AccountID: "test-account",
					BCMJobID:  "test-job",
					Region:    "test-region",
					ClusterID: "test-cluster",
				},
			},
			want: false,
		},
		{
			name: "missing cluster id",
			bcmj: &cpromv1.BCMJob{
				Spec: cpromv1.BCMJobSpec{
					AccountID:  "test-account",
					BCMJobID:   "test-job",
					Region:     "test-region",
					InstanceID: "test-instance",
				},
			},
			want: false,
		},
	}

	api := &BCMJobAPI{}
	ctx := context.Background()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := api.validate(ctx, tt.bcmj); got != tt.want {
				t.Errorf("validate() = %v, want %v", got, tt.want)
			}
		})
	}
}
