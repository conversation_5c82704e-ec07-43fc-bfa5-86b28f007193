package handler

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// setupTestContext 创建测试用的gin.Context
func setupTestContext(method, url, body string, params map[string]string, queries map[string]string) (*gin.Context, *httptest.ResponseRecorder) {
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()

	var req *http.Request
	if body != "" {
		req = httptest.NewRequest(method, url, strings.NewReader(body))
		req.Header.Set("Content-Type", "application/json")
	} else {
		req = httptest.NewRequest(method, url, nil)
	}

	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 设置路径参数
	if params != nil {
		for key, value := range params {
			c.Params = append(c.<PERSON>, gin.Param{Key: key, Value: value})
		}
	}

	// 设置查询参数
	if queries != nil {
		q := req.URL.Query()
		for key, value := range queries {
			q.Set(key, value)
		}
		req.URL.RawQuery = q.Encode()
	}

	return c, w
}

// TestInstanceInQueryResolver 测试instanceInQueryResolver
func TestInstanceInQueryResolver(t *testing.T) {
	resolver := &instanceInQueryResolver{}

	tests := []struct {
		name          string
		queries       map[string]string
		expectedRes   string
		expectedID    string
		expectedError bool
		errorContains string
	}{
		{
			name:          "valid instanceId query parameter",
			queries:       map[string]string{"instanceId": "test-instance-123"},
			expectedRes:   "Cprom_Instance/test-instance-123",
			expectedID:    "test-instance-123",
			expectedError: false,
		},
		{
			name:          "valid instanceID query parameter (uppercase)",
			queries:       map[string]string{"instanceID": "test-instance-456"},
			expectedRes:   "Cprom_Instance/test-instance-456",
			expectedID:    "test-instance-456",
			expectedError: false,
		},
		{
			name:          "both instanceId and instanceID present (instanceId takes priority)",
			queries:       map[string]string{"instanceId": "priority-instance", "instanceID": "secondary-instance"},
			expectedRes:   "Cprom_Instance/priority-instance",
			expectedID:    "priority-instance",
			expectedError: false,
		},
		{
			name:          "missing instance id",
			queries:       map[string]string{},
			expectedRes:   "",
			expectedID:    "",
			expectedError: true,
			errorContains: "invalid instance id",
		},
		{
			name:          "empty instanceId",
			queries:       map[string]string{"instanceId": ""},
			expectedRes:   "",
			expectedID:    "",
			expectedError: true,
			errorContains: "invalid instance id",
		},
		{
			name:          "empty instanceID",
			queries:       map[string]string{"instanceID": ""},
			expectedRes:   "",
			expectedID:    "",
			expectedError: true,
			errorContains: "invalid instance id",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c, _ := setupTestContext("GET", "/test", "", nil, tt.queries)

			resource, instanceID, err := resolver.Resource(c)

			if tt.expectedError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
			}

			assert.Equal(t, tt.expectedRes, resource)
			assert.Equal(t, tt.expectedID, instanceID)
		})
	}
}

// TestStatisticsResourceResolver 测试statisticsResourceResolver
func TestStatisticsResourceResolver(t *testing.T) {
	resolver := &statisticsResourceResolver{}

	tests := []struct {
		name          string
		requestBody   interface{}
		expectedRes   string
		expectedID    string
		expectedError bool
		errorContains string
	}{
		{
			name: "valid request body",
			requestBody: statisticsQueryReqData{
				InstanceID: "test-instance-123",
				StartTime:  "2023-01-01T00:00:00Z",
				EndTime:    "2023-01-02T00:00:00Z",
			},
			expectedRes:   "Cprom_Instance/test-instance-123",
			expectedID:    "test-instance-123",
			expectedError: false,
		},
		{
			name: "empty instance id in body",
			requestBody: statisticsQueryReqData{
				InstanceID: "",
				StartTime:  "2023-01-01T00:00:00Z",
				EndTime:    "2023-01-02T00:00:00Z",
			},
			expectedRes:   "Cprom_Instance/",
			expectedID:    "",
			expectedError: false,
		},
		{
			name:          "invalid json body",
			requestBody:   "invalid-json",
			expectedRes:   "Cprom_Instance/*",
			expectedID:    "",
			expectedError: true,
		},
		{
			name:          "empty body",
			requestBody:   "",
			expectedRes:   "Cprom_Instance/",
			expectedID:    "",
			expectedError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var body string
			if tt.requestBody != "" {
				if data, ok := tt.requestBody.(statisticsQueryReqData); ok {
					bodyBytes, _ := json.Marshal(data)
					body = string(bodyBytes)
				} else {
					body = tt.requestBody.(string)
				}
			}

			c, _ := setupTestContext("POST", "/test", body, nil, nil)

			resource, instanceID, err := resolver.Resource(c)

			if tt.expectedError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
			}

			assert.Equal(t, tt.expectedRes, resource)
			assert.Equal(t, tt.expectedID, instanceID)

			// 验证请求体是否被正确存储在context中
			if body != "" && !tt.expectedError {
				storedBody, exists := c.Get("requestBody")
				assert.True(t, exists)
				assert.Equal(t, []byte(body), storedBody)
			}
		})
	}
}

// TestDefaultResourceResolver 测试defaultResourceResolver
func TestDefaultResourceResolver(t *testing.T) {
	resolver := &defaultResourceResolver{}

	c, _ := setupTestContext("GET", "/test", "", nil, nil)

	resource, instanceID, err := resolver.Resource(c)

	assert.NoError(t, err)
	assert.Equal(t, "Cprom_Instance/*", resource)
	assert.Equal(t, "", instanceID)
}

// TestInstanceResolver 测试instanceResolver
func TestInstanceResolver(t *testing.T) {
	resolver := &instanceResolver{}

	tests := []struct {
		name          string
		params        map[string]string
		queries       map[string]string
		expectedRes   string
		expectedID    string
		expectedError bool
		errorContains string
	}{
		{
			name:          "valid ID parameter",
			params:        map[string]string{"ID": "param-instance-123"},
			expectedRes:   "Cprom_Instance/param-instance-123",
			expectedID:    "param-instance-123",
			expectedError: false,
		},
		{
			name:          "valid instanceID query parameter",
			queries:       map[string]string{"instanceID": "query-instance-456"},
			expectedRes:   "Cprom_Instance/query-instance-456",
			expectedID:    "query-instance-456",
			expectedError: false,
		},
		{
			name:          "valid instanceId query parameter",
			queries:       map[string]string{"instanceId": "query-instance-789"},
			expectedRes:   "Cprom_Instance/query-instance-789",
			expectedID:    "query-instance-789",
			expectedError: false,
		},
		{
			name:          "ID param takes priority over query",
			params:        map[string]string{"ID": "param-priority"},
			queries:       map[string]string{"instanceID": "query-secondary"},
			expectedRes:   "Cprom_Instance/param-priority",
			expectedID:    "param-priority",
			expectedError: false,
		},
		{
			name:          "instanceID query takes priority over instanceId",
			queries:       map[string]string{"instanceID": "instanceID-priority", "instanceId": "instanceId-secondary"},
			expectedRes:   "Cprom_Instance/instanceID-priority",
			expectedID:    "instanceID-priority",
			expectedError: false,
		},
		{
			name:          "missing instance id",
			expectedRes:   "",
			expectedID:    "",
			expectedError: true,
			errorContains: "invalid instance id",
		},
		{
			name:          "empty ID parameter",
			params:        map[string]string{"ID": ""},
			queries:       map[string]string{"instanceID": "fallback-instance"},
			expectedRes:   "Cprom_Instance/fallback-instance",
			expectedID:    "fallback-instance",
			expectedError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c, _ := setupTestContext("GET", "/test", "", tt.params, tt.queries)

			resource, instanceID, err := resolver.Resource(c)

			if tt.expectedError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
			}

			assert.Equal(t, tt.expectedRes, resource)
			assert.Equal(t, tt.expectedID, instanceID)
		})
	}
}

// TestGrafanaResolver 测试grafanaResolver
func TestGrafanaResolver(t *testing.T) {
	resolver := &grafanaResolver{}

	tests := []struct {
		name        string
		params      map[string]string
		queries     map[string]string
		expectedRes string
		expectedID  string
	}{
		{
			name:        "valid ID parameter",
			params:      map[string]string{"ID": "grafana-123"},
			expectedRes: "Cprom_Grafana/grafana-123",
			expectedID:  "grafana-123",
		},
		{
			name:        "valid grafanaId query parameter",
			queries:     map[string]string{"grafanaId": "grafana-456"},
			expectedRes: "Cprom_Grafana/grafana-456",
			expectedID:  "grafana-456",
		},
		{
			name:        "ID param takes priority over query",
			params:      map[string]string{"ID": "param-grafana"},
			queries:     map[string]string{"grafanaId": "query-grafana"},
			expectedRes: "Cprom_Grafana/param-grafana",
			expectedID:  "param-grafana",
		},
		{
			name:        "missing grafana id returns wildcard",
			expectedRes: "Cprom_Grafana/*",
			expectedID:  "",
		},
		{
			name:        "empty ID parameter falls back to query",
			params:      map[string]string{"ID": ""},
			queries:     map[string]string{"grafanaId": "fallback-grafana"},
			expectedRes: "Cprom_Grafana/fallback-grafana",
			expectedID:  "fallback-grafana",
		},
		{
			name:        "empty grafanaId query returns wildcard",
			queries:     map[string]string{"grafanaId": ""},
			expectedRes: "Cprom_Grafana/*",
			expectedID:  "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c, _ := setupTestContext("GET", "/test", "", tt.params, tt.queries)

			resource, grafanaID, err := resolver.Resource(c)

			assert.NoError(t, err)
			assert.Equal(t, tt.expectedRes, resource)
			assert.Equal(t, tt.expectedID, grafanaID)
		})
	}
}

// TestGlobalResolvers 测试全局resolver变量
func TestGlobalResolvers(t *testing.T) {
	tests := []struct {
		name     string
		resolver ResourceResolver
	}{
		{
			name:     "InstanceResolver",
			resolver: InstanceResolver,
		},
		{
			name:     "DefaultResourceResolver",
			resolver: DefaultResourceResolver,
		},
		{
			name:     "StatisticsResourceResolver",
			resolver: StatisticsResourceResolver,
		},
		{
			name:     "GrafanaResolver",
			resolver: GrafanaResolver,
		},
		{
			name:     "InstanceInQueryResolver",
			resolver: InstanceInQueryResolver,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.NotNil(t, tt.resolver)
			// 验证resolver实现了ResourceResolver接口
			assert.Implements(t, (*ResourceResolver)(nil), tt.resolver)
		})
	}
}

// TestConstants 测试常量定义
func TestConstants(t *testing.T) {
	// 测试权限常量
	assert.Equal(t, "READ", Read)
	assert.Equal(t, "OPERATE", Operate)
	assert.Equal(t, "FULL_CONTROL", FullControl)

	// 测试实例操作常量
	assert.Equal(t, "INSTANCE_CREATE", InstanceCreate)
	assert.Equal(t, "INSTANCE_READ", InstanceRead)
	assert.Equal(t, "INSTANCE_UPDATE", InstanceUpdate)
	assert.Equal(t, "INSTANCE_DELETE", InstanceDelete)

	// 测试Grafana操作常量
	assert.Equal(t, "Grafana_CREATE", GrafanaCreate)
	assert.Equal(t, "Grafana_READ", GrafanaRead)
	assert.Equal(t, "Grafana_UPDATE", GrafanaUpdate)
	assert.Equal(t, "Grafana_DELETE", GrafanaDelete)

	// 测试资源类型常量
	assert.Equal(t, "Cprom_Instance", CpromInstance)
	assert.Equal(t, "Cprom_Grafana", CpromGrafana)
	assert.Equal(t, "Statistics", Statistics)
	assert.Equal(t, "Cprom_InstanceInQuery", CpromInstanceInQuery)
	assert.Equal(t, "USE_REQUESTER_ACCOUNTID", DefaultOwner)
}

// TestStatisticsResourceResolver_RequestBodyHandling 测试statisticsResourceResolver的请求体处理
func TestStatisticsResourceResolver_RequestBodyHandling(t *testing.T) {
	resolver := &statisticsResourceResolver{}

	// 测试请求体被正确读取和重置
	requestData := statisticsQueryReqData{
		InstanceID: "test-instance",
		StartTime:  "2023-01-01T00:00:00Z",
		EndTime:    "2023-01-02T00:00:00Z",
	}

	bodyBytes, _ := json.Marshal(requestData)
	c, _ := setupTestContext("POST", "/test", string(bodyBytes), nil, nil)

	// 第一次调用resolver
	resource, instanceID, err := resolver.Resource(c)
	assert.NoError(t, err)
	assert.Equal(t, "Cprom_Instance/test-instance", resource)
	assert.Equal(t, "test-instance", instanceID)

	// 验证请求体被存储在context中
	storedBody, exists := c.Get("requestBody")
	assert.True(t, exists)
	assert.Equal(t, bodyBytes, storedBody)

	// 验证可以从context中获取存储的请求体数据
	var parsedData statisticsQueryReqData
	err = json.Unmarshal(storedBody.([]byte), &parsedData)
	assert.NoError(t, err)
	assert.Equal(t, requestData.InstanceID, parsedData.InstanceID)
	assert.Equal(t, requestData.StartTime, parsedData.StartTime)
	assert.Equal(t, requestData.EndTime, parsedData.EndTime)
}

// TestStatisticsResourceResolver_ErrorCases 测试statisticsResourceResolver的错误情况
func TestStatisticsResourceResolver_ErrorCases(t *testing.T) {
	resolver := &statisticsResourceResolver{}

	tests := []struct {
		name          string
		setupContext  func() *gin.Context
		expectedRes   string
		expectedID    string
		expectedError bool
		errorContains string
	}{
		{
			name: "request body read error",
			setupContext: func() *gin.Context {
				// 创建一个会导致读取错误的context
				c, _ := setupTestContext("POST", "/test", "", nil, nil)
				// 设置一个已经关闭的body来模拟读取错误
				c.Request.Body = &errorReader{}
				return c
			},
			expectedRes:   "Cprom_Instance/*",
			expectedID:    "",
			expectedError: true,
			errorContains: "Failed to read request body",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := tt.setupContext()

			resource, instanceID, err := resolver.Resource(c)

			if tt.expectedError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
			}

			assert.Equal(t, tt.expectedRes, resource)
			assert.Equal(t, tt.expectedID, instanceID)
		})
	}
}

// errorReader 模拟读取错误的Reader
type errorReader struct{}

func (e *errorReader) Read(p []byte) (n int, err error) {
	return 0, bytes.ErrTooLarge
}

func (e *errorReader) Close() error {
	return nil
}

// TestResourceResolver_Interface 测试ResourceResolver接口
func TestResourceResolver_Interface(t *testing.T) {
	// 验证所有resolver都实现了ResourceResolver接口
	resolvers := []ResourceResolver{
		&instanceInQueryResolver{},
		&statisticsResourceResolver{},
		&defaultResourceResolver{},
		&instanceResolver{},
		&grafanaResolver{},
	}

	for i, resolver := range resolvers {
		t.Run(fmt.Sprintf("resolver_%d", i), func(t *testing.T) {
			assert.Implements(t, (*ResourceResolver)(nil), resolver)

			// 测试每个resolver都有Resource方法
			c, _ := setupTestContext("GET", "/test", "", nil, nil)
			_, _, err := resolver.Resource(c)
			// 不检查具体错误，只确保方法可以调用
			_ = err
		})
	}
}

// TestEdgeCases 测试边界情况
func TestEdgeCases(t *testing.T) {
	t.Run("instanceResolver with special characters in ID", func(t *testing.T) {
		resolver := &instanceResolver{}
		specialID := "instance-123_abc-def.test"

		c, _ := setupTestContext("GET", "/test", "", map[string]string{"ID": specialID}, nil)

		resource, instanceID, err := resolver.Resource(c)

		assert.NoError(t, err)
		assert.Equal(t, "Cprom_Instance/"+specialID, resource)
		assert.Equal(t, specialID, instanceID)
	})

	t.Run("grafanaResolver with special characters in ID", func(t *testing.T) {
		resolver := &grafanaResolver{}
		specialID := "grafana-123_abc-def.test"

		c, _ := setupTestContext("GET", "/test", "", map[string]string{"ID": specialID}, nil)

		resource, grafanaID, err := resolver.Resource(c)

		assert.NoError(t, err)
		assert.Equal(t, "Cprom_Grafana/"+specialID, resource)
		assert.Equal(t, specialID, grafanaID)
	})

	t.Run("instanceInQueryResolver with very long instanceId", func(t *testing.T) {
		resolver := &instanceInQueryResolver{}
		longID := strings.Repeat("a", 1000)

		c, _ := setupTestContext("GET", "/test", "", nil, map[string]string{"instanceId": longID})

		resource, instanceID, err := resolver.Resource(c)

		assert.NoError(t, err)
		assert.Equal(t, "Cprom_Instance/"+longID, resource)
		assert.Equal(t, longID, instanceID)
	})
}
