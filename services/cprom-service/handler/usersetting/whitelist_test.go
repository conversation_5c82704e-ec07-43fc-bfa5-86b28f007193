package usersetting

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/usersetting"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/configuration"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/middleware"
	"icode.baidu.com/baidu/cprom/cloud-stack/services/cprom-service/handler"
)

type mockUserSettingClient struct {
	usersetting.Interface
}

type mockMiddleware struct {
	middleware.Interface
}

func TestNewRegister(t *testing.T) {
	tests := []struct {
		name     string
		cfg      handler.Config
		expected *UserSetting
	}{
		{
			name: "successful creation",
			cfg: handler.Config{
				Config: &configuration.ServiceConfig{
					Region: "test-region",
				},
				Middlew:     &mockMiddleware{},
				UserSetting: &mockUserSettingClient{},
			},
			expected: &UserSetting{
				region:  "test-region",
				middlew: &mockMiddleware{},
				cli:     &mockUserSettingClient{},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := NewRegister(tt.cfg)
			assert.Equal(t, tt.expected, got)
		})
	}
}

func TestUserSetting_CheckFeatureACL(t *testing.T) {
	tests := []struct {
		name           string
		setupContext   func(c *gin.Context)
		expectedStatus int
		expectedBody   bool
	}{
		{
			name: "in whitelist",
			setupContext: func(c *gin.Context) {
				c.Set("inWhitelist", true)
			},
			expectedStatus: http.StatusOK,
			expectedBody:   true,
		},
		{
			name: "not in whitelist",
			setupContext: func(c *gin.Context) {
				c.Set("inWhitelist", false)
			},
			expectedStatus: http.StatusOK,
			expectedBody:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			tt.setupContext(c)

			// Test
			us := &UserSetting{}
			us.CheckFeatureACL(c)

			// Assert
			assert.Equal(t, tt.expectedStatus, w.Code)
			var response bool
			err := json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err)
			assert.Equal(t, tt.expectedBody, response)
		})
	}
}

func TestUserSetting_GetQuota(t *testing.T) {
	tests := []struct {
		name           string
		setupContext   func(c *gin.Context)
		expectedStatus int
		expectedBody   map[string]int
	}{
		{
			name: "quota exists",
			setupContext: func(c *gin.Context) {
				c.Set("quota", 10)
			},
			expectedStatus: http.StatusOK,
			expectedBody:   map[string]int{"instance": 10},
		},
		{
			name: "quota not set",
			setupContext: func(c *gin.Context) {
				// do not set quota
			},
			expectedStatus: http.StatusOK,
			expectedBody:   nil, // will panic in actual execution
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			tt.setupContext(c)

			// Test
			us := &UserSetting{}
			if tt.name == "quota not set" {
				assert.Panics(t, func() { us.GetQuota(c) })
				return
			}
			us.GetQuota(c)

			// Assert
			assert.Equal(t, tt.expectedStatus, w.Code)
			var response map[string]int
			err := json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err)
			assert.Equal(t, tt.expectedBody, response)
		})
	}
}

func TestUserSetting_RegisterAPI(t *testing.T) {
	tests := []struct {
		name           string
		path           string
		method         string
		expectedStatus int
	}{
		{
			name:           "check whitelist endpoint",
			path:           "/api/v1/cprom/check_white_list",
			method:         http.MethodGet,
			expectedStatus: http.StatusOK,
		},
		{
			name:           "quota endpoint",
			path:           "/api/v1/cprom/quota",
			method:         http.MethodGet,
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			gin.SetMode(gin.TestMode)
			r := gin.New()
			us := &UserSetting{
				cli:     &mockUserSettingClient{},
				middlew: &mockMiddleware{},
				region:  "test-region",
			}
			us.RegisterAPI(r)

		})
	}
}
