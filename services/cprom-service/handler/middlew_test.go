// nolint
package handler

import (
	"errors"
	"fmt"
	"github.com/agiledragon/gomonkey/v2"
	"net/http"
	"net/http/httptest"
	"net/url"
	"strings"
	"testing"
	"time"

	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	controllerruntime "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	fakeclient "sigs.k8s.io/controller-runtime/pkg/client/fake"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"

	ccrv1alpha1 "icode.baidu.com/baidu/cprom/cloud-stack/pkg/apis/ccr/v1alpha1"
	cpromv1 "icode.baidu.com/baidu/cprom/cloud-stack/pkg/apis/cprom/v1"
	monitoringv1 "icode.baidu.com/baidu/cprom/cloud-stack/pkg/apis/monitoring/v1"
	vmv1beta1 "icode.baidu.com/baidu/cprom/cloud-stack/pkg/apis/vm/v1beta1"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/usersetting"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/usersetting/mock"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/logger"
	gmock "icode.baidu.com/baidu/cprom/cloud-stack/pkg/mock"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/mysql/vm_manage"
	vm_manage_mock "icode.baidu.com/baidu/cprom/cloud-stack/pkg/mysql/vm_manage/mock"
)

var ctx *gin.Context

func TestQuotaGet(t *testing.T) {
	mockController := gomock.NewController(t)

	ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
	ctx.Set(RequestID, "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
	ctx.Set("accountID", "2c0ebe60-ca4be206040f")

	type args struct {
		cli usersetting.Interface
		c   *gin.Context
	}

	tests := []struct {
		name string
		args args
		want int
	}{
		//{
		//	name: "case1: get quota succeed",
		//	args: func() args {
		//		ctl := gomock.NewController(t)
		//		ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
		//		ctx.Set(RequestID, "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
		//		ctx.Set("accountID", "2c0ebe60-ca4be206040f")
		//		mockCli := mock.NewMockInterface(ctl)
		//		mockCli.EXPECT().GetUserQuotas(gomock.Any(), gomock.Any(), gomock.Any()).Return(&usersetting.QuotaResponse{
		//			QuotaType2Quota: map[usersetting.QuotaType]string{
		//				usersetting.QuotaTypeMonitorInstance: "5",
		//			},
		//		}, nil)
		//
		//		return args{
		//			cli: mockCli,
		//			c:   ctx,
		//		}
		//	}(),
		//	want: 5,
		//},
		{
			name: "case1: get quota succeed",
			args: args{
				cli: func() usersetting.Interface {
					mockCli := mock.NewMockInterface(mockController)
					mockCli.EXPECT().GetUserQuotas(gomock.Any(), gomock.Any(), gomock.Any()).Return(&usersetting.QuotaResponse{
						QuotaType2Quota: map[usersetting.QuotaType]string{
							usersetting.QuotaTypeMonitorInstance: "5",
						},
					}, nil)
					return mockCli
				}(),
				c: ctx,
			},
			want: 5,
		},
		{
			name: "case2: get quota failed",
			args: args{
				cli: func() usersetting.Interface {
					mockCli := mock.NewMockInterface(mockController)
					mockCli.EXPECT().GetUserQuotas(gomock.Any(), gomock.Any(), gomock.Any()).Return(&usersetting.QuotaResponse{
						QuotaType2Quota: map[usersetting.QuotaType]string{
							usersetting.QuotaTypeMonitorInstance: "5",
						},
					}, fmt.Errorf("get quota failed"))
					return mockCli
				}(),
				c: ctx,
			},
			want: 5,
		},
		{
			name: "case3: quota convert to num failed",
			args: args{
				cli: func() usersetting.Interface {
					mockCli := mock.NewMockInterface(mockController)
					mockCli.EXPECT().GetUserQuotas(gomock.Any(), gomock.Any(), gomock.Any()).Return(&usersetting.QuotaResponse{
						QuotaType2Quota: map[usersetting.QuotaType]string{
							usersetting.QuotaTypeMonitorInstance: "a",
						},
					}, nil)
					return mockCli
				}(),
				c: ctx,
			},
			want: 0,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fn := QuotaGet(tt.args.cli)
			fn(tt.args.c)

			quota := tt.args.c.MustGet("quota").(int)
			if quota != tt.want {
				t.Errorf("QuotaGet() = %v, want %v", quota, tt.want)
			}
		})
	}
}

func schemeForTests() *runtime.Scheme {
	var scheme = runtime.NewScheme()
	utilruntime.Must(clientgoscheme.AddToScheme(scheme))
	utilruntime.Must(cpromv1.AddToScheme(scheme))
	utilruntime.Must(ccrv1alpha1.AddToScheme(scheme))
	utilruntime.Must(vmv1beta1.AddToScheme(scheme))
	utilruntime.Must(monitoringv1.AddToScheme(scheme))
	return scheme
}

func TestCheckUserQuota(t *testing.T) {
	mockController := gomock.NewController(t)
	ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
	ctx.Set(RequestID, "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
	ctx.Set("accountID", "2c0ebe60-ca4be206040f")
	ctx.Set("quota", 2)

	type args struct {
		c             *gin.Context
		k8sClient     client.Client
		accountManage vm_manage.Interface
	}
	tests := []struct {
		name string
		args args
		want gin.HandlerFunc
	}{
		{
			name: "case1: soft instance",
			args: args{
				c: ctx,
				k8sClient: func() client.Client {
					cli := fakeclient.NewFakeClientWithScheme(schemeForTests(),
						&cpromv1.MonitorInstance{
							ObjectMeta: controllerruntime.ObjectMeta{
								Name:      "monitor-instance-xxx",
								Namespace: "default",
								Labels: map[string]string{
									cpromv1.BCEAccountIDLabel: "2c0ebe60-ca4be206040f",
								},
							},
						},
					)
					return cli
				}(),
				accountManage: func() vm_manage.Interface {
					accountManageMock := vm_manage_mock.NewMockInterface(mockController)
					accountManageMock.EXPECT().QueryByAccountIdAndAvailable(gomock.Any(), gomock.Any()).Return([]*vm_manage.TAccountManage{
						{
							FBceAccount: "2c0ebe60-ca4be206040f",
						},
					}, nil).AnyTimes()
					return accountManageMock
				}(),
			},
			want: nil,
		},
		{
			name: "case1: real instance",
			args: args{
				c: ctx,
				k8sClient: func() client.Client {
					cli := fakeclient.NewFakeClientWithScheme(schemeForTests(),
						&cpromv1.MonitorInstance{
							ObjectMeta: controllerruntime.ObjectMeta{
								Name:      "monitor-instance-xxx",
								Namespace: "default",
								Labels: map[string]string{
									cpromv1.BCEAccountIDLabel: "2c0ebe60-ca4be206040f",
								},
							},
						},
					)
					return cli
				}(),
				accountManage: func() vm_manage.Interface {
					accountManageMock := vm_manage_mock.NewMockInterface(mockController)
					accountManageMock.EXPECT().QueryByAccountIdAndAvailable(gomock.Any(), gomock.Any()).Return([]*vm_manage.TAccountManage{}, nil).AnyTimes()
					return accountManageMock
				}(),
			},
			want: nil,
		},
		{
			name: "case3: query mysql error",
			args: args{
				c: ctx,
				k8sClient: func() client.Client {
					cli := fakeclient.NewFakeClientWithScheme(schemeForTests(),
						&cpromv1.MonitorInstance{},
					)
					return cli
				}(),
				accountManage: func() vm_manage.Interface {
					accountManageMock := vm_manage_mock.NewMockInterface(mockController)
					accountManageMock.EXPECT().QueryByAccountIdAndAvailable(gomock.Any(), gomock.Any()).Return([]*vm_manage.TAccountManage{}, fmt.Errorf("mysql error")).AnyTimes()
					return accountManageMock
				}(),
			},
			want: nil,
		},
		{
			name: "case3: soft instance more than quota",
			args: args{
				c: ctx,
				k8sClient: func() client.Client {
					cli := fakeclient.NewFakeClientWithScheme(schemeForTests(),
						&cpromv1.MonitorInstance{},
					)
					return cli
				}(),
				accountManage: func() vm_manage.Interface {
					accountManageMock := vm_manage_mock.NewMockInterface(mockController)
					accountManageMock.EXPECT().QueryByAccountIdAndAvailable(gomock.Any(), gomock.Any()).Return([]*vm_manage.TAccountManage{
						{
							FBceAccount: "2c0ebe60-ca4be206040f",
						},
						{
							FBceAccount: "2c0ebe60-xxxxxxxxxxxx",
						},
					}, nil).AnyTimes()
					return accountManageMock
				}(),
			},
			want: nil,
		},
		{
			name: "case4: real instance more than quota",
			args: args{
				c: ctx,
				k8sClient: func() client.Client {
					cli := fakeclient.NewFakeClientWithScheme(schemeForTests(),
						&cpromv1.MonitorInstance{
							ObjectMeta: controllerruntime.ObjectMeta{
								Name:      "monitor-instance-111",
								Namespace: "default",
								Labels: map[string]string{
									cpromv1.BCEAccountIDLabel: "2c0ebe60-ca4be206040f",
								},
							},
						},
						&cpromv1.MonitorInstance{
							ObjectMeta: controllerruntime.ObjectMeta{
								Name:      "monitor-instance-222",
								Namespace: "default",
								Labels: map[string]string{
									cpromv1.BCEAccountIDLabel: "2c0ebe60-ca4be206040f",
								},
							},
						},
					)
					return cli
				}(),
				accountManage: func() vm_manage.Interface {
					accountManageMock := vm_manage_mock.NewMockInterface(mockController)
					accountManageMock.EXPECT().QueryByAccountIdAndAvailable(gomock.Any(), gomock.Any()).Return([]*vm_manage.TAccountManage{}, nil).AnyTimes()
					return accountManageMock
				}(),
			},
			want: nil,
		},
		{
			name: "case5: k8s err",
			args: args{
				c: ctx,
				k8sClient: func() client.Client {
					cli := fakeclient.NewFakeClientWithScheme(schemeForTests())
					return cli
				}(),
				accountManage: func() vm_manage.Interface {
					accountManageMock := vm_manage_mock.NewMockInterface(mockController)
					accountManageMock.EXPECT().QueryByAccountIdAndAvailable(gomock.Any(), gomock.Any()).Return([]*vm_manage.TAccountManage{}, nil).AnyTimes()
					return accountManageMock
				}(),
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fn := CheckUserQuota(tt.args.k8sClient, tt.args.accountManage)
			fn(tt.args.c)
		})
	}
}

func TestValidateVMRule(t *testing.T) {

	type fields struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
	}{
		{
			name: "case1: check vmrule succeed",
			fields: func() fields {
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "2c0ebe60-ca4be206040f")
				ctx.Set("instanceID", "2c0ebe60-ca4be206040f")
				vmruleRequestBody := strings.NewReader(`
{"spec":{"groups":[{"name":"xsp-test04081449","rules":[{"record":"mem:sum","expr":"test_xsp_metrics"}]}]}}
`)
				req := httptest.NewRequest("POST", "/", vmruleRequestBody)
				header := http.Header{}
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req
				return fields{
					c: ctx,
				}
			}(),
		},
		{
			name: "case1: bind failed",
			fields: func() fields {
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "2c0ebe60-ca4be206040f")
				ctx.Set("instanceID", "2c0ebe60-ca4be206040f")
				vmruleRequestBody := strings.NewReader(`
()
`)
				req := httptest.NewRequest("POST", "/", vmruleRequestBody)
				header := http.Header{}
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req
				return fields{
					c: ctx,
				}
			}(),
		},
		{
			name: "case1: groups is empty",
			fields: func() fields {
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "2c0ebe60-ca4be206040f")
				ctx.Set("instanceID", "2c0ebe60-ca4be206040f")
				vmruleRequestBody := strings.NewReader(`
{"spec":{"groups":[]}}
`)
				req := httptest.NewRequest("POST", "/", vmruleRequestBody)
				header := http.Header{}
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req
				return fields{
					c: ctx,
				}
			}(),
		},
		{
			name: "case1: group name is empty",
			fields: func() fields {
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "2c0ebe60-ca4be206040f")
				ctx.Set("instanceID", "2c0ebe60-ca4be206040f")
				vmruleRequestBody := strings.NewReader(`
{"spec":{"groups":[{"name":""}]}}
`)
				req := httptest.NewRequest("POST", "/", vmruleRequestBody)
				header := http.Header{}
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req
				return fields{
					c: ctx,
				}
			}(),
		},
		{
			name: "case1: group name is empty",
			fields: func() fields {
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "2c0ebe60-ca4be206040f")
				ctx.Set("instanceID", "2c0ebe60-ca4be206040f")
				vmruleRequestBody := strings.NewReader(`
{"spec":{"groups":[{"name":""}]}}
`)
				req := httptest.NewRequest("POST", "/", vmruleRequestBody)
				header := http.Header{}
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req
				return fields{
					c: ctx,
				}
			}(),
		},
		{
			name: "case1: rules is empty",
			fields: func() fields {
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "2c0ebe60-ca4be206040f")
				ctx.Set("instanceID", "2c0ebe60-ca4be206040f")
				vmruleRequestBody := strings.NewReader(`
{"spec":{"groups":[{"name":"xsp-test04081449","rules":[]}]}}
`)
				req := httptest.NewRequest("POST", "/", vmruleRequestBody)
				header := http.Header{}
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req
				return fields{
					c: ctx,
				}
			}(),
		},
		{
			name: "case1: record of rule is empty",
			fields: func() fields {
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "2c0ebe60-ca4be206040f")
				ctx.Set("instanceID", "2c0ebe60-ca4be206040f")
				vmruleRequestBody := strings.NewReader(`
{"spec":{"groups":[{"name":"xsp-test04081449","rules":[{"record":""}]}]}}
`)
				req := httptest.NewRequest("POST", "/", vmruleRequestBody)
				header := http.Header{}
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req
				return fields{
					c: ctx,
				}
			}(),
		},
		{
			name: "case1: expr of rule is invalid",
			fields: func() fields {
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "2c0ebe60-ca4be206040f")
				ctx.Set("instanceID", "2c0ebe60-ca4be206040f")
				vmruleRequestBody := strings.NewReader(`
{"spec":{"groups":[{"name":"xsp-test04081449","rules":[{"record":"mem:sum","expr":"啊回到家卡仕达"}]}]}}
`)
				req := httptest.NewRequest("POST", "/", vmruleRequestBody)
				header := http.Header{}
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req
				return fields{
					c: ctx,
				}
			}(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fn := ValidateVMRule()
			fn(tt.fields.c)
		})
	}
}

// 测试ValidatePodMonitor函数
func TestValidatePodMonitor(t *testing.T) {
	mockController := gomock.NewController(t)
	ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
	ctx.Request = httptest.NewRequest(http.MethodGet, "/some-url?ID=job-test", nil)
	ctx.Set(RequestID, "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
	ctx.Set("accountID", "2c0ebe60-ca4be206040f")
	ctx.Set("instanceId", "cprom-123")
	ctx.Set("agentId", "agent")

	type args struct {
		c             *gin.Context
		k8sClient     client.Client
		accountManage vm_manage.Interface
	}
	tests := []struct {
		name string
		args args
		want gin.HandlerFunc
	}{
		{
			name: "case1: can find podMonitor",
			args: args{
				c: ctx,
				k8sClient: func() client.Client {
					cli := fakeclient.NewFakeClientWithScheme(schemeForTests(),
						&monitoringv1.PodMonitor{
							ObjectMeta: metav1.ObjectMeta{
								Name:      "job-test-agent",
								Namespace: "cprom-123",
								Labels: map[string]string{
									cpromv1.BCEAccountIDLabel: "2c0ebe60-ca4be206040f",
									cpromv1.InstanceIDLabel:   "cprom-123",
									cpromv1.AgentIDLabel:      "agent",
									"enable":                  "true",
								},
								CreationTimestamp: metav1.Time{
									Time: time.Now(),
								},
							},
						},
					)
					return cli
				}(),
				accountManage: func() vm_manage.Interface {
					accountManageMock := vm_manage_mock.NewMockInterface(mockController)
					accountManageMock.EXPECT().QueryByNewInstanceId(gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
						FNewInstanceId: "cprom-123",
						FNamespace:     "cprom-123",
						FBceAccount:    "2c0ebe60-ca4be206040f",
					}, nil).AnyTimes()
					return accountManageMock
				}(),
			},
			want: nil,
		},
		{
			name: "case2: can not find podMonitor",
			args: args{
				c: ctx,
				k8sClient: func() client.Client {
					cli := fakeclient.NewFakeClientWithScheme(schemeForTests(),
						&monitoringv1.PodMonitor{
							ObjectMeta: metav1.ObjectMeta{
								Name:      "job-test",
								Namespace: "cprom-123",
								Labels: map[string]string{
									cpromv1.BCEAccountIDLabel: "2c0ebe60-ca4be206040f",
									cpromv1.InstanceIDLabel:   "cprom-123",
									cpromv1.AgentIDLabel:      "agent",
									"enable":                  "true",
								},
								CreationTimestamp: metav1.Time{
									Time: time.Now(),
								},
							},
						},
					)
					return cli
				}(),
				accountManage: func() vm_manage.Interface {
					accountManageMock := vm_manage_mock.NewMockInterface(mockController)
					accountManageMock.EXPECT().QueryByNewInstanceId(gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
						FNewInstanceId: "cprom-123",
						FNamespace:     "cprom-123",
						FBceAccount:    "2c0ebe60-ca4be206040f",
					}, nil).AnyTimes()
					return accountManageMock
				}(),
			},
			want: nil,
		},
		{
			name: "case3: sql error",
			args: args{
				c: ctx,
				k8sClient: func() client.Client {
					cli := fakeclient.NewFakeClientWithScheme(schemeForTests(),
						&monitoringv1.PodMonitor{
							ObjectMeta: metav1.ObjectMeta{
								Name:      "job-test",
								Namespace: "cprom-123",
								Labels: map[string]string{
									cpromv1.BCEAccountIDLabel: "2c0ebe60-ca4be206040f",
									cpromv1.InstanceIDLabel:   "cprom-123",
									cpromv1.AgentIDLabel:      "agent",
									"enable":                  "true",
								},
								CreationTimestamp: metav1.Time{
									Time: time.Now(),
								},
							},
						},
					)
					return cli
				}(),
				accountManage: func() vm_manage.Interface {
					accountManageMock := vm_manage_mock.NewMockInterface(mockController)
					accountManageMock.EXPECT().QueryByNewInstanceId(gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
						FNewInstanceId: "cprom",
						FNamespace:     "cprom-123",
						FBceAccount:    "2c0ebe60-ca4be206040f",
					}, gorm.ErrInvalidSQL).AnyTimes()
					return accountManageMock
				}(),
			},
			want: nil,
		},
		{
			name: "case4: v1 instance can find podMonitor",
			args: args{
				c: ctx,
				k8sClient: func() client.Client {
					cli := fakeclient.NewFakeClientWithScheme(schemeForTests(),
						&monitoringv1.PodMonitor{
							ObjectMeta: metav1.ObjectMeta{
								Name:      "job-test-agent",
								Namespace: "cprom-123",
								Labels: map[string]string{
									cpromv1.BCEAccountIDLabel: "2c0ebe60-ca4be206040f",
									cpromv1.InstanceIDLabel:   "cprom-123",
									cpromv1.AgentIDLabel:      "agent",
									"enable":                  "true",
								},
								CreationTimestamp: metav1.Time{
									Time: time.Now(),
								},
							},
						},
					)
					return cli
				}(),
				accountManage: func() vm_manage.Interface {
					accountManageMock := vm_manage_mock.NewMockInterface(mockController)
					accountManageMock.EXPECT().QueryByNewInstanceId(gomock.Any(), gomock.Any()).Return(nil, gorm.ErrRecordNotFound).AnyTimes()
					return accountManageMock
				}(),
			},
			want: nil,
		},
		{
			name: "case5: v1 instance accountid not match",
			args: args{
				c: ctx,
				k8sClient: func() client.Client {
					cli := fakeclient.NewFakeClientWithScheme(schemeForTests(),
						&monitoringv1.PodMonitor{
							ObjectMeta: metav1.ObjectMeta{
								Name:      "job-test-agent",
								Namespace: "cprom-123",
								Labels: map[string]string{
									cpromv1.BCEAccountIDLabel: "2c0ebe60",
									cpromv1.InstanceIDLabel:   "cprom-123",
									cpromv1.AgentIDLabel:      "agent",
									"enable":                  "true",
								},
								CreationTimestamp: metav1.Time{
									Time: time.Now(),
								},
							},
						},
					)
					return cli
				}(),
				accountManage: func() vm_manage.Interface {
					accountManageMock := vm_manage_mock.NewMockInterface(mockController)
					accountManageMock.EXPECT().QueryByNewInstanceId(gomock.Any(), gomock.Any()).Return(nil, gorm.ErrRecordNotFound).AnyTimes()
					return accountManageMock
				}(),
			},
			want: nil,
		},
		{
			name: "case6: v1 instance agentid not match",
			args: args{
				c: ctx,
				k8sClient: func() client.Client {
					cli := fakeclient.NewFakeClientWithScheme(schemeForTests(),
						&monitoringv1.PodMonitor{
							ObjectMeta: metav1.ObjectMeta{
								Name:      "job-test-agent",
								Namespace: "cprom-123",
								Labels: map[string]string{
									cpromv1.BCEAccountIDLabel: "2c0ebe60-ca4be206040f",
									cpromv1.InstanceIDLabel:   "cprom-123",
									cpromv1.AgentIDLabel:      "agent2",
									"enable":                  "true",
								},
								CreationTimestamp: metav1.Time{
									Time: time.Now(),
								},
							},
						},
					)
					return cli
				}(),
				accountManage: func() vm_manage.Interface {
					accountManageMock := vm_manage_mock.NewMockInterface(mockController)
					accountManageMock.EXPECT().QueryByNewInstanceId(gomock.Any(), gomock.Any()).Return(nil, gorm.ErrRecordNotFound).AnyTimes()
					return accountManageMock
				}(),
			},
			want: nil,
		},
		{
			name: "case7: v1 instance can not find podMonitor",
			args: args{
				c: ctx,
				k8sClient: func() client.Client {
					cli := fakeclient.NewFakeClientWithScheme(schemeForTests(),
						&monitoringv1.PodMonitor{
							ObjectMeta: metav1.ObjectMeta{
								Name:      "job-test",
								Namespace: "cprom-123",
								Labels: map[string]string{
									cpromv1.BCEAccountIDLabel: "2c0ebe60-ca4be206040f",
									cpromv1.InstanceIDLabel:   "cprom-123",
									cpromv1.AgentIDLabel:      "agent",
									"enable":                  "true",
								},
								CreationTimestamp: metav1.Time{
									Time: time.Now(),
								},
							},
						},
					)
					return cli
				}(),
				accountManage: func() vm_manage.Interface {
					accountManageMock := vm_manage_mock.NewMockInterface(mockController)
					accountManageMock.EXPECT().QueryByNewInstanceId(gomock.Any(), gomock.Any()).Return(nil, gorm.ErrRecordNotFound).AnyTimes()
					return accountManageMock
				}(),
			},
			want: nil,
		},
		{
			name: "case8: can find podMonitor,AccountID not match",
			args: args{
				c: ctx,
				k8sClient: func() client.Client {
					cli := fakeclient.NewFakeClientWithScheme(schemeForTests(),
						&monitoringv1.PodMonitor{
							ObjectMeta: metav1.ObjectMeta{
								Name:      "job-test-agent",
								Namespace: "cprom-123",
								Labels: map[string]string{
									cpromv1.BCEAccountIDLabel: "2c0ebe60-ca4be206040f",
									cpromv1.InstanceIDLabel:   "cprom-123",
									cpromv1.AgentIDLabel:      "agent",
									"enable":                  "true",
								},
								CreationTimestamp: metav1.Time{
									Time: time.Now(),
								},
							},
						},
					)
					return cli
				}(),
				accountManage: func() vm_manage.Interface {
					accountManageMock := vm_manage_mock.NewMockInterface(mockController)
					accountManageMock.EXPECT().QueryByNewInstanceId(gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
						FNewInstanceId: "cprom-123",
						FNamespace:     "cprom-123",
						FBceAccount:    "2c0ebe60",
					}, nil).AnyTimes()
					return accountManageMock
				}(),
			},
			want: nil,
		},
		{
			name: "case9: can not find podMonitor,k8s error",
			args: args{
				c: ctx,
				k8sClient: func() client.Client {
					cli := fakeclient.NewFakeClientWithScheme(schemeForTests())
					return cli
				}(),
				accountManage: func() vm_manage.Interface {
					accountManageMock := vm_manage_mock.NewMockInterface(mockController)
					accountManageMock.EXPECT().QueryByNewInstanceId(gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
						FNewInstanceId: "cprom-123",
						FNamespace:     "cprom-123",
						FBceAccount:    "2c0ebe60-ca4be206040f",
					}, nil).AnyTimes()
					return accountManageMock
				}(),
			},
			want: nil,
		},
		{
			name: "case10: v1 instance can not find podMonitor,k8s error",
			args: args{
				c: ctx,
				k8sClient: func() client.Client {
					cli := fakeclient.NewFakeClientWithScheme(schemeForTests())
					return cli
				}(),
				accountManage: func() vm_manage.Interface {
					accountManageMock := vm_manage_mock.NewMockInterface(mockController)
					accountManageMock.EXPECT().QueryByNewInstanceId(gomock.Any(), gomock.Any()).Return(nil, gorm.ErrRecordNotFound).AnyTimes()
					return accountManageMock
				}(),
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if strings.Contains(tt.name, "case9") || strings.Contains(tt.name, "case10") {
				mockController = gomock.NewController(t)
				mockClient := gmock.NewMockClient(mockController)
				mockClient.EXPECT().Get(
					gomock.Any(), gomock.Any(), gomock.Any(), // 匹配 PodMonitor 类型
				).Return(errors.New("internal server error"))
				tt.args.k8sClient = mockClient
			}
			fn := ValidatePodMonitor(tt.args.k8sClient, tt.args.accountManage)
			fn(tt.args.c)
		})
	}
}

func TestAuthClient_permissionTable(t *testing.T) {
	ac := &AuthClient{}
	table := ac.permissionTable()
	if table == nil {
		t.Error("permissionTable should not be nil")
	}
}

func TestMiddlew(t *testing.T) {
	mockController := gomock.NewController(t)
	defer mockController.Finish()

	tests := []struct {
		name             string
		setupContext     func() *gin.Context
		mockDBError      error
		mockK8sError     error
		expectJSONCalled bool
		expectStatusCode int
		expectAborted    bool
	}{
		{
			name: "database error case",
			setupContext: func() *gin.Context {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = &http.Request{
					URL: &url.URL{
						RawQuery: "instanceID=test-instance-123",
					},
				}
				c.Set("x-bce-request-id", "123")
				c.Set("accountID", "123")
				c.Set("_test_recorder", w) // 保存recorder用于测试验证
				return c
			},
			mockDBError:      errors.New("test database error"),
			mockK8sError:     nil,
			expectJSONCalled: true,
			expectStatusCode: 500,
			expectAborted:    true,
		},
		{
			name: "database success but k8s not found error case",
			setupContext: func() *gin.Context {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = &http.Request{
					URL: &url.URL{
						RawQuery: "instanceID=test-instance-123",
					},
				}
				c.Set("x-bce-request-id", "123")
				c.Set("accountID", "123")
				c.Set("_test_recorder", w) // 保存recorder用于测试验证
				return c
			},
			mockDBError:      nil,
			mockK8sError:     apierrors.NewNotFound(schema.GroupResource{Group: "cprom.io", Resource: "monitorinstances"}, "test-namespace"),
			expectJSONCalled: true,
			expectStatusCode: 404,
			expectAborted:    true,
		},
		{
			name: "database success but k8s internal error case",
			setupContext: func() *gin.Context {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = &http.Request{
					URL: &url.URL{
						RawQuery: "instanceID=test-instance-123",
					},
				}
				c.Set("x-bce-request-id", "123")
				c.Set("accountID", "123")
				c.Set("_test_recorder", w) // 保存recorder用于测试验证
				return c
			},
			mockDBError:      nil,
			mockK8sError:     errors.New("internal server error"),
			expectJSONCalled: true,
			expectStatusCode: 500,
			expectAborted:    true,
		},
		{
			name: "all success case",
			setupContext: func() *gin.Context {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = &http.Request{
					URL: &url.URL{
						RawQuery: "instanceID=test-instance-123",
					},
				}
				c.Set("x-bce-request-id", "123")
				c.Set("accountID", "123")
				c.Set("_test_recorder", w) // 保存recorder用于测试验证
				return c
			},
			mockDBError:      nil,
			mockK8sError:     nil,
			expectJSONCalled: false,
			expectStatusCode: 200,
			expectAborted:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := tt.setupContext()

			// 创建mock k8s client
			mockClient := gmock.NewMockClient(mockController)

			// 如果数据库调用成功，需要mock k8s client的Get调用
			if tt.mockDBError == nil {
				mockClient.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(tt.mockK8sError).AnyTimes()
			}

			// Mock数据库调用
			patch := gomonkey.ApplyFunc((*vm_manage.TAccountManage).Get, func(account *vm_manage.TAccountManage, session *gorm.DB, newInstanceId string) (err error) {
				if tt.mockDBError == nil {
					// 设置mock数据，模拟成功的数据库查询
					account.FNewInstanceId = "test-instance-123"
					account.FNamespace = "test-namespace"
					account.FBceAccount = "123"
				}
				return tt.mockDBError
			})
			defer patch.Reset()

			// 执行测试
			ValidateInstance(mockClient)(c)

			// 获取recorder来验证HTTP响应
			w := c.MustGet("_test_recorder").(*httptest.ResponseRecorder)
			actualStatusCode := w.Code

			// 验证结果
			if tt.expectJSONCalled {
				// 如果期望调用JSON，检查状态码是否正确设置
				if actualStatusCode != tt.expectStatusCode {
					t.Errorf("Expected status code: %d, got: %d", tt.expectStatusCode, actualStatusCode)
				}
			} else {
				// 如果不期望调用JSON，状态码应该是200（默认值）
				if actualStatusCode != 200 {
					t.Errorf("Expected status code: 200 (no JSON call), got: %d", actualStatusCode)
				}
			}

			// 验证是否被中止（通过检查IsAborted方法）
			if c.IsAborted() != tt.expectAborted {
				t.Errorf("Expected abort: %v, got: %v", tt.expectAborted, c.IsAborted())
			}

			// 验证context中的值是否正确设置（仅在成功情况下）
			if !tt.expectAborted && tt.mockDBError == nil && tt.mockK8sError == nil {
				if _, exists := c.Get("instanceID"); !exists {
					t.Error("Expected instanceID to be set in context")
				}
				if _, exists := c.Get("newInstanceID"); !exists {
					t.Error("Expected newInstanceID to be set in context")
				}
			}
		})
	}
}

func Test_ValidateXSS(t *testing.T) {
	ast := assert.New(t)
	c, _ := gin.CreateTestContext(httptest.NewRecorder())
	c.Request = &http.Request{
		URL: &url.URL{
			RawQuery: "name=<script>alert(1)</script>",
		},
	}

	ValidateXSS()(c)
	ast.Equal(400, c.Writer.Status())
}

func TestNewContext(t *testing.T) {
	tests := []struct {
		name        string
		setup       func() *gin.Context
		wantReqID   string
		expectError bool
	}{
		{
			name: "case1: context with request id",
			setup: func() *gin.Context {
				ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set(RequestID, "test-request-id")
				return ctx
			},
			wantReqID:   "test-request-id",
			expectError: false,
		},
		{
			name: "case2: context without request id",
			setup: func() *gin.Context {
				ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
				return ctx
			},
			wantReqID:   "",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := tt.setup()
			resultCtx := NewContext(ctx)

			reqID, ok := resultCtx.Value(logger.RequestID).(string)
			if !ok && tt.wantReqID != "" {
				t.Errorf("NewContext() failed to get request id from context")
			}

			if reqID != tt.wantReqID && tt.wantReqID != "" {
				t.Errorf("NewContext() = %v, want %v", reqID, tt.wantReqID)
			}
		})
	}
}
