// nolint
package alerting_rule

import (
	"net/http/httptest"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/gin-gonic/gin"
	"github.com/jinzhu/gorm"
	cpromv1 "icode.baidu.com/baidu/cprom/cloud-stack/pkg/apis/cprom/v1"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/mysql"
)

func init() {
	arApi = &AlertingRuleAPI{}
	c, _ = gin.CreateTestContext(httptest.NewRecorder())
}

func TestValidateNotifyRule(t *testing.T) {
	// mock一个*sql.DB对象，不需要连接真实的数据库
	db, _, err := sqlmock.New()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer db.Close()

	mysql.Conn, err = gorm.Open("mysql", db)

	arApi.validateNotifyRule(c, "accountId", "notifyRuleId")
}

func TestDeleteBuiltInLabelKeys(t *testing.T) {
	tests := []struct {
		name     string
		input    map[string]string
		expected map[string]string
	}{
		{
			name: "remove all built-in labels",
			input: map[string]string{
				cpromv1.AccountIdLabel:         "123",
				cpromv1.MonitorInstanceIdLabel: "456",
				cpromv1.AlertSourceIDLabel:     "789",
				"custom_label":                 "value",
			},
			expected: map[string]string{
				"custom_label": "value",
			},
		},
		{
			name: "remove some built-in labels",
			input: map[string]string{
				cpromv1.AccountIdLabel:         "123",
				cpromv1.MonitorInstanceIdLabel: "456",
				"custom_label1":                "value1",
				"custom_label2":                "value2",
			},
			expected: map[string]string{
				"custom_label1": "value1",
				"custom_label2": "value2",
			},
		},
		{
			name: "no built-in labels",
			input: map[string]string{
				"custom_label1": "value1",
				"custom_label2": "value2",
			},
			expected: map[string]string{
				"custom_label1": "value1",
				"custom_label2": "value2",
			},
		},
		{
			name:     "empty labels",
			input:    map[string]string{},
			expected: map[string]string{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			DeleteBuiltInLabelKeys(&tt.input)
			if len(tt.input) != len(tt.expected) {
				t.Errorf("expected length %d, got %d", len(tt.expected), len(tt.input))
			}
			for k, v := range tt.expected {
				if tt.input[k] != v {
					t.Errorf("expected %s=%s, got %s=%s", k, v, k, tt.input[k])
				}
			}
			for k := range tt.input {
				if _, ok := tt.expected[k]; !ok {
					t.Errorf("unexpected key %s in result", k)
				}
			}
		})
	}
}
