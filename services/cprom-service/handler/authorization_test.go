// nolint
package handler

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/middleware"
)

type mockMiddleware1 struct {
	middleware.Interface
	verifyResult bool
	verifyErr    error
}

func (m *mockMiddleware1) VerifyPermission(ctx context.Context, r *http.Request, resource string, instanceID string, perms []string, accountID string, userID string, isTagged bool) (bool, error) {
	return m.verifyResult, m.verifyErr
}

type mockMiddleware struct {
	middleware.Interface
}

func TestGetResolver(t *testing.T) {
	tests := []struct {
		name         string
		resourceType string
		expected     ResourceResolver
	}{
		{
			name:         "CpromInstance",
			resourceType: CpromInstance,
			expected:     InstanceResolver,
		},
		{
			name:         "CpromGrafana",
			resourceType: CpromGrafana,
			expected:     GrafanaResolver,
		},
		{
			name:         "CpromInstanceInQuery",
			resourceType: CpromInstanceInQuery,
			expected:     InstanceInQueryResolver,
		},
		{
			name:         "Statistics",
			resourceType: Statistics,
			expected:     StatisticsResourceResolver,
		},
		{
			name:         "UnknownType",
			resourceType: "UnknownType",
			expected:     DefaultResourceResolver,
		},
	}

	ac := &AuthClient{
		mw:     &mockMiddleware{},
		region: "test-region",
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resolver := ac.getResolver(tt.resourceType)
			assert.Equal(t, tt.expected, resolver)
		})
	}
}

func TestAuthorization(t *testing.T) {
	tests := []struct {
		name           string
		method         string
		path           string
		mockVerify     bool
		mockErr        error
		expectedResult bool
	}{
		{
			name:           "NoPermissionPath",
			method:         "GET",
			path:           "/unknown/path",
			mockVerify:     false,
			mockErr:        nil,
			expectedResult: true,
		},
		{
			name:           "HasPermission",
			method:         "GET",
			path:           "/api/v1/cprom/alert_packages",
			mockVerify:     true,
			mockErr:        nil,
			expectedResult: true,
		},
		{
			name:           "NoPermission",
			method:         "GET",
			path:           "/api/v1/cprom/alert_packages",
			mockVerify:     false,
			mockErr:        nil,
			expectedResult: true,
		},
		{
			name:           "VerifyError",
			method:         "GET",
			path:           "/api/v1/cprom/alert_packages",
			mockVerify:     false,
			mockErr:        assert.AnError,
			expectedResult: true,
		},
		{
			name:           "InstancePermission",
			method:         "GET",
			path:           "/api/v1/cprom/instances/123",
			mockVerify:     true,
			mockErr:        nil,
			expectedResult: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gin.SetMode(gin.TestMode)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = httptest.NewRequest(tt.method, tt.path, nil)
			c.Params = gin.Params{}

			mockMW := &mockMiddleware1{
				verifyResult: tt.mockVerify,
				verifyErr:    tt.mockErr,
			}

			ac := &AuthClient{
				mw:     mockMW,
				region: "test-region",
			}

			result := ac.Authorization(c, context.Background(), "test-account", "test-user")
			assert.Equal(t, tt.expectedResult, result)
		})
	}
}
