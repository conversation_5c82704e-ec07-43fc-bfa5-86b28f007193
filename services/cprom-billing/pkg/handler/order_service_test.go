// nolint
package handler

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/cprom/cloud-stack/services/cprom-billing/pkg/models"
)

func TestImportOrder(t *testing.T) {
	// Setup test server
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		assert.Equal(t, "/orders/realTimeImport", r.URL.Path)
		assert.Equal(t, "POST", r.Method)
		assert.NotEmpty(t, r.Header.Get("Authorization"))
		assert.Equal(t, "application/json", r.Header.Get("Content-Type"))
		assert.NotEmpty(t, r.Header.Get("x-bce-request-id"))

		var req models.ImportOrderRequest
		err := json.NewDecoder(r.Body).Decode(&req)
		assert.NoError(t, err)
		assert.NotEmpty(t, req.Items)

		response := models.ImportOrderResponse{
			ImportedOrderUUIDList: []string{"order1", "order2"},
		}
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}))
	defer ts.Close()

	// Prepare test data
	client := NewOrderClient("test_ak", "test_sk", ts.URL)
	ctx := context.Background()
	instanceInfos := []models.InstanceInfo{
		{
			InstanceID: "inst1",
		},
		{
			InstanceID: "inst2",
		},
	}

	// Execute test
	code, err := client.ImportOrder(ctx, instanceInfos)

	// Verify results
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, code)
	assert.Equal(t, "order1", instanceInfos[0].OrderID)
	assert.Equal(t, "order2", instanceInfos[1].OrderID)
}

func TestImportOrder_ErrorResponse(t *testing.T) {
	// Setup test server with error response
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusBadRequest)
		w.Write([]byte(`{"error": "invalid request"}`))
	}))
	defer ts.Close()

	// Prepare test data
	client := NewOrderClient("test_ak", "test_sk", ts.URL)
	ctx := context.Background()
	instanceInfos := []models.InstanceInfo{
		{
			InstanceID: "inst1",
		},
	}

	// Execute test
	code, err := client.ImportOrder(ctx, instanceInfos)

	// Verify results
	assert.Error(t, err)
	assert.Equal(t, http.StatusBadRequest, code)
	assert.Contains(t, err.Error(), "invalid request")
}

func TestImportOrder_MarshalError(t *testing.T) {
	// Setup test server (should not be called)
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		t.Fatal("should not reach here")
	}))
	defer ts.Close()

	// Prepare test data with invalid instance info
	client := NewOrderClient("test_ak", "test_sk", ts.URL)
	ctx := context.Background()
	instanceInfos := []models.InstanceInfo{
		{
			CreateTime: time.Now(),
		},
	}

	// Execute test
	code, err := client.ImportOrder(ctx, instanceInfos)

	// Verify results
	assert.Error(t, err)
	assert.Equal(t, -1, code)
	assert.Equal(t, assert.AnError, err)
}

func TestImportOrder_UnmarshalError(t *testing.T) {
	// Setup test server with invalid response
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`invalid json`))
	}))
	defer ts.Close()

	// Prepare test data
	client := NewOrderClient("test_ak", "test_sk", ts.URL)
	ctx := context.Background()
	instanceInfos := []models.InstanceInfo{
		{
			InstanceID: "inst1",
		},
	}

	// Execute test
	code, err := client.ImportOrder(ctx, instanceInfos)

	// Verify results
	assert.Error(t, err)
	assert.Equal(t, -1, code)
}
